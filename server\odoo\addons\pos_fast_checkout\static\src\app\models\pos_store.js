/** @odoo-module */

import { PosStore } from "@point_of_sale/app/store/pos_store";
import { patch } from "@web/core/utils/patch";

patch(PosStore.prototype, {
    /**
     * Override phương thức tạo đơn hàng mới để áp dụng các cài đặt mặc định
     */
    add_new_order() {
        const order = super.add_new_order();
        
        // Chỉ áp dụng nếu Fast Checkout được kích hoạt
        if (!this.config?.fast_checkout_enabled) {
            return order;
        }
        
        // Áp dụng khách hàng mặc định
        this._applyDefaultCustomer(order);
        
        return order;
    },

    /**
     * Áp dụng khách hàng mặc định cho đơn hàng
     */
    _applyDefaultCustomer(order) {
        if (!order || !this.config?.auto_apply_default_customer || !this.config?.default_customer_id) {
            return;
        }

        try {
            const defaultCustomer = this.models["res.partner"].get(this.config.default_customer_id);
            if (defaultCustomer) {
                order.set_partner(defaultCustomer);
                console.log('Fast Checkout: Đã áp dụng khách hàng mặc định:', defaultCustomer.name);
            }
        } catch (error) {
            console.warn('Fast Checkout: Không thể áp dụng khách hàng mặc định:', error);
        }
    },

    /**
     * Lấy phương thức thanh toán mặc định
     */
    getDefaultPaymentMethod() {
        if (!this.config?.fast_checkout_enabled || !this.config?.default_payment_method_id) {
            return null;
        }

        try {
            // Kiểm tra models có sẵn không
            if (!this.models || !this.models["pos.payment.method"]) {
                console.warn('Fast Checkout: pos.payment.method model not available');
                return null;
            }

            const defaultPaymentMethod = this.models["pos.payment.method"].get(this.config.default_payment_method_id);
            if (defaultPaymentMethod && this.config.payment_method_ids && this.config.payment_method_ids.includes(defaultPaymentMethod.id)) {
                return defaultPaymentMethod;
            }
        } catch (error) {
            console.warn('Fast Checkout: Không thể lấy phương thức thanh toán mặc định:', error);
        }

        return null;
    },

    /**
     * Kiểm tra xem Fast Checkout có được kích hoạt không
     */
    isFastCheckoutEnabled() {
        return this.config?.fast_checkout_enabled || false;
    },

    /**
     * Kiểm tra xem phím Space có được kích hoạt không
     */
    isSpaceKeyCheckoutEnabled() {
        return this.config?.fast_checkout_enabled && this.config?.space_key_checkout;
    },

    /**
     * Kiểm tra xem in tự động có được kích hoạt không
     */
    isAutoPrintEnabled() {
        return this.config?.fast_checkout_enabled && this.config?.auto_print_receipt_fast;
    },

    /**
     * Override printReceipt để tối ưu hóa cho Fast Checkout
     */
    async printReceipt(options = {}) {
        if (this.isAutoPrintEnabled() && !options.skipFastPrint) {
            // In nhanh mà không hiển thị dialog
            try {
                const result = await super.printReceipt({
                    ...options,
                    basic: false,
                    order: options.order || this.get_order()
                });
                console.log('Fast Checkout: Đã in hóa đơn tự động');
                return result;
            } catch (error) {
                console.warn('Fast Checkout: Lỗi khi in hóa đơn tự động:', error);
                // Fallback to normal printing
                return super.printReceipt(options);
            }
        }
        
        return super.printReceipt(options);
    }
});

/** @odoo-module */

import { PaymentScreen } from "@point_of_sale/app/screens/payment_screen/payment_screen";
import { patch } from "@web/core/utils/patch";
import { useHotkey } from "@web/core/hotkeys/hotkey_hook";
import { onMounted, onWillUnmount } from "@odoo/owl";

patch(PaymentScreen.prototype, {
    setup() {
        super.setup();

        // Thiết lập phím tắt Space cho Fast Checkout
        if (this.pos?.isSpaceKeyCheckoutEnabled && this.pos.isSpaceKeyCheckoutEnabled()) {
            useHotkey("space", this._onSpaceKeyPressed.bind(this), {
                allowRepeat: false,
                bypassEditableProtection: true,
                isAvailable: () => {
                    // Chỉ kích hoạt khi đang ở PaymentScreen và Fast Checkout enabled
                    return this._isSpaceKeyAvailable();
                }
            });
        }

        onMounted(() => {
            // Delay để đảm bảo component đã được khởi tạo đầy đủ
            setTimeout(() => {
                this._onPaymentScreenMounted();
            }, 100);
        });

        onWillUnmount(() => {
            this._onPaymentScreenUnmounted();
        });
    },

    /**
     * Xử lý khi màn hình thanh toán được mount
     */
    _onPaymentScreenMounted() {
        if (!this.pos?.isFastCheckoutEnabled()) {
            return;
        }

        // Kiểm tra currentOrder có sẵn không
        if (!this.currentOrder) {
            console.warn('Fast Checkout: currentOrder not available in onMounted');
            return;
        }

        // Tự động thêm phương thức thanh toán mặc định
        this._autoApplyDefaultPayment();

        // Focus vào input amount nếu có payment line
        this._focusPaymentInput();
    },

    /**
     * Xử lý khi màn hình thanh toán bị unmount
     */
    _onPaymentScreenUnmounted() {
        // Cleanup nếu cần
    },

    /**
     * Kiểm tra xem phím Space có khả dụng không
     */
    _isSpaceKeyAvailable() {
        try {
            // Kiểm tra Fast Checkout enabled
            if (!this.pos?.isSpaceKeyCheckoutEnabled || !this.pos.isSpaceKeyCheckoutEnabled()) {
                return false;
            }

            // Kiểm tra có currentOrder
            if (!this.currentOrder) {
                return false;
            }

            // Kiểm tra đang ở PaymentScreen (component này đang active)
            if (!this.el || !this.el.isConnected) {
                return false;
            }

            return true;
        } catch (error) {
            console.warn('Fast Checkout: Error checking space key availability:', error);
            return false;
        }
    },

    /**
     * Xử lý phím Space được nhấn
     * @param {Object} context - Hotkey context object từ useHotkey: { area, target }
     */
    async _onSpaceKeyPressed(context) {
        // Double check availability (isAvailable đã check nhưng để chắc chắn)
        if (!this._isSpaceKeyAvailable()) {
            console.log('Fast Checkout: Space key not available');
            return;
        }

        console.log('Fast Checkout: Space key pressed', context);

        // Ngăn chặn hành vi mặc định nếu có target element
        try {
            if (context?.target && typeof context.target.blur === 'function') {
                // Blur target element để tránh xung đột với input fields
                context.target.blur();
            }
        } catch (error) {
            console.warn('Fast Checkout: Could not blur target element:', error);
        }

        // Kiểm tra xem có thể validate order không
        if (this._canFastValidate()) {
            console.log('Fast Checkout: Thực hiện thanh toán nhanh với phím Space');
            await this._performFastValidation();
        } else {
            console.log('Fast Checkout: Không thể thực hiện thanh toán nhanh');
            this._showFastCheckoutHint();
        }
    },

    /**
     * Tự động áp dụng phương thức thanh toán mặc định
     */
    _autoApplyDefaultPayment() {
        // Kiểm tra các điều kiện cần thiết
        if (!this.pos?.config?.auto_apply_default_payment) {
            return;
        }

        if (!this.currentOrder) {
            console.warn('Fast Checkout: currentOrder not available for auto payment');
            return;
        }

        // Kiểm tra paymentlines có tồn tại và là array
        if (!this.currentOrder.paymentlines || !Array.isArray(this.currentOrder.paymentlines)) {
            console.warn('Fast Checkout: paymentlines not available or not an array');
            return;
        }

        // Chỉ thêm nếu chưa có payment line nào
        if (this.currentOrder.paymentlines.length > 0) {
            return;
        }

        const defaultPaymentMethod = this.pos.getDefaultPaymentMethod();
        if (defaultPaymentMethod) {
            try {
                this.addNewPaymentLine(defaultPaymentMethod);
                console.log('Fast Checkout: Đã thêm phương thức thanh toán mặc định:', defaultPaymentMethod.name);

                // Tự động set amount bằng due amount
                const paymentLine = this.currentOrder.selected_paymentline;
                if (paymentLine && this.currentOrder.get_due && this.currentOrder.get_due() > 0) {
                    paymentLine.set_amount(this.currentOrder.get_due());
                }
            } catch (error) {
                console.warn('Fast Checkout: Lỗi khi thêm phương thức thanh toán mặc định:', error);
            }
        }
    },

    /**
     * Focus vào input payment amount
     */
    _focusPaymentInput() {
        setTimeout(() => {
            const amountInput = this.el?.querySelector('.payment-input-number input');
            if (amountInput) {
                amountInput.focus();
                amountInput.select();
            }
        }, 100);
    },

    /**
     * Kiểm tra xem có thể thực hiện fast validation không
     */
    _canFastValidate() {
        if (!this.currentOrder) {
            return false;
        }

        // Kiểm tra có sản phẩm trong đơn hàng
        if (!this.currentOrder.get_orderlines || this.currentOrder.get_orderlines().length === 0) {
            return false;
        }

        // Kiểm tra đã thanh toán đủ
        if (!this.currentOrder.get_due || this.currentOrder.get_due() > 0.01) {
            return false;
        }

        // Kiểm tra có payment line
        if (!this.currentOrder.paymentlines || !Array.isArray(this.currentOrder.paymentlines) || this.currentOrder.paymentlines.length === 0) {
            return false;
        }

        return true;
    },

    /**
     * Thực hiện fast validation
     */
    async _performFastValidation() {
        try {
            // Validate order với fast mode
            await this.validateOrder(true);
        } catch (error) {
            console.error('Fast Checkout: Lỗi khi validate order:', error);
            this.env.services.notification.add(
                'Có lỗi xảy ra khi thanh toán. Vui lòng thử lại.',
                { type: 'danger' }
            );
        }
    },

    /**
     * Hiển thị gợi ý Fast Checkout
     */
    _showFastCheckoutHint() {
        let message = 'Không thể thanh toán nhanh. ';

        if (!this.currentOrder) {
            message += 'Đơn hàng không khả dụng.';
        } else if (!this.currentOrder.get_orderlines || this.currentOrder.get_orderlines().length === 0) {
            message += 'Vui lòng thêm sản phẩm vào đơn hàng.';
        } else if (!this.currentOrder.get_due || this.currentOrder.get_due() > 0.01) {
            const dueAmount = this.currentOrder.get_due ? this.currentOrder.get_due() : 0;
            message += `Còn thiếu ${this.env.utils.formatCurrency(dueAmount)}.`;
        } else if (!this.currentOrder.paymentlines || !Array.isArray(this.currentOrder.paymentlines) || this.currentOrder.paymentlines.length === 0) {
            message += 'Vui lòng chọn phương thức thanh toán.';
        }

        this.env.services.notification.add(message, { type: 'warning' });
    },

    /**
     * Override validateOrder để tối ưu cho Fast Checkout
     */
    async validateOrder(isForceValidate) {
        // Nếu là Fast Checkout và skip confirmation
        if (this.pos.isFastCheckoutEnabled() && this.pos.config?.skip_payment_confirmation && !isForceValidate) {
            isForceValidate = true;
        }
        
        const result = await super.validateOrder(isForceValidate);
        return result;
    },

    /**
     * Override afterOrderValidation để xử lý in tự động
     */
    async afterOrderValidation() {
        if (this.pos.isAutoPrintEnabled()) {
            // In tự động và chuyển về ProductScreen ngay lập tức
            try {
                // Set config để in tự động và skip screen
                const originalPrintAuto = this.pos.config.iface_print_auto;
                const originalSkipScreen = this.pos.config.iface_print_skip_screen;

                this.pos.config.iface_print_auto = true;
                this.pos.config.iface_print_skip_screen = true;

                // Gọi parent method với config đã được set
                await super.afterOrderValidation();

                // Restore original config
                this.pos.config.iface_print_auto = originalPrintAuto;
                this.pos.config.iface_print_skip_screen = originalSkipScreen;

                return;
            } catch (error) {
                console.warn('Fast Checkout: Lỗi khi in tự động, fallback to normal flow:', error);
            }
        }

        // Fallback to normal flow
        await super.afterOrderValidation();
    },

    /**
     * Override addNewPaymentLine để tối ưu cho Fast Checkout
     */
    addNewPaymentLine(paymentMethod) {
        const result = super.addNewPaymentLine(paymentMethod);
        
        // Nếu là Fast Checkout, tự động focus vào input
        if (this.pos.isFastCheckoutEnabled()) {
            this._focusPaymentInput();
        }
        
        return result;
    }
});

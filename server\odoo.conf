[options]
addons_path = c:\program files\odoo 18.0.20250519\server\odoo\addons
admin_passwd = $pbkdf2-sha512$600000$MwagNOacU.o9Z.x9jxFi7A$82uza3GikQ.NNf.1kCHPxxm1KQZZn.azDeq7Zt7fSAvL9LE8P4wbmhebEX8Cux2Phhq5JAzuikhhhhoJjDUHfA
bin_path = C:\Program Files\Odoo 18.0.20250519\thirdparty
csv_internal_sep = ,
data_dir = C:\Program Files\Odoo 18.0.20250519\sessions
db_host = localhost
db_maxconn = 64
db_maxconn_gevent = False
db_name = False
db_password = openpgpwd
db_port = 5432
db_replica_host = False
db_replica_port = False
db_sslmode = prefer
db_template = template0
db_user = openpg
dbfilter = 
default_productivity_apps = True
email_from = False
from_filter = False
geoip_city_db = c:\usr\share\geoip\geolite2-city.mmdb
geoip_country_db = c:\usr\share\geoip\geolite2-country.mmdb
gevent_port = 8072
http_enable = True
http_interface = 
http_port = 8069
import_partial = 
limit_memory_hard = None
limit_memory_hard_gevent = None
limit_memory_soft = None
limit_memory_soft_gevent = None
limit_request = None
limit_time_cpu = None
limit_time_real = None
limit_time_real_cron = None
limit_time_worker_cron = 0
list_db = True
log_db = False
log_db_level = warning
log_handler = :INFO
log_level = info
logfile = C:\Program Files\Odoo 18.0.20250519\server\odoo.log
max_cron_threads = 2
osv_memory_count_limit = 0
pg_path = C:\Program Files\Odoo 18.0.20250519\PostgreSQL\bin
pidfile = 
pre_upgrade_scripts = 
proxy_mode = True
reportgz = False
screencasts = 
screenshots = c:\users\<USER>\appdata\local\temp\odoo_tests
server_wide_modules = web, base, rest_api_odoo
smtp_password = False
smtp_port = 25
smtp_server = localhost
smtp_ssl = False
smtp_ssl_certificate_filename = False
smtp_ssl_private_key_filename = False
smtp_user = False
syslog = False
test_enable = False
test_file = 
test_tags = None
transient_age_limit = 1.0
translate_modules = ['all']
unaccent = False
upgrade_path = 
websocket_keep_alive_timeout = 3600
websocket_rate_limit_burst = 10
websocket_rate_limit_delay = 0.2
without_demo = False
workers = None
x_sendfile = False


# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class TikTokShopConfig(models.Model):
    _name = 'tiktok.shop.config'
    _description = 'Cấu hình TikTok Shop'
    _rec_name = 'shop_name'

    # Thông tin cơ bản
    shop_name = fields.Char(
        string='Tên Shop',
        required=True,
        help='Tên của shop TikTok để dễ nhận biết'
    )
    shop_id = fields.Char(
        string='Shop ID',
        required=True,
        help='ID của shop trên TikTok'
    )
    
    # Thông tin API
    app_key = fields.Char(
        string='App Key',
        required=True,
        help='App Key từ TikTok Developer Portal'
    )
    app_secret = fields.Char(
        string='App Secret',
        required=True,
        help='App Secret từ TikTok Developer Portal'
    )
    access_token = fields.Char(
        string='Access Token',
        help='Access Token để truy cập API'
    )
    
    # <PERSON><PERSON><PERSON> hình môi trường
    is_sandbox = fields.<PERSON><PERSON>an(
        string='Môi trường Sandbox',
        default=True,
        help='Sử dụng môi trường test hay production'
    )
    
    # Trạng thái kết nối
    is_active = fields.Boolean(
        string='Kích hoạt',
        default=True,
        help='Bật/tắt kết nối với shop này'
    )
    connection_status = fields.Selection([
        ('not_connected', 'Chưa kết nối'),
        ('connected', 'Đã kết nối'),
        ('error', 'Lỗi kết nối'),
    ], string='Trạng thái kết nối', default='not_connected', readonly=True)
    
    # Cấu hình đồng bộ
    auto_sync_orders = fields.Boolean(
        string='Tự động đồng bộ đơn hàng',
        default=True,
        help='Tự động đồng bộ đơn hàng theo lịch'
    )
    
    # Thống kê
    total_orders_synced = fields.Integer(
        string='Tổng đơn hàng đã đồng bộ',
        readonly=True,
        default=0
    )
    
    @api.constrains('app_key', 'app_secret')
    def _check_api_credentials(self):
        for record in self:
            if not record.app_key or not record.app_secret:
                raise ValidationError(_('App Key và App Secret là bắt buộc!'))
    
    def action_test_connection(self):
        """Test kết nối với TikTok Shop API"""
        self.ensure_one()
        # Mock test connection
        self.connection_status = 'connected'
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Thành công!'),
                'message': _('Kết nối TikTok Shop thành công!'),
                'type': 'success',
            }
        }

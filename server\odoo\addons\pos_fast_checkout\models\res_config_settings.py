# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import models, fields


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'
    
    # Fast Checkout Settings
    pos_fast_checkout_enabled = fields.Boolean(
        related='pos_config_id.fast_checkout_enabled',
        readonly=False,
        string='Kích hoạt Fast Checkout'
    )
    
    pos_space_key_checkout = fields.Boolean(
        related='pos_config_id.space_key_checkout',
        readonly=False,
        string='Phím Space để thanh toán'
    )
    
    pos_auto_print_receipt_fast = fields.Boolean(
        related='pos_config_id.auto_print_receipt_fast',
        readonly=False,
        string='In hóa đơn tự động (Fast)'
    )
    
    pos_default_payment_method_id = fields.Many2one(
        related='pos_config_id.default_payment_method_id',
        readonly=False,
        string='<PERSON><PERSON><PERSON><PERSON> thức thanh toán mặc định'
    )
    
    pos_default_customer_id = fields.Many2one(
        related='pos_config_id.default_customer_id',
        readonly=False,
        string='Khách hàng mặc định'
    )
    
    pos_auto_apply_default_payment = fields.Boolean(
        related='pos_config_id.auto_apply_default_payment',
        readonly=False,
        string='Tự động áp dụng thanh toán mặc định'
    )
    
    pos_auto_apply_default_customer = fields.Boolean(
        related='pos_config_id.auto_apply_default_customer',
        readonly=False,
        string='Tự động áp dụng khách hàng mặc định'
    )
    
    pos_skip_payment_confirmation = fields.Boolean(
        related='pos_config_id.skip_payment_confirmation',
        readonly=False,
        string='Bỏ qua xác nhận thanh toán'
    )

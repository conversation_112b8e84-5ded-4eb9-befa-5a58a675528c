<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- <PERSON><PERSON>h TikTok Shop -->
    <menuitem id="menu_tiktok_shop_root" 
              name="TikTok Shop" 
              sequence="50"/>

    <!-- <PERSON><PERSON> c<PERSON>u hình -->
    <menuitem id="menu_tiktok_shop_config" 
              name="Cấu hình" 
              parent="menu_tiktok_shop_root" 
              sequence="10"
              action="action_tiktok_shop_config"/>

    <!-- Action cho TikTok Shop Config -->
    <record id="action_tiktok_shop_config" model="ir.actions.act_window">
        <field name="name">Cấu hình TikTok Shop</field>
        <field name="res_model">tiktok.shop.config</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                T<PERSON><PERSON> c<PERSON><PERSON> hình TikTok Shop đầu tiên!
            </p>
            <p>
                Cấu hình kết nối với TikTok Shop để đồng bộ đơn hàng và sản phẩm.
            </p>
        </field>
    </record>

    <!-- Form view cho TikTok Shop Config -->
    <record id="view_tiktok_shop_config_form" model="ir.ui.view">
        <field name="name">tiktok.shop.config.form</field>
        <field name="model">tiktok.shop.config</field>
        <field name="arch" type="xml">
            <form string="Cấu hình TikTok Shop">
                <header>
                    <button name="action_test_connection" type="object" string="Kiểm tra kết nối" 
                            class="btn-primary" icon="fa-plug"/>
                    <field name="connection_status" widget="statusbar" 
                           statusbar_visible="not_connected,connected,error"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <label for="shop_name"/>
                        <h1>
                            <field name="shop_name" placeholder="Tên shop TikTok..."/>
                        </h1>
                    </div>
                    
                    <group>
                        <group string="Thông tin cơ bản">
                            <field name="shop_id"/>
                            <field name="is_active"/>
                        </group>
                        <group string="Cấu hình API">
                            <field name="is_sandbox"/>
                            <field name="app_key" password="True"/>
                            <field name="app_secret" password="True"/>
                            <field name="access_token" password="True"/>
                        </group>
                    </group>
                    
                    <group>
                        <group string="Cấu hình đồng bộ">
                            <field name="auto_sync_orders"/>
                        </group>
                        <group string="Thống kê">
                            <field name="total_orders_synced" readonly="1"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Tree view cho TikTok Shop Config -->
    <record id="view_tiktok_shop_config_tree" model="ir.ui.view">
        <field name="name">tiktok.shop.config.tree</field>
        <field name="model">tiktok.shop.config</field>
        <field name="arch" type="xml">
            <tree string="Cấu hình TikTok Shop">
                <field name="shop_name"/>
                <field name="shop_id"/>
                <field name="connection_status" widget="badge" 
                       decoration-success="connection_status == 'connected'"
                       decoration-danger="connection_status == 'error'"
                       decoration-muted="connection_status == 'not_connected'"/>
                <field name="is_active" widget="boolean_toggle"/>
                <field name="total_orders_synced"/>
            </tree>
        </field>
    </record>
</odoo>

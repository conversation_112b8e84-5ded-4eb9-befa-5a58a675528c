# GeoIP Fix Module

## Mô tả
Module này fix lỗi GeoIP AttributeError trong Odoo 18:
- `AttributeError: GeoIP object has no attribute 'country_name'`
- `AttributeError: GeoIP object has no attribute 'country_code'`

## Nguyên nhân lỗi
Lỗi xảy ra do:
1. GeoIP database không được cấu hình đúng
2. Sự không nhất quán trong API GeoIP giữa các phiên bản
3. Thiếu xử lý exception khi truy cập thuộc tính GeoIP

## Giải pháp

### 1. Cài đặt module fix
```bash
# Module đã được tạo tại: server/odoo/addons/geoip_fix/
# Cài đặt qua Odoo Apps interface
```

### 2. Hoặc tắt GeoIP (Giải pháp nhanh)
Thêm vào file config `odoo.conf`:
```ini
# Tắt GeoIP để tránh lỗi
geoip_city_db = 
geoip_country_db = 
```

### 3. Hoặc cấu hình GeoIP đúng cách
```bash
# Tải GeoIP database
wget https://download.maxmind.com/app/geoip_download?edition_id=GeoLite2-City&license_key=YOUR_LICENSE_KEY&suffix=tar.gz
wget https://download.maxmind.com/app/geoip_download?edition_id=GeoLite2-Country&license_key=YOUR_LICENSE_KEY&suffix=tar.gz

# Giải nén và copy vào thư mục phù hợp
# Cập nhật đường dẫn trong odoo.conf
```

## Tác động của lỗi
- Không thể đăng nhập website
- Lỗi 500 khi truy cập trang chủ
- Authentication failures
- Device logging errors

## Sau khi fix
- Website hoạt động bình thường
- Không còn lỗi GeoIP trong log
- Authentication process ổn định
- Device logging hoạt động đúng

## Lưu ý
- Module này chỉ fix lỗi, không cải thiện tính năng GeoIP
- Nếu cần GeoIP đầy đủ, hãy cấu hình database GeoIP đúng cách
- Module tương thích với Odoo 18.0

# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo.tests import tagged
from odoo.addons.point_of_sale.tests.common import TestPoSCommon


@tagged('post_install', '-at_install')
class TestPosFastCheckout(TestPoSCommon):

    def setUp(self):
        super().setUp()
        
        # Tạo payment method cho test
        self.cash_payment_method = self.env['pos.payment.method'].create({
            'name': 'Cash Test',
            'receivable_account_id': self.company_data['default_account_receivable'].id,
            'journal_id': self.company_data['default_journal_cash'].id,
        })
        
        # Tạo customer cho test
        self.test_customer = self.env['res.partner'].create({
            'name': 'Test Customer',
            'is_company': False,
        })

    def test_pos_config_fast_checkout_fields(self):
        """Test các field Fast Checkout trong pos.config"""
        
        # Cập nhật config với Fast Checkout
        self.main_pos_config.write({
            'fast_checkout_enabled': True,
            'space_key_checkout': True,
            'auto_print_receipt_fast': True,
            'default_payment_method_id': self.cash_payment_method.id,
            'default_customer_id': self.test_customer.id,
            'auto_apply_default_payment': True,
            'auto_apply_default_customer': True,
            'skip_payment_confirmation': False,
            'payment_method_ids': [(4, self.cash_payment_method.id)],
        })
        
        # Kiểm tra các field đã được set đúng
        self.assertTrue(self.main_pos_config.fast_checkout_enabled)
        self.assertTrue(self.main_pos_config.space_key_checkout)
        self.assertTrue(self.main_pos_config.auto_print_receipt_fast)
        self.assertEqual(self.main_pos_config.default_payment_method_id, self.cash_payment_method)
        self.assertEqual(self.main_pos_config.default_customer_id, self.test_customer)
        self.assertTrue(self.main_pos_config.auto_apply_default_payment)
        self.assertTrue(self.main_pos_config.auto_apply_default_customer)

    def test_pos_config_onchange_fast_checkout(self):
        """Test onchange khi tắt Fast Checkout"""
        
        # Bật tất cả tính năng
        self.main_pos_config.write({
            'fast_checkout_enabled': True,
            'space_key_checkout': True,
            'auto_print_receipt_fast': True,
            'auto_apply_default_payment': True,
            'auto_apply_default_customer': True,
            'skip_payment_confirmation': True,
        })
        
        # Tắt Fast Checkout
        self.main_pos_config.fast_checkout_enabled = False
        self.main_pos_config._onchange_fast_checkout_enabled()
        
        # Kiểm tra tất cả tính năng đã bị tắt
        self.assertFalse(self.main_pos_config.space_key_checkout)
        self.assertFalse(self.main_pos_config.auto_print_receipt_fast)
        self.assertFalse(self.main_pos_config.auto_apply_default_payment)
        self.assertFalse(self.main_pos_config.auto_apply_default_customer)
        self.assertFalse(self.main_pos_config.skip_payment_confirmation)

    def test_get_default_payment_method(self):
        """Test method get_default_payment_method"""
        
        # Thêm payment method vào config
        self.main_pos_config.write({
            'payment_method_ids': [(4, self.cash_payment_method.id)],
            'default_payment_method_id': self.cash_payment_method.id,
        })
        
        # Test method
        result = self.main_pos_config.get_default_payment_method()
        self.assertEqual(result, self.cash_payment_method)
        
        # Test khi payment method không có trong danh sách
        other_payment_method = self.env['pos.payment.method'].create({
            'name': 'Other Payment',
            'receivable_account_id': self.company_data['default_account_receivable'].id,
            'journal_id': self.company_data['default_journal_cash'].id,
        })
        
        self.main_pos_config.default_payment_method_id = other_payment_method.id
        result = self.main_pos_config.get_default_payment_method()
        self.assertFalse(result)

    def test_get_default_customer(self):
        """Test method get_default_customer"""
        
        self.main_pos_config.default_customer_id = self.test_customer.id
        result = self.main_pos_config.get_default_customer()
        self.assertEqual(result, self.test_customer)
        
        # Test khi không có default customer
        self.main_pos_config.default_customer_id = False
        result = self.main_pos_config.get_default_customer()
        self.assertFalse(result)

    def test_res_config_settings_fields(self):
        """Test các field trong res.config.settings"""
        
        settings = self.env['res.config.settings'].create({
            'pos_config_id': self.main_pos_config.id,
            'pos_fast_checkout_enabled': True,
            'pos_space_key_checkout': True,
            'pos_auto_print_receipt_fast': True,
            'pos_default_payment_method_id': self.cash_payment_method.id,
            'pos_default_customer_id': self.test_customer.id,
            'pos_auto_apply_default_payment': True,
            'pos_auto_apply_default_customer': True,
            'pos_skip_payment_confirmation': False,
        })
        
        # Execute để apply settings
        settings.execute()
        
        # Kiểm tra settings đã được apply vào config
        self.assertTrue(self.main_pos_config.fast_checkout_enabled)
        self.assertTrue(self.main_pos_config.space_key_checkout)
        self.assertTrue(self.main_pos_config.auto_print_receipt_fast)
        self.assertEqual(self.main_pos_config.default_payment_method_id, self.cash_payment_method)
        self.assertEqual(self.main_pos_config.default_customer_id, self.test_customer)

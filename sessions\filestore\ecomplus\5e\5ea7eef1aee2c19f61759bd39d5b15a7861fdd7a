
/* /im_livechat/static/src/embed/common/scss/bootstrap_overridden.scss */
 

/* /web/static/lib/bootstrap/scss/_functions.scss */
 

/* /web/static/lib/bootstrap/scss/_mixins.scss */
 

/* /web/static/src/scss/functions.scss */
 

/* /web/static/src/scss/mixins_forwardport.scss */
 

/* /web/static/src/scss/bs_mixins_overrides.scss */
 .bg-dark-light h1, .o_colored_level .bg-dark-light h1, .bg-light-light h1, .o_colored_level .bg-light-light h1, .bg-danger-light h1, .o_colored_level .bg-danger-light h1, .bg-warning-light h1, .o_colored_level .bg-warning-light h1, .bg-info-light h1, .o_colored_level .bg-info-light h1, .bg-success-light h1, .o_colored_level .bg-success-light h1, .bg-secondary-light h1, .o_colored_level .bg-secondary-light h1, .bg-primary-light h1, .o_colored_level .bg-primary-light h1, .bg-dark-light .h1, .o_colored_level .bg-dark-light .h1, .bg-light-light .h1, .o_colored_level .bg-light-light .h1, .bg-danger-light .h1, .o_colored_level .bg-danger-light .h1, .bg-warning-light .h1, .o_colored_level .bg-warning-light .h1, .bg-info-light .h1, .bg-success-light .h1, .bg-secondary-light .h1, .bg-primary-light .h1, .bg-dark-light h2, .o_colored_level .bg-dark-light h2, .bg-light-light h2, .o_colored_level .bg-light-light h2, .bg-danger-light h2, .o_colored_level .bg-danger-light h2, .bg-warning-light h2, .o_colored_level .bg-warning-light h2, .bg-info-light h2, .o_colored_level .bg-info-light h2, .bg-success-light h2, .o_colored_level .bg-success-light h2, .bg-secondary-light h2, .o_colored_level .bg-secondary-light h2, .bg-primary-light h2, .o_colored_level .bg-primary-light h2, .bg-dark-light .h2, .o_colored_level .bg-dark-light .h2, .bg-light-light .h2, .o_colored_level .bg-light-light .h2, .bg-danger-light .h2, .o_colored_level .bg-danger-light .h2, .bg-warning-light .h2, .o_colored_level .bg-warning-light .h2, .bg-info-light .h2, .bg-success-light .h2, .bg-secondary-light .h2, .bg-primary-light .h2, .bg-dark-light h3, .o_colored_level .bg-dark-light h3, .bg-light-light h3, .o_colored_level .bg-light-light h3, .bg-danger-light h3, .o_colored_level .bg-danger-light h3, .bg-warning-light h3, .o_colored_level .bg-warning-light h3, .bg-info-light h3, .o_colored_level .bg-info-light h3, .bg-success-light h3, .o_colored_level .bg-success-light h3, .bg-secondary-light h3, .o_colored_level .bg-secondary-light h3, .bg-primary-light h3, .o_colored_level .bg-primary-light h3, .bg-dark-light .h3, .o_colored_level .bg-dark-light .h3, .bg-light-light .h3, .o_colored_level .bg-light-light .h3, .bg-danger-light .h3, .o_colored_level .bg-danger-light .h3, .bg-warning-light .h3, .o_colored_level .bg-warning-light .h3, .bg-info-light .h3, .bg-success-light .h3, .bg-secondary-light .h3, .bg-primary-light .h3, .bg-dark-light h4, .o_colored_level .bg-dark-light h4, .bg-light-light h4, .o_colored_level .bg-light-light h4, .bg-danger-light h4, .o_colored_level .bg-danger-light h4, .bg-warning-light h4, .o_colored_level .bg-warning-light h4, .bg-info-light h4, .o_colored_level .bg-info-light h4, .bg-success-light h4, .o_colored_level .bg-success-light h4, .bg-secondary-light h4, .o_colored_level .bg-secondary-light h4, .bg-primary-light h4, .o_colored_level .bg-primary-light h4, .bg-dark-light .h4, .o_colored_level .bg-dark-light .h4, .bg-light-light .h4, .o_colored_level .bg-light-light .h4, .bg-danger-light .h4, .o_colored_level .bg-danger-light .h4, .bg-warning-light .h4, .o_colored_level .bg-warning-light .h4, .bg-info-light .h4, .bg-success-light .h4, .bg-secondary-light .h4, .bg-primary-light .h4, .bg-dark-light h5, .o_colored_level .bg-dark-light h5, .bg-light-light h5, .o_colored_level .bg-light-light h5, .bg-danger-light h5, .o_colored_level .bg-danger-light h5, .bg-warning-light h5, .o_colored_level .bg-warning-light h5, .bg-info-light h5, .o_colored_level .bg-info-light h5, .bg-success-light h5, .o_colored_level .bg-success-light h5, .bg-secondary-light h5, .o_colored_level .bg-secondary-light h5, .bg-primary-light h5, .o_colored_level .bg-primary-light h5, .bg-dark-light .h5, .o_colored_level .bg-dark-light .h5, .bg-light-light .h5, .o_colored_level .bg-light-light .h5, .bg-danger-light .h5, .o_colored_level .bg-danger-light .h5, .bg-warning-light .h5, .o_colored_level .bg-warning-light .h5, .bg-info-light .h5, .bg-success-light .h5, .bg-secondary-light .h5, .bg-primary-light .h5, .bg-dark-light h6, .o_colored_level .bg-dark-light h6, .bg-light-light h6, .o_colored_level .bg-light-light h6, .bg-danger-light h6, .o_colored_level .bg-danger-light h6, .bg-warning-light h6, .o_colored_level .bg-warning-light h6, .bg-info-light h6, .o_colored_level .bg-info-light h6, .bg-success-light h6, .o_colored_level .bg-success-light h6, .bg-secondary-light h6, .o_colored_level .bg-secondary-light h6, .bg-primary-light h6, .o_colored_level .bg-primary-light h6, .bg-dark-light .h6, .o_colored_level .bg-dark-light .h6, .bg-light-light .h6, .o_colored_level .bg-light-light .h6, .bg-danger-light .h6, .o_colored_level .bg-danger-light .h6, .bg-warning-light .h6, .o_colored_level .bg-warning-light .h6, .bg-info-light .h6, .bg-success-light .h6, .bg-secondary-light .h6, .bg-primary-light .h6{color: inherit;}

/* /web/static/src/scss/utils.scss */
 .o_colorpicker_widget .o_opacity_slider, .o_colorpicker_widget .o_color_preview{position: relative; z-index: 0;}.o_colorpicker_widget .o_opacity_slider::before, .o_colorpicker_widget .o_color_preview::before{content: ""; position: absolute; top: 0; left: 0; bottom: 0; right: 0; z-index: -1; background-image: url("/web/static/img/transparent.png"); background-size: 10px auto; border-radius: inherit;}.o_colorpicker_widget .o_opacity_slider::after, .o_colorpicker_widget .o_color_preview::after{content: ""; position: absolute; top: 0; left: 0; bottom: 0; right: 0; z-index: -1; background: inherit; border-radius: inherit;}

/* /web/static/src/scss/primary_variables.scss */
 

/* /muk_web_chatter/static/src/scss/variables.scss */
 

/* /web/static/src/core/avatar/avatar.variables.scss */
 

/* /web/static/src/core/notifications/notification.variables.scss */
 

/* /web/static/src/search/control_panel/control_panel.variables.scss */
 

/* /web/static/src/search/search_bar/search_bar.variables.scss */
 

/* /web/static/src/search/search_panel/search_panel.variables.scss */
 

/* /web/static/src/views/fields/statusbar/statusbar_field.variables.scss */
 

/* /web/static/src/views/fields/translation_button.variables.scss */
 

/* /web/static/src/views/form/form.variables.scss */
 

/* /web/static/src/views/kanban/kanban.variables.scss */
 

/* /web/static/src/webclient/burger_menu/burger_menu.variables.scss */
 

/* /web/static/src/webclient/navbar/navbar.variables.scss */
 

/* /mail/static/src/core/common/primary_variables.scss */
 

/* /mail/static/src/discuss/typing/common/primary_variables.scss */
 

/* /mail/static/src/scss/variables/primary_variables.scss */
 

/* /onboarding/static/src/scss/onboarding.variables.scss */
 

/* /web_editor/static/src/scss/web_editor.variables.scss */
 

/* /web_editor/static/src/scss/wysiwyg.variables.scss */
 

/* /portal/static/src/scss/primary_variables.scss */
 

/* /account/static/src/scss/variables.scss */
 @keyframes animate-red{0%{color: red;}100%{color: inherit;}}.animate{animation: animate-red 1s ease;}

/* /website/static/src/scss/primary_variables.scss */
 

/* /website/static/src/scss/options/user_values.scss */
 

/* /website/static/src/scss/options/colors/user_color_palette.scss */
 

/* /website/static/src/scss/options/colors/user_gray_color_palette.scss */
 

/* /website/static/src/scss/options/colors/user_theme_color_palette.scss */
 

/* /website_sale/static/src/scss/primary_variables.scss */
 

/* /hr_org_chart/static/src/scss/variables.scss */
 

/* /muk_web_appsbar/static/src/scss/variables.scss */
 

/* /website/static/src/snippets/s_badge/000_variables.scss */
 

/* /website/static/src/snippets/s_product_list/000_variables.scss */
 

/* /website/static/src/scss/secondary_variables.scss */
 

/* /web/static/src/scss/secondary_variables.scss */
 

/* /web_editor/static/src/scss/secondary_variables.scss */
 

/* /web/static/src/scss/bootstrap_overridden.scss */
 .user-select-none{-webkit-user-select: none !important;}

/* /web/static/src/scss/bs_mixins_overrides_backend.scss */
 

/* /web_editor/static/src/scss/bootstrap_overridden_backend.scss */
 

/* /web_editor/static/src/scss/bootstrap_overridden.scss */
 

/* /muk_web_appsbar/static/src/scss/mixins.scss */
 

/* /web/static/src/scss/pre_variables.scss */
 

/* /web/static/lib/bootstrap/scss/_variables.scss */
 

/* /web/static/lib/bootstrap/scss/_variables-dark.scss */
 

/* /web/static/lib/bootstrap/scss/_maps.scss */
 

/* /web/static/src/scss/import_bootstrap.scss */
 :root, [data-bs-theme="light"]{--blue: #007bff; --indigo: #6610f2; --purple: #6f42c1; --pink: #e83e8c; --red: #dc3545; --orange: #fd7e14; --yellow: #ffc107; --green: #28a745; --teal: #20c997; --cyan: #17a2b8; --white: #FFFFFF; --gray: #6c757d; --gray-dark: #343a40; --o-cc5-btn-secondary-border: ; --o-cc5-btn-secondary: #F3F2F2; --o-cc5-btn-primary-border: ; --o-cc5-btn-primary: ; --o-cc5-link: ; --o-cc5-h6: ; --o-cc5-h5: ; --o-cc5-h4: ; --o-cc5-h3: ; --o-cc5-h2: ; --o-cc5-headings: #FFFFFF; --o-cc5-text: ; --o-cc5-bg: #111827; --o-cc4-btn-secondary-border: ; --o-cc4-btn-secondary: #F3F2F2; --o-cc4-btn-primary-border: ; --o-cc4-btn-primary: #111827; --o-cc4-link: #111827; --o-cc4-h6: ; --o-cc4-h5: ; --o-cc4-h4: ; --o-cc4-h3: ; --o-cc4-h2: ; --o-cc4-headings: ; --o-cc4-text: ; --o-cc4-bg: #714B67; --o-cc3-btn-secondary-border: ; --o-cc3-btn-secondary: #F3F2F2; --o-cc3-btn-primary-border: ; --o-cc3-btn-primary: ; --o-cc3-link: ; --o-cc3-h6: ; --o-cc3-h5: ; --o-cc3-h4: ; --o-cc3-h3: ; --o-cc3-h2: ; --o-cc3-headings: ; --o-cc3-text: ; --o-cc3-bg: #2D3142; --o-cc2-btn-secondary-border: ; --o-cc2-btn-secondary: ; --o-cc2-btn-primary-border: ; --o-cc2-btn-primary: ; --o-cc2-link: ; --o-cc2-h6: ; --o-cc2-h5: ; --o-cc2-h4: ; --o-cc2-h3: ; --o-cc2-h2: ; --o-cc2-headings: #111827; --o-cc2-text: ; --o-cc2-bg: #F3F2F2; --o-cc1-btn-secondary-border: ; --o-cc1-btn-secondary: ; --o-cc1-btn-primary-border: ; --o-cc1-btn-primary: ; --o-cc1-link: ; --o-cc1-h6: ; --o-cc1-h5: ; --o-cc1-h4: ; --o-cc1-h3: ; --o-cc1-h2: ; --o-cc1-headings: ; --o-cc1-text: ; --o-cc1-bg: #FFFFFF; --copyright-custom: rgba(0, 0, 0, 0.15); --copyright: ; --footer-custom: ; --footer: #111827; --header-sales_four-custom: ; --header-sales_four: #FFFFFF; --header-sales_three-custom: ; --header-sales_three: #F3F2F2; --header-sales_two-custom: ; --header-sales_two: #111827; --header-sales_one-custom: ; --header-sales_one: #F3F2F2; --menu-border-color: ; --menu-custom: ; --menu: #FFFFFF; --input: ; --body: white; --o-color-5: #111827; --o-color-4: #FFFFFF; --o-color-3: #F3F2F2; --o-color-2: #2D3142; --o-color-1: #714B67; --gray-100: #f8f9fa; --gray-200: #e9ecef; --gray-300: #dee2e6; --gray-400: #ced4da; --gray-500: #adb5bd; --gray-600: #6c757d; --gray-700: #495057; --gray-800: #343a40; --gray-900: #212529; --gray-white-85: rgba(255, 255, 255, 0.85); --gray-white-75: rgba(255, 255, 255, 0.75); --gray-white-50: rgba(255, 255, 255, 0.5); --gray-white-25: rgba(255, 255, 255, 0.25); --gray-black-75: rgba(0, 0, 0, 0.75); --gray-black-50: rgba(0, 0, 0, 0.5); --gray-black-25: rgba(0, 0, 0, 0.25); --gray-black-15: rgba(0, 0, 0, 0.15); --primary: #71639e; --secondary: #dee2e6; --success: #28a745; --info: #17a2b8; --warning: #ffac00; --danger: #dc3545; --light: #f8f9fa; --dark: #212529; --primary-rgb: 113, 99, 158; --secondary-rgb: 222, 226, 230; --success-rgb: 40, 167, 69; --info-rgb: 23, 162, 184; --warning-rgb: 255, 172, 0; --danger-rgb: 220, 53, 69; --light-rgb: 248, 249, 250; --dark-rgb: 33, 37, 41; --primary-text-emphasis: #2d283f; --secondary-text-emphasis: #595a5c; --success-text-emphasis: #10431c; --info-text-emphasis: #09414a; --warning-text-emphasis: #664500; --danger-text-emphasis: #58151c; --light-text-emphasis: #495057; --dark-text-emphasis: #495057; --primary-bg-subtle: #e3e0ec; --secondary-bg-subtle: #f8f9fa; --success-bg-subtle: #d4edda; --info-bg-subtle: #d1ecf1; --warning-bg-subtle: #ffeecc; --danger-bg-subtle: #f8d7da; --light-bg-subtle: #fcfcfd; --dark-bg-subtle: #ced4da; --primary-border-subtle: #c6c1d8; --secondary-border-subtle: #f2f3f5; --success-border-subtle: #a9dcb5; --info-border-subtle: #a2dae3; --warning-border-subtle: #ffde99; --danger-border-subtle: #f1aeb5; --light-border-subtle: #e9ecef; --dark-border-subtle: #adb5bd; --white-rgb: 255, 255, 255; --black-rgb: 0, 0, 0; --font-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Ubuntu, "Noto Sans", Arial, "Odoo Unicode Support Noto", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; --font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; --gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0)); --body-font-family: var(--font-sans-serif); --body-font-size: 0.875rem; --body-font-weight: 400; --body-line-height: 1.5; --body-color: #495057; --body-color-rgb: 73, 80, 87; --body-bg: #f8f9fa; --body-bg-rgb: 248, 249, 250; --emphasis-color: #000000; --emphasis-color-rgb: 0, 0, 0; --secondary-color: rgba(73, 80, 87, 0.75); --secondary-color-rgb: 73, 80, 87; --secondary-bg: #e9ecef; --secondary-bg-rgb: 233, 236, 239; --tertiary-color: rgba(73, 80, 87, 0.5); --tertiary-color-rgb: 73, 80, 87; --tertiary-bg: #f8f9fa; --tertiary-bg-rgb: 248, 249, 250; --heading-color: #212529; --link-color: #66598f; --link-color-rgb: 101.56719368, 88.756917, 142.743083; --link-decoration: none; --link-hover-color: #473e64; --link-hover-color-rgb: 71, 62, 100; --link-hover-decoration: none; --code-color: #d2317b; --highlight-color: #495057; --highlight-bg: #fff3cd; --border-width: 1px; --border-style: solid; --border-color: #dee2e6; --border-color-translucent: rgba(0, 0, 0, 0.175); --border-radius: 0.25rem; --border-radius-sm: 0.1875rem; --border-radius-lg: 0.375rem; --border-radius-xl: 1rem; --border-radius-xxl: 2rem; --border-radius-2xl: var(--border-radius-xxl); --border-radius-pill: 50rem; --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15); --box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); --box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175); --box-shadow-inset: inset 0 1px 2px rgba(0, 0, 0, 0.075); --focus-ring-width: 0.25rem; --focus-ring-opacity: 0.25; --focus-ring-color: rgba(113, 99, 158, 0.25); --form-valid-color: #28a745; --form-valid-border-color: #28a745; --form-invalid-color: #dc3545; --form-invalid-border-color: #dc3545;}*, *::before, *::after{box-sizing: border-box;}body{margin: 0; font-family: var(--body-font-family); font-size: var(--body-font-size); font-weight: var(--body-font-weight); line-height: var(--body-line-height); color: var(--body-color); text-align: var(--body-text-align); background-color: var(--body-bg); -webkit-text-size-adjust: 100%; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);}hr{margin: 16px 0; color: inherit; border: 0; border-top: var(--border-width) solid; opacity: 0.25;}h6, .h6, h5, .h5, h4, .h4, h3, .h3, h2, .h2, h1, .h1{margin-top: 0; margin-bottom: 8px; font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Ubuntu, "Noto Sans", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; font-weight: 500; line-height: 1.2; color: var(--heading-color);}h1, .h1{font-size: calc(1.3rem + 0.6vw);}@media (min-width: 1200px){h1, .h1{font-size: 1.75rem;}}h2, .h2{font-size: calc(1.25625rem + 0.075vw);}@media (min-width: 1200px){h2, .h2{font-size: 1.3125rem;}}h3, .h3{font-size: 1.1375rem;}h4, .h4{font-size: 1.05rem;}h5, .h5{font-size: 0.9625rem;}h6, .h6{font-size: 0.875rem;}p{margin-top: 0; margin-bottom: 1rem;}abbr[title]{text-decoration: underline dotted; cursor: help; text-decoration-skip-ink: none;}address{margin-bottom: 1rem; font-style: normal; line-height: inherit;}ol, ul{padding-left: 2rem;}ol, ul, dl{margin-top: 0; margin-bottom: 1rem;}ol ol, ul ul, ol ul, ul ol{margin-bottom: 0;}dt{font-weight: 500;}dd{margin-bottom: .5rem; margin-left: 0;}blockquote{margin: 0 0 1rem;}b, strong{font-weight: bolder;}small, .small{font-size: 0.8125rem;}mark, .mark{padding: 0.1875em; color: var(--highlight-color); background-color: var(--highlight-bg);}sub, sup{position: relative; font-size: 0.75em; line-height: 0; vertical-align: baseline;}sub{bottom: -.25em;}sup{top: -.5em;}a{color: rgba(var(--link-color-rgb), var(--link-opacity, 1)); text-decoration: none;}a:hover{--link-color-rgb: var(--link-hover-color-rgb); text-decoration: none;}a:not([href]):not([class]), a:not([href]):not([class]):hover{color: inherit; text-decoration: none;}pre, code, kbd, samp{font-family: var(--font-monospace); font-size: 1em;}pre{display: block; margin-top: 0; margin-bottom: 1rem; overflow: auto; font-size: 0.8125rem;}pre code{font-size: inherit; color: inherit; word-break: normal;}code{font-size: 0.8125rem; color: var(--code-color); word-wrap: break-word;}a > code{color: inherit;}kbd{padding: 0.1875rem 0.375rem; font-size: 0.8125rem; color: #495057; background-color: #f8f9fa; border-radius: 0.1875rem;}kbd kbd{padding: 0; font-size: 1em;}figure{margin: 0 0 1rem;}img, svg{vertical-align: middle;}table{caption-side: bottom; border-collapse: collapse;}caption{padding-top: 0.75rem; padding-bottom: 0.75rem; color: var(--secondary-color); text-align: left;}th{font-weight: 500; text-align: inherit; text-align: -webkit-match-parent;}thead, tbody, tfoot, tr, td, th{border-color: inherit; border-style: solid; border-width: 0;}label{display: inline-block;}button{border-radius: 0;}button:focus:not(:focus-visible){outline: 0;}input, button, select, optgroup, textarea{margin: 0; font-family: inherit; font-size: inherit; line-height: inherit;}button, select{text-transform: none;}[role="button"]{cursor: pointer;}select{word-wrap: normal;}select:disabled{opacity: 1;}[list]:not([type="date"]):not([type="datetime-local"]):not([type="month"]):not([type="week"]):not([type="time"])::-webkit-calendar-picker-indicator{display: none !important;}button, [type="button"], [type="reset"], [type="submit"]{-webkit--webkit-appearance: button; -moz-appearance: button; appearance: button;}button:not(:disabled), [type="button"]:not(:disabled), [type="reset"]:not(:disabled), [type="submit"]:not(:disabled){cursor: pointer;}::-moz-focus-inner{padding: 0; border-style: none;}textarea{resize: vertical;}fieldset{min-width: 0; padding: 0; margin: 0; border: 0;}legend{float: left; width: 100%; padding: 0; margin-bottom: 0.5rem; font-size: calc(1.275rem + 0.3vw); line-height: inherit;}@media (min-width: 1200px){legend{font-size: 1.5rem;}}legend + *{clear: left;}::-webkit-datetime-edit-fields-wrapper, ::-webkit-datetime-edit-text, ::-webkit-datetime-edit-minute, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-year-field{padding: 0;}::-webkit-inner-spin-button{height: auto;}[type="search"]{-webkit--webkit-appearance: textfield; -moz-appearance: textfield; appearance: textfield; outline-offset: -2px;}::-webkit-search-decoration{-webkit--webkit-appearance: none; -moz-appearance: none; appearance: none;}::-webkit-color-swatch-wrapper{padding: 0;}::file-selector-button{font: inherit; -webkit--webkit-appearance: button; -moz-appearance: button; appearance: button;}output{display: inline-block;}iframe{border: 0;}summary{display: list-item; cursor: pointer;}progress{vertical-align: baseline;}[hidden]{display: none !important;}.lead{font-size: 1.09375rem; font-weight: 300;}.display-1{font-size: calc(1.625rem + 4.5vw); font-weight: 300; line-height: 1.2;}@media (min-width: 1200px){.display-1{font-size: 5rem;}}.display-2{font-size: calc(1.575rem + 3.9vw); font-weight: 300; line-height: 1.2;}@media (min-width: 1200px){.display-2{font-size: 4.5rem;}}.display-3{font-size: calc(1.525rem + 3.3vw); font-weight: 300; line-height: 1.2;}@media (min-width: 1200px){.display-3{font-size: 4rem;}}.display-4{font-size: calc(1.475rem + 2.7vw); font-weight: 300; line-height: 1.2;}@media (min-width: 1200px){.display-4{font-size: 3.5rem;}}.display-5{font-size: calc(1.425rem + 2.1vw); font-weight: 300; line-height: 1.2;}@media (min-width: 1200px){.display-5{font-size: 3rem;}}.display-6{font-size: calc(1.375rem + 1.5vw); font-weight: 300; line-height: 1.2;}@media (min-width: 1200px){.display-6{font-size: 2.5rem;}}.list-unstyled{padding-left: 0; list-style: none;}.list-inline{padding-left: 0; list-style: none;}.list-inline-item{display: inline-block;}.list-inline-item:not(:last-child){margin-right: 0.5rem;}.initialism{font-size: 0.8125rem; text-transform: uppercase;}.blockquote{margin-bottom: 16px; font-size: 1.09375rem;}.blockquote > :last-child{margin-bottom: 0;}.blockquote-footer{margin-top: -16px; margin-bottom: 16px; font-size: 0.8125rem; color: #6c757d;}.blockquote-footer::before{content: "\2014\00A0";}.img-fluid{max-width: 100%; height: auto;}.img-thumbnail{padding: 0.25rem; background-color: var(--body-bg); border: var(--border-width) solid var(--border-color); border-radius: var(--border-radius); max-width: 100%; height: auto;}.figure{display: inline-block;}.figure-img{margin-bottom: 8px; line-height: 1;}.figure-caption{font-size: 0.8125rem; color: var(--secondary-color);}.container, .container-fluid, .container-xxl, .container-xl, .container-lg, .container-md, .container-sm{--gutter-x: 32px; --gutter-y: 0; width: 100%; padding-right: calc(var(--gutter-x) * .5); padding-left: calc(var(--gutter-x) * .5); margin-right: auto; margin-left: auto;}@media (min-width: 576px){.container-sm, .container{max-width: 540px;}}@media (min-width: 768px){.container-md, .container-sm, .container{max-width: 720px;}}@media (min-width: 992px){.container-lg, .container-md, .container-sm, .container{max-width: 960px;}}@media (min-width: 1200px){.container-xl, .container-lg, .container-md, .container-sm, .container{max-width: 1140px;}}@media (min-width: 1400px){.container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container{max-width: 1320px;}}:root{--breakpoint-xs: 0; --breakpoint-sm: 576px; --breakpoint-md: 768px; --breakpoint-lg: 992px; --breakpoint-xl: 1200px; --breakpoint-xxl: 1400px;}.row{--gutter-x: 32px; --gutter-y: 0; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; margin-top: calc(-1 * var(--gutter-y)); margin-right: calc(-.5 * var(--gutter-x)); margin-left: calc(-.5 * var(--gutter-x));}.row > *{flex-shrink: 0; width: 100%; max-width: 100%; padding-right: calc(var(--gutter-x) * .5); padding-left: calc(var(--gutter-x) * .5); margin-top: var(--gutter-y);}.grid{display: grid; grid-template-rows: repeat(var(--rows, 1), 1fr); grid-template-columns: repeat(var(--columns, 12), 1fr); gap: var(--gap, 32px);}.grid .g-col-1{grid-column: auto/span 1;}.grid .g-col-2{grid-column: auto/span 2;}.grid .g-col-3{grid-column: auto/span 3;}.grid .g-col-4{grid-column: auto/span 4;}.grid .g-col-5{grid-column: auto/span 5;}.grid .g-col-6{grid-column: auto/span 6;}.grid .g-col-7{grid-column: auto/span 7;}.grid .g-col-8{grid-column: auto/span 8;}.grid .g-col-9{grid-column: auto/span 9;}.grid .g-col-10{grid-column: auto/span 10;}.grid .g-col-11{grid-column: auto/span 11;}.grid .g-col-12{grid-column: auto/span 12;}.grid .g-start-1{grid-column-start: 1;}.grid .g-start-2{grid-column-start: 2;}.grid .g-start-3{grid-column-start: 3;}.grid .g-start-4{grid-column-start: 4;}.grid .g-start-5{grid-column-start: 5;}.grid .g-start-6{grid-column-start: 6;}.grid .g-start-7{grid-column-start: 7;}.grid .g-start-8{grid-column-start: 8;}.grid .g-start-9{grid-column-start: 9;}.grid .g-start-10{grid-column-start: 10;}.grid .g-start-11{grid-column-start: 11;}@media (min-width: 576px){.grid .g-col-sm-1{grid-column: auto/span 1;}.grid .g-col-sm-2{grid-column: auto/span 2;}.grid .g-col-sm-3{grid-column: auto/span 3;}.grid .g-col-sm-4{grid-column: auto/span 4;}.grid .g-col-sm-5{grid-column: auto/span 5;}.grid .g-col-sm-6{grid-column: auto/span 6;}.grid .g-col-sm-7{grid-column: auto/span 7;}.grid .g-col-sm-8{grid-column: auto/span 8;}.grid .g-col-sm-9{grid-column: auto/span 9;}.grid .g-col-sm-10{grid-column: auto/span 10;}.grid .g-col-sm-11{grid-column: auto/span 11;}.grid .g-col-sm-12{grid-column: auto/span 12;}.grid .g-start-sm-1{grid-column-start: 1;}.grid .g-start-sm-2{grid-column-start: 2;}.grid .g-start-sm-3{grid-column-start: 3;}.grid .g-start-sm-4{grid-column-start: 4;}.grid .g-start-sm-5{grid-column-start: 5;}.grid .g-start-sm-6{grid-column-start: 6;}.grid .g-start-sm-7{grid-column-start: 7;}.grid .g-start-sm-8{grid-column-start: 8;}.grid .g-start-sm-9{grid-column-start: 9;}.grid .g-start-sm-10{grid-column-start: 10;}.grid .g-start-sm-11{grid-column-start: 11;}}@media (min-width: 768px){.grid .g-col-md-1{grid-column: auto/span 1;}.grid .g-col-md-2{grid-column: auto/span 2;}.grid .g-col-md-3{grid-column: auto/span 3;}.grid .g-col-md-4{grid-column: auto/span 4;}.grid .g-col-md-5{grid-column: auto/span 5;}.grid .g-col-md-6{grid-column: auto/span 6;}.grid .g-col-md-7{grid-column: auto/span 7;}.grid .g-col-md-8{grid-column: auto/span 8;}.grid .g-col-md-9{grid-column: auto/span 9;}.grid .g-col-md-10{grid-column: auto/span 10;}.grid .g-col-md-11{grid-column: auto/span 11;}.grid .g-col-md-12{grid-column: auto/span 12;}.grid .g-start-md-1{grid-column-start: 1;}.grid .g-start-md-2{grid-column-start: 2;}.grid .g-start-md-3{grid-column-start: 3;}.grid .g-start-md-4{grid-column-start: 4;}.grid .g-start-md-5{grid-column-start: 5;}.grid .g-start-md-6{grid-column-start: 6;}.grid .g-start-md-7{grid-column-start: 7;}.grid .g-start-md-8{grid-column-start: 8;}.grid .g-start-md-9{grid-column-start: 9;}.grid .g-start-md-10{grid-column-start: 10;}.grid .g-start-md-11{grid-column-start: 11;}}@media (min-width: 992px){.grid .g-col-lg-1{grid-column: auto/span 1;}.grid .g-col-lg-2{grid-column: auto/span 2;}.grid .g-col-lg-3{grid-column: auto/span 3;}.grid .g-col-lg-4{grid-column: auto/span 4;}.grid .g-col-lg-5{grid-column: auto/span 5;}.grid .g-col-lg-6{grid-column: auto/span 6;}.grid .g-col-lg-7{grid-column: auto/span 7;}.grid .g-col-lg-8{grid-column: auto/span 8;}.grid .g-col-lg-9{grid-column: auto/span 9;}.grid .g-col-lg-10{grid-column: auto/span 10;}.grid .g-col-lg-11{grid-column: auto/span 11;}.grid .g-col-lg-12{grid-column: auto/span 12;}.grid .g-start-lg-1{grid-column-start: 1;}.grid .g-start-lg-2{grid-column-start: 2;}.grid .g-start-lg-3{grid-column-start: 3;}.grid .g-start-lg-4{grid-column-start: 4;}.grid .g-start-lg-5{grid-column-start: 5;}.grid .g-start-lg-6{grid-column-start: 6;}.grid .g-start-lg-7{grid-column-start: 7;}.grid .g-start-lg-8{grid-column-start: 8;}.grid .g-start-lg-9{grid-column-start: 9;}.grid .g-start-lg-10{grid-column-start: 10;}.grid .g-start-lg-11{grid-column-start: 11;}}@media (min-width: 1200px){.grid .g-col-xl-1{grid-column: auto/span 1;}.grid .g-col-xl-2{grid-column: auto/span 2;}.grid .g-col-xl-3{grid-column: auto/span 3;}.grid .g-col-xl-4{grid-column: auto/span 4;}.grid .g-col-xl-5{grid-column: auto/span 5;}.grid .g-col-xl-6{grid-column: auto/span 6;}.grid .g-col-xl-7{grid-column: auto/span 7;}.grid .g-col-xl-8{grid-column: auto/span 8;}.grid .g-col-xl-9{grid-column: auto/span 9;}.grid .g-col-xl-10{grid-column: auto/span 10;}.grid .g-col-xl-11{grid-column: auto/span 11;}.grid .g-col-xl-12{grid-column: auto/span 12;}.grid .g-start-xl-1{grid-column-start: 1;}.grid .g-start-xl-2{grid-column-start: 2;}.grid .g-start-xl-3{grid-column-start: 3;}.grid .g-start-xl-4{grid-column-start: 4;}.grid .g-start-xl-5{grid-column-start: 5;}.grid .g-start-xl-6{grid-column-start: 6;}.grid .g-start-xl-7{grid-column-start: 7;}.grid .g-start-xl-8{grid-column-start: 8;}.grid .g-start-xl-9{grid-column-start: 9;}.grid .g-start-xl-10{grid-column-start: 10;}.grid .g-start-xl-11{grid-column-start: 11;}}@media (min-width: 1400px){.grid .g-col-xxl-1{grid-column: auto/span 1;}.grid .g-col-xxl-2{grid-column: auto/span 2;}.grid .g-col-xxl-3{grid-column: auto/span 3;}.grid .g-col-xxl-4{grid-column: auto/span 4;}.grid .g-col-xxl-5{grid-column: auto/span 5;}.grid .g-col-xxl-6{grid-column: auto/span 6;}.grid .g-col-xxl-7{grid-column: auto/span 7;}.grid .g-col-xxl-8{grid-column: auto/span 8;}.grid .g-col-xxl-9{grid-column: auto/span 9;}.grid .g-col-xxl-10{grid-column: auto/span 10;}.grid .g-col-xxl-11{grid-column: auto/span 11;}.grid .g-col-xxl-12{grid-column: auto/span 12;}.grid .g-start-xxl-1{grid-column-start: 1;}.grid .g-start-xxl-2{grid-column-start: 2;}.grid .g-start-xxl-3{grid-column-start: 3;}.grid .g-start-xxl-4{grid-column-start: 4;}.grid .g-start-xxl-5{grid-column-start: 5;}.grid .g-start-xxl-6{grid-column-start: 6;}.grid .g-start-xxl-7{grid-column-start: 7;}.grid .g-start-xxl-8{grid-column-start: 8;}.grid .g-start-xxl-9{grid-column-start: 9;}.grid .g-start-xxl-10{grid-column-start: 10;}.grid .g-start-xxl-11{grid-column-start: 11;}}.col{flex: 1 0 0%;}.row-cols-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-1{margin-left: 8.33333333%;}.offset-2{margin-left: 16.66666667%;}.offset-3{margin-left: 25%;}.offset-4{margin-left: 33.33333333%;}.offset-5{margin-left: 41.66666667%;}.offset-6{margin-left: 50%;}.offset-7{margin-left: 58.33333333%;}.offset-8{margin-left: 66.66666667%;}.offset-9{margin-left: 75%;}.offset-10{margin-left: 83.33333333%;}.offset-11{margin-left: 91.66666667%;}.g-0, .gx-0{--gutter-x: 0;}.g-0, .gy-0{--gutter-y: 0;}.g-1, .gx-1{--gutter-x: 4px;}.g-1, .gy-1{--gutter-y: 4px;}.g-2, .gx-2{--gutter-x: 8px;}.g-2, .gy-2{--gutter-y: 8px;}.g-3, .gx-3{--gutter-x: 16px;}.g-3, .gy-3{--gutter-y: 16px;}.g-4, .gx-4{--gutter-x: 24px;}.g-4, .gy-4{--gutter-y: 24px;}.g-5, .gx-5{--gutter-x: 48px;}.g-5, .gy-5{--gutter-y: 48px;}@media (min-width: 576px){.col-sm{flex: 1 0 0%;}.row-cols-sm-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-sm-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-sm-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-sm-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-sm-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-sm-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-sm-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-sm-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-sm-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-sm-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-sm-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-sm-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-sm-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-sm-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-sm-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-sm-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-sm-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-sm-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-sm-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-sm-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-sm-0{margin-left: 0;}.offset-sm-1{margin-left: 8.33333333%;}.offset-sm-2{margin-left: 16.66666667%;}.offset-sm-3{margin-left: 25%;}.offset-sm-4{margin-left: 33.33333333%;}.offset-sm-5{margin-left: 41.66666667%;}.offset-sm-6{margin-left: 50%;}.offset-sm-7{margin-left: 58.33333333%;}.offset-sm-8{margin-left: 66.66666667%;}.offset-sm-9{margin-left: 75%;}.offset-sm-10{margin-left: 83.33333333%;}.offset-sm-11{margin-left: 91.66666667%;}.g-sm-0, .gx-sm-0{--gutter-x: 0;}.g-sm-0, .gy-sm-0{--gutter-y: 0;}.g-sm-1, .gx-sm-1{--gutter-x: 4px;}.g-sm-1, .gy-sm-1{--gutter-y: 4px;}.g-sm-2, .gx-sm-2{--gutter-x: 8px;}.g-sm-2, .gy-sm-2{--gutter-y: 8px;}.g-sm-3, .gx-sm-3{--gutter-x: 16px;}.g-sm-3, .gy-sm-3{--gutter-y: 16px;}.g-sm-4, .gx-sm-4{--gutter-x: 24px;}.g-sm-4, .gy-sm-4{--gutter-y: 24px;}.g-sm-5, .gx-sm-5{--gutter-x: 48px;}.g-sm-5, .gy-sm-5{--gutter-y: 48px;}}@media (min-width: 768px){.col-md{flex: 1 0 0%;}.row-cols-md-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-md-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-md-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-md-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-md-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-md-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-md-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-md-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-md-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-md-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-md-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-md-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-md-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-md-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-md-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-md-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-md-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-md-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-md-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-md-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-md-0{margin-left: 0;}.offset-md-1{margin-left: 8.33333333%;}.offset-md-2{margin-left: 16.66666667%;}.offset-md-3{margin-left: 25%;}.offset-md-4{margin-left: 33.33333333%;}.offset-md-5{margin-left: 41.66666667%;}.offset-md-6{margin-left: 50%;}.offset-md-7{margin-left: 58.33333333%;}.offset-md-8{margin-left: 66.66666667%;}.offset-md-9{margin-left: 75%;}.offset-md-10{margin-left: 83.33333333%;}.offset-md-11{margin-left: 91.66666667%;}.g-md-0, .gx-md-0{--gutter-x: 0;}.g-md-0, .gy-md-0{--gutter-y: 0;}.g-md-1, .gx-md-1{--gutter-x: 4px;}.g-md-1, .gy-md-1{--gutter-y: 4px;}.g-md-2, .gx-md-2{--gutter-x: 8px;}.g-md-2, .gy-md-2{--gutter-y: 8px;}.g-md-3, .gx-md-3{--gutter-x: 16px;}.g-md-3, .gy-md-3{--gutter-y: 16px;}.g-md-4, .gx-md-4{--gutter-x: 24px;}.g-md-4, .gy-md-4{--gutter-y: 24px;}.g-md-5, .gx-md-5{--gutter-x: 48px;}.g-md-5, .gy-md-5{--gutter-y: 48px;}}@media (min-width: 992px){.col-lg{flex: 1 0 0%;}.row-cols-lg-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-lg-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-lg-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-lg-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-lg-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-lg-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-lg-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-lg-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-lg-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-lg-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-lg-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-lg-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-lg-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-lg-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-lg-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-lg-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-lg-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-lg-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-lg-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-lg-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-lg-0{margin-left: 0;}.offset-lg-1{margin-left: 8.33333333%;}.offset-lg-2{margin-left: 16.66666667%;}.offset-lg-3{margin-left: 25%;}.offset-lg-4{margin-left: 33.33333333%;}.offset-lg-5{margin-left: 41.66666667%;}.offset-lg-6{margin-left: 50%;}.offset-lg-7{margin-left: 58.33333333%;}.offset-lg-8{margin-left: 66.66666667%;}.offset-lg-9{margin-left: 75%;}.offset-lg-10{margin-left: 83.33333333%;}.offset-lg-11{margin-left: 91.66666667%;}.g-lg-0, .gx-lg-0{--gutter-x: 0;}.g-lg-0, .gy-lg-0{--gutter-y: 0;}.g-lg-1, .gx-lg-1{--gutter-x: 4px;}.g-lg-1, .gy-lg-1{--gutter-y: 4px;}.g-lg-2, .gx-lg-2{--gutter-x: 8px;}.g-lg-2, .gy-lg-2{--gutter-y: 8px;}.g-lg-3, .gx-lg-3{--gutter-x: 16px;}.g-lg-3, .gy-lg-3{--gutter-y: 16px;}.g-lg-4, .gx-lg-4{--gutter-x: 24px;}.g-lg-4, .gy-lg-4{--gutter-y: 24px;}.g-lg-5, .gx-lg-5{--gutter-x: 48px;}.g-lg-5, .gy-lg-5{--gutter-y: 48px;}}@media (min-width: 1200px){.col-xl{flex: 1 0 0%;}.row-cols-xl-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-xl-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-xl-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-xl-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-xl-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-xl-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-xl-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-xl-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-xl-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-xl-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-xl-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-xl-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-xl-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-xl-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-xl-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-xl-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-xl-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-xl-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-xl-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-xl-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-xl-0{margin-left: 0;}.offset-xl-1{margin-left: 8.33333333%;}.offset-xl-2{margin-left: 16.66666667%;}.offset-xl-3{margin-left: 25%;}.offset-xl-4{margin-left: 33.33333333%;}.offset-xl-5{margin-left: 41.66666667%;}.offset-xl-6{margin-left: 50%;}.offset-xl-7{margin-left: 58.33333333%;}.offset-xl-8{margin-left: 66.66666667%;}.offset-xl-9{margin-left: 75%;}.offset-xl-10{margin-left: 83.33333333%;}.offset-xl-11{margin-left: 91.66666667%;}.g-xl-0, .gx-xl-0{--gutter-x: 0;}.g-xl-0, .gy-xl-0{--gutter-y: 0;}.g-xl-1, .gx-xl-1{--gutter-x: 4px;}.g-xl-1, .gy-xl-1{--gutter-y: 4px;}.g-xl-2, .gx-xl-2{--gutter-x: 8px;}.g-xl-2, .gy-xl-2{--gutter-y: 8px;}.g-xl-3, .gx-xl-3{--gutter-x: 16px;}.g-xl-3, .gy-xl-3{--gutter-y: 16px;}.g-xl-4, .gx-xl-4{--gutter-x: 24px;}.g-xl-4, .gy-xl-4{--gutter-y: 24px;}.g-xl-5, .gx-xl-5{--gutter-x: 48px;}.g-xl-5, .gy-xl-5{--gutter-y: 48px;}}@media (min-width: 1400px){.col-xxl{flex: 1 0 0%;}.row-cols-xxl-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-xxl-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-xxl-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-xxl-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-xxl-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-xxl-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-xxl-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-xxl-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-xxl-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-xxl-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-xxl-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-xxl-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-xxl-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-xxl-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-xxl-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-xxl-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-xxl-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-xxl-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-xxl-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-xxl-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-xxl-0{margin-left: 0;}.offset-xxl-1{margin-left: 8.33333333%;}.offset-xxl-2{margin-left: 16.66666667%;}.offset-xxl-3{margin-left: 25%;}.offset-xxl-4{margin-left: 33.33333333%;}.offset-xxl-5{margin-left: 41.66666667%;}.offset-xxl-6{margin-left: 50%;}.offset-xxl-7{margin-left: 58.33333333%;}.offset-xxl-8{margin-left: 66.66666667%;}.offset-xxl-9{margin-left: 75%;}.offset-xxl-10{margin-left: 83.33333333%;}.offset-xxl-11{margin-left: 91.66666667%;}.g-xxl-0, .gx-xxl-0{--gutter-x: 0;}.g-xxl-0, .gy-xxl-0{--gutter-y: 0;}.g-xxl-1, .gx-xxl-1{--gutter-x: 4px;}.g-xxl-1, .gy-xxl-1{--gutter-y: 4px;}.g-xxl-2, .gx-xxl-2{--gutter-x: 8px;}.g-xxl-2, .gy-xxl-2{--gutter-y: 8px;}.g-xxl-3, .gx-xxl-3{--gutter-x: 16px;}.g-xxl-3, .gy-xxl-3{--gutter-y: 16px;}.g-xxl-4, .gx-xxl-4{--gutter-x: 24px;}.g-xxl-4, .gy-xxl-4{--gutter-y: 24px;}.g-xxl-5, .gx-xxl-5{--gutter-x: 48px;}.g-xxl-5, .gy-xxl-5{--gutter-y: 48px;}}.table{--table-color-type: initial; --table-bg-type: initial; --table-color-state: initial; --table-bg-state: initial; --table-color: var(--emphasis-color); --table-bg: transparent; --table-border-color: #dee2e6; --table-accent-bg: transparent; --table-striped-color: inherit; --table-striped-bg: rgba(var(--emphasis-color-rgb), 0.01); --table-active-color: #212529; --table-active-bg: rgba(var(--emphasis-color-rgb), 0.05); --table-hover-color: var(--emphasis-color); --table-hover-bg: rgba(var(--emphasis-color-rgb), 0.055); width: 100%; margin-bottom: 16px; vertical-align: top; border-color: var(--table-border-color);}.table > :not(caption) > * > *{padding: 0.75rem 0.75rem; color: var(--table-color-state, var(--table-color-type, var(--table-color))); background-color: var(--table-bg); border-bottom-width: var(--border-width); box-shadow: inset 0 0 0 9999px var(--table-bg-state, var(--table-bg-type, var(--table-accent-bg)));}.table > tbody{vertical-align: inherit;}.table > thead{vertical-align: bottom;}.table-group-divider{border-top: calc(var(--border-width) * 2) solid #e9ecef;}.caption-top{caption-side: top;}.table-sm > :not(caption) > * > *{padding: 0.5rem 0.3rem;}.table-bordered > :not(caption) > *{border-width: var(--border-width) 0;}.table-bordered > :not(caption) > * > *{border-width: 0 var(--border-width);}.table-borderless > :not(caption) > * > *{border-bottom-width: 0;}.table-borderless > :not(:first-child){border-top-width: 0;}.table-striped > tbody > tr:nth-of-type(even) > *{--table-color-type: var(--table-striped-color); --table-bg-type: var(--table-striped-bg);}.table-striped-columns > :not(caption) > tr > :nth-child(even){--table-color-type: var(--table-striped-color); --table-bg-type: var(--table-striped-bg);}.table-active{--table-color-state: var(--table-active-color); --table-bg-state: var(--table-active-bg);}.table-hover > tbody > tr:hover > *{--table-color-state: var(--table-hover-color); --table-bg-state: var(--table-hover-bg);}.table-primary{--table-color: #000000; --table-bg: #e3e0ec; --table-border-color: #b6b3bd; --table-striped-bg: #e1deea; --table-striped-color: #000000; --table-active-bg: #d8d5e0; --table-active-color: #000000; --table-hover-bg: #d7d4df; --table-hover-color: #000000; color: var(--table-color); border-color: var(--table-border-color);}.table-secondary{--table-color: #000000; --table-bg: #f8f9fa; --table-border-color: #c6c7c8; --table-striped-bg: #f6f7f8; --table-striped-color: #000000; --table-active-bg: #ecedee; --table-active-color: #000000; --table-hover-bg: #eaebec; --table-hover-color: #000000; color: var(--table-color); border-color: var(--table-border-color);}.table-success{--table-color: #000000; --table-bg: #d4edda; --table-border-color: #aabeae; --table-striped-bg: #d2ebd8; --table-striped-color: #000000; --table-active-bg: #c9e1cf; --table-active-color: #000000; --table-hover-bg: #c8e0ce; --table-hover-color: #000000; color: var(--table-color); border-color: var(--table-border-color);}.table-info{--table-color: #000000; --table-bg: #d1ecf1; --table-border-color: #a7bdc1; --table-striped-bg: #cfeaef; --table-striped-color: #000000; --table-active-bg: #c7e0e5; --table-active-color: #000000; --table-hover-bg: #c6dfe4; --table-hover-color: #000000; color: var(--table-color); border-color: var(--table-border-color);}.table-warning{--table-color: #000000; --table-bg: #ffeecc; --table-border-color: #ccbea3; --table-striped-bg: #fcecca; --table-striped-color: #000000; --table-active-bg: #f2e2c2; --table-active-color: #000000; --table-hover-bg: #f1e1c1; --table-hover-color: #000000; color: var(--table-color); border-color: var(--table-border-color);}.table-danger{--table-color: #000000; --table-bg: #f8d7da; --table-border-color: #c6acae; --table-striped-bg: #f6d5d8; --table-striped-color: #000000; --table-active-bg: #eccccf; --table-active-color: #000000; --table-hover-bg: #eacbce; --table-hover-color: #000000; color: var(--table-color); border-color: var(--table-border-color);}.table-light{--table-color: #000000; --table-bg: #f8f9fa; --table-border-color: #c6c7c8; --table-striped-bg: #f6f7f8; --table-striped-color: #000000; --table-active-bg: #ecedee; --table-active-color: #000000; --table-hover-bg: #eaebec; --table-hover-color: #000000; color: var(--table-color); border-color: var(--table-border-color);}.table-dark{--table-color: #FFFFFF; --table-bg: #212529; --table-border-color: #4d5154; --table-striped-bg: #23272b; --table-striped-color: #FFFFFF; --table-active-bg: #2c3034; --table-active-color: #FFFFFF; --table-hover-bg: #2d3135; --table-hover-color: #FFFFFF; color: var(--table-color); border-color: var(--table-border-color);}.table-responsive{overflow-x: auto; -webkit-overflow-scrolling: touch;}@media (max-width: 575.98px){.table-responsive-sm{overflow-x: auto; -webkit-overflow-scrolling: touch;}}@media (max-width: 767.98px){.table-responsive-md{overflow-x: auto; -webkit-overflow-scrolling: touch;}}@media (max-width: 991.98px){.table-responsive-lg{overflow-x: auto; -webkit-overflow-scrolling: touch;}}@media (max-width: 1199.98px){.table-responsive-xl{overflow-x: auto; -webkit-overflow-scrolling: touch;}}@media (max-width: 1399.98px){.table-responsive-xxl{overflow-x: auto; -webkit-overflow-scrolling: touch;}}.form-label{margin-bottom: 0.5rem;}.col-form-label{padding-top: calc(0.3125rem + var(--border-width)); padding-bottom: calc(0.3125rem + var(--border-width)); margin-bottom: 0; font-size: inherit; line-height: 1.5;}.col-form-label-lg{padding-top: calc(0.375rem + var(--border-width)); padding-bottom: calc(0.375rem + var(--border-width)); font-size: 1.09375rem;}.col-form-label-sm{padding-top: calc(0.1875rem + var(--border-width)); padding-bottom: calc(0.1875rem + var(--border-width)); font-size: 0.8125rem;}.form-text{margin-top: 0.25rem; font-size: 0.8125rem; color: var(--secondary-color);}.form-control{display: block; width: 100%; padding: 0.3125rem 0.625rem; font-size: 0.875rem; font-weight: 400; line-height: 1.5; color: var(--body-color); -webkit-appearance: none; -moz-appearance: none; appearance: none; background-color: transparent; background-clip: padding-box; border: var(--border-width) solid var(--border-color); border-radius: var(--border-radius); transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-control{transition: none;}}.form-control[type="file"]{overflow: hidden;}.form-control[type="file"]:not(:disabled):not([readonly]){cursor: pointer;}.form-control:focus{color: var(--body-color); background-color: transparent; border-color: #71639e; outline: 0; box-shadow: 0 0 0 0.25rem rgba(113, 99, 158, 0.25);}.form-control::-webkit-date-and-time-value{min-width: 85px; height: 1.5em; margin: 0;}.form-control::-webkit-datetime-edit{display: block; padding: 0;}.form-control::placeholder{color: #bec5cc; opacity: 1;}.form-control:disabled{background-color: var(--secondary-bg); opacity: 1;}.form-control::file-selector-button{padding: 0.3125rem 0.625rem; margin: -0.3125rem -0.625rem; margin-inline-end: 0.625rem; color: var(--body-color); background-color: var(--tertiary-bg); pointer-events: none; border-color: inherit; border-style: solid; border-width: 0; border-inline-end-width: var(--border-width); border-radius: 0; transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-control::file-selector-button{transition: none;}}.form-control:hover:not(:disabled):not([readonly])::file-selector-button{background-color: var(--secondary-bg);}.form-control-plaintext{display: block; width: 100%; padding: 0.3125rem 0; margin-bottom: 0; line-height: 1.5; color: var(--body-color); background-color: transparent; border: solid transparent; border-width: var(--border-width) 0;}.form-control-plaintext:focus{outline: 0;}.form-control-plaintext.form-control-sm, .form-control-plaintext.form-control-lg{padding-right: 0; padding-left: 0;}.form-control-sm{min-height: calc(1.5em + 0.375rem + calc(var(--border-width) * 2)); padding: 0.1875rem 0.5rem; font-size: 0.8125rem; border-radius: var(--border-radius-sm);}.form-control-sm::file-selector-button{padding: 0.1875rem 0.5rem; margin: -0.1875rem -0.5rem; margin-inline-end: 0.5rem;}.form-control-lg{min-height: calc(1.5em + 0.75rem + calc(var(--border-width) * 2)); padding: 0.375rem 0.75rem; font-size: 1.09375rem; border-radius: var(--border-radius-lg);}.form-control-lg::file-selector-button{padding: 0.375rem 0.75rem; margin: -0.375rem -0.75rem; margin-inline-end: 0.75rem;}textarea.form-control{min-height: calc(1.5em + 0.625rem + calc(var(--border-width) * 2));}textarea.form-control-sm{min-height: calc(1.5em + 0.375rem + calc(var(--border-width) * 2));}textarea.form-control-lg{min-height: calc(1.5em + 0.75rem + calc(var(--border-width) * 2));}.form-control-color{width: 3rem; height: calc(1.5em + 0.625rem + calc(var(--border-width) * 2)); padding: 0.3125rem;}.form-control-color:not(:disabled):not([readonly]){cursor: pointer;}.form-control-color::-moz-color-swatch{border: 0 !important; border-radius: var(--border-radius);}.form-control-color::-webkit-color-swatch{border: 0 !important; border-radius: var(--border-radius);}.form-control-color.form-control-sm{height: calc(1.5em + 0.375rem + calc(var(--border-width) * 2));}.form-control-color.form-control-lg{height: calc(1.5em + 0.75rem + calc(var(--border-width) * 2));}.form-select{--form-select-bg-img: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e"); display: block; width: 100%; padding: 0.3125rem 1.875rem 0.3125rem 0.625rem; font-size: 0.875rem; font-weight: 400; line-height: 1.5; color: var(--body-color); -webkit-appearance: none; -moz-appearance: none; appearance: none; background-color: transparent; background-image: var(--form-select-bg-img), var(--form-select-bg-icon, none); background-repeat: no-repeat; background-position: right 0.625rem center; background-size: 16px 12px; border: var(--border-width) solid var(--border-color); border-radius: var(--border-radius); transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-select{transition: none;}}.form-select:focus{border-color: #71639e; outline: 0; box-shadow: 0 0 0 0.25rem rgba(113, 99, 158, 0.25);}.form-select[multiple], .form-select[size]:not([size="1"]){padding-right: 0.625rem; background-image: none;}.form-select:disabled{background-color: var(--secondary-bg);}.form-select:-moz-focusring{color: transparent; text-shadow: 0 0 0 var(--body-color);}.form-select-sm{padding-top: 0.1875rem; padding-bottom: 0.1875rem; padding-left: 0.5rem; font-size: 0.8125rem; border-radius: var(--border-radius-sm);}.form-select-lg{padding-top: 0.375rem; padding-bottom: 0.375rem; padding-left: 0.75rem; font-size: 1.09375rem; border-radius: var(--border-radius-lg);}.form-check{display: block; min-height: 1.3125rem; padding-left: 1.5em; margin-bottom: 0.125rem;}.form-check .form-check-input{float: left; margin-left: -1.5em;}.form-check-reverse{padding-right: 1.5em; padding-left: 0; text-align: right;}.form-check-reverse .form-check-input{float: right; margin-right: -1.5em; margin-left: 0;}.form-check-input{--form-check-bg: transparent; flex-shrink: 0; width: 1em; height: 1em; margin-top: 0.25em; vertical-align: top; -webkit-appearance: none; -moz-appearance: none; appearance: none; background-color: var(--form-check-bg); background-image: var(--form-check-bg-image); background-repeat: no-repeat; background-position: center; background-size: contain; border: var(--border-width) solid var(--border-color); print-color-adjust: exact;}.form-check-input[type="checkbox"]{border-radius: 0.25em;}.form-check-input[type="radio"]{border-radius: 50%;}.form-check-input:active{filter: brightness(90%);}.form-check-input:focus{border-color: #71639e; outline: 0; box-shadow: 0 0 0 0.25rem rgba(113, 99, 158, 0.25);}.form-check-input:checked{background-color: #71639e; border-color: #71639e;}.form-check-input:checked[type="checkbox"]{--form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23f8f9fa' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");}.form-check-input:checked[type="radio"]{--form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23f8f9fa'/%3e%3c/svg%3e");}.form-check-input[type="checkbox"]:indeterminate{background-color: #dddbe8; border-color: #dddbe8; --form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23212529' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");}.form-check-input:disabled{pointer-events: none; filter: none; opacity: 0.5;}.form-check-input[disabled] ~ .form-check-label, .form-check-input:disabled ~ .form-check-label{cursor: default; opacity: 0.5;}.form-switch{padding-left: 2.5em;}.form-switch .form-check-input{--form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e"); width: 2em; margin-left: -2.5em; background-image: var(--form-switch-bg); background-position: left center; border-radius: 2em; transition: background-position 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-switch .form-check-input{transition: none;}}.form-switch .form-check-input:focus{--form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%2840, 167, 69, 0.5%29'/%3e%3c/svg%3e");}.form-switch .form-check-input:checked{background-position: right center; --form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23FFFFFF'/%3e%3c/svg%3e");}.form-switch.form-check-reverse{padding-right: 2.5em; padding-left: 0;}.form-switch.form-check-reverse .form-check-input{margin-right: -2.5em; margin-left: 0;}.form-check-inline{display: inline-block; margin-right: 1rem;}.btn-check{position: absolute; clip: rect(0, 0, 0, 0); pointer-events: none;}.btn-check[disabled] + .btn, .btn-check:disabled + .btn{pointer-events: none; filter: none; opacity: 0.5;}.form-range{width: 100%; height: 1.5rem; padding: 0; -webkit-appearance: none; -moz-appearance: none; appearance: none; background-color: transparent;}.form-range:focus{outline: 0;}.form-range:focus::-webkit-slider-thumb{box-shadow: 0 0 0 1px #71639e;}.form-range:focus::-moz-range-thumb{box-shadow: 0 0 0 1px #71639e;}.form-range::-moz-focus-outer{border: 0;}.form-range::-webkit-slider-thumb{width: 1rem; height: 1rem; margin-top: -0.25rem; -webkit-appearance: none; -moz-appearance: none; appearance: none; background-color: #71639e; border: 0; border-radius: 1rem; transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-range::-webkit-slider-thumb{transition: none;}}.form-range::-webkit-slider-thumb:active{background-color: #f5f4f8;}.form-range::-webkit-slider-runnable-track{width: 100%; height: 0.5rem; color: transparent; cursor: pointer; background-color: var(--secondary-bg); border-color: transparent; border-radius: 1rem;}.form-range::-moz-range-thumb{width: 1rem; height: 1rem; -webkit-appearance: none; -moz-appearance: none; appearance: none; background-color: #71639e; border: 0; border-radius: 1rem; transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-range::-moz-range-thumb{transition: none;}}.form-range::-moz-range-thumb:active{background-color: #f5f4f8;}.form-range::-moz-range-track{width: 100%; height: 0.5rem; color: transparent; cursor: pointer; background-color: var(--secondary-bg); border-color: transparent; border-radius: 1rem;}.form-range:disabled{pointer-events: none;}.form-range:disabled::-webkit-slider-thumb{background-color: var(--secondary-color);}.form-range:disabled::-moz-range-thumb{background-color: var(--secondary-color);}.form-floating{position: relative;}.form-floating > .form-control, .form-floating > .form-control-plaintext, .form-floating > .form-select{height: calc(3.5rem + calc(var(--border-width) * 2)); min-height: calc(3.5rem + calc(var(--border-width) * 2)); line-height: 1.25;}.form-floating > label{position: absolute; top: 0; left: 0; z-index: 2; height: 100%; padding: 1rem 0.625rem; overflow: hidden; text-align: start; text-overflow: ellipsis; white-space: nowrap; pointer-events: none; border: var(--border-width) solid transparent; transform-origin: 0 0; transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-floating > label{transition: none;}}.form-floating > .form-control, .form-floating > .form-control-plaintext{padding: 1rem 0.625rem;}.form-floating > .form-control::placeholder, .form-floating > .form-control-plaintext::placeholder{color: transparent;}.form-floating > .form-control:focus, .form-floating > .form-control:not(:placeholder-shown), .form-floating > .form-control-plaintext:focus, .form-floating > .form-control-plaintext:not(:placeholder-shown){padding-top: 1.625rem; padding-bottom: 0.625rem;}.form-floating > .form-control:-webkit-autofill, .form-floating > .form-control-plaintext:-webkit-autofill{padding-top: 1.625rem; padding-bottom: 0.625rem;}.form-floating > .form-select{padding-top: 1.625rem; padding-bottom: 0.625rem;}.form-floating > .form-control:focus ~ label, .form-floating > .form-control:not(:placeholder-shown) ~ label, .form-floating > .form-control-plaintext ~ label, .form-floating > .form-select ~ label{color: rgba(var(--body-color-rgb), 0.65); transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);}.form-floating > .form-control:focus ~ label::after, .form-floating > .form-control:not(:placeholder-shown) ~ label::after, .form-floating > .form-control-plaintext ~ label::after, .form-floating > .form-select ~ label::after{position: absolute; inset: 1rem 0.3125rem; z-index: -1; height: 1.5em; content: ""; background-color: transparent; border-radius: var(--border-radius);}.form-floating > .form-control:-webkit-autofill ~ label{color: rgba(var(--body-color-rgb), 0.65); transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);}.form-floating > .form-control-plaintext ~ label{border-width: var(--border-width) 0;}.form-floating > :disabled ~ label, .form-floating > .form-control:disabled ~ label{color: #6c757d;}.form-floating > :disabled ~ label::after, .form-floating > .form-control:disabled ~ label::after{background-color: var(--secondary-bg);}.input-group{position: relative; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; align-items: stretch; width: 100%;}.input-group > .form-control, .input-group > .form-select, .input-group > .form-floating{position: relative; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; width: 1%; min-width: 0;}.input-group > .form-control:focus, .input-group > .form-select:focus, .input-group > .form-floating:focus-within{z-index: 5;}.input-group .btn{position: relative; z-index: 2;}.input-group .btn:focus{z-index: 5;}.input-group-text{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; padding: 0.3125rem 0.625rem; font-size: 0.875rem; font-weight: 400; line-height: 1.5; color: var(--body-color); text-align: center; white-space: nowrap; background-color: var(--tertiary-bg); border: var(--border-width) solid var(--border-color); border-radius: var(--border-radius);}.input-group-lg > .form-control, .input-group-lg > .form-select, .input-group-lg > .input-group-text, .input-group-lg > .btn{padding: 0.375rem 0.75rem; font-size: 1.09375rem; border-radius: var(--border-radius-lg);}.input-group-sm > .form-control, .input-group-sm > .form-select, .input-group-sm > .input-group-text, .input-group-sm > .btn{padding: 0.1875rem 0.5rem; font-size: 0.8125rem; border-radius: var(--border-radius-sm);}.input-group-lg > .form-select, .input-group-sm > .form-select{padding-right: 2.5rem;}.input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating), .input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n + 3), .input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-control, .input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-select{border-top-right-radius: 0; border-bottom-right-radius: 0;}.input-group.has-validation > :nth-last-child(n + 3):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating), .input-group.has-validation > .dropdown-toggle:nth-last-child(n + 4), .input-group.has-validation > .form-floating:nth-last-child(n + 3) > .form-control, .input-group.has-validation > .form-floating:nth-last-child(n + 3) > .form-select{border-top-right-radius: 0; border-bottom-right-radius: 0;}.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback){margin-left: calc(var(--border-width) * -1); border-top-left-radius: 0; border-bottom-left-radius: 0;}.input-group > .form-floating:not(:first-child) > .form-control, .input-group > .form-floating:not(:first-child) > .form-select{border-top-left-radius: 0; border-bottom-left-radius: 0;}.valid-feedback{display: none; width: 100%; margin-top: 0.25rem; font-size: 0.8125rem; color: var(--form-valid-color);}.valid-tooltip{position: absolute; top: 100%; z-index: 5; display: none; max-width: 100%; padding: 4px 8px; margin-top: .1rem; font-size: 0.8125rem; color: #fff; background-color: var(--success); border-radius: var(--border-radius);}.was-validated :valid ~ .valid-feedback, .was-validated :valid ~ .valid-tooltip, .is-valid ~ .valid-feedback, .is-valid ~ .valid-tooltip{display: block;}.was-validated .form-control:valid, .form-control.is-valid{border-color: var(--form-valid-border-color); padding-right: calc(1.5em + 0.625rem); background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e"); background-repeat: no-repeat; background-position: right calc(0.375em + 0.15625rem) center; background-size: calc(0.75em + 0.3125rem) calc(0.75em + 0.3125rem);}.was-validated .form-control:valid:focus, .form-control.is-valid:focus{border-color: var(--form-valid-border-color); box-shadow: 0 0 0 0.25rem rgba(var(--success-rgb), 0.25);}.was-validated textarea.form-control:valid, textarea.form-control.is-valid{padding-right: calc(1.5em + 0.625rem); background-position: top calc(0.375em + 0.15625rem) right calc(0.375em + 0.15625rem);}.was-validated .form-select:valid, .form-select.is-valid{border-color: var(--form-valid-border-color);}.was-validated .form-select:valid:not([multiple]):not([size]), .was-validated .form-select:valid:not([multiple])[size="1"], .form-select.is-valid:not([multiple]):not([size]), .form-select.is-valid:not([multiple])[size="1"]{--form-select-bg-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e"); padding-right: 3.4375rem; background-position: right 0.625rem center, center right 1.875rem; background-size: 16px 12px, calc(0.75em + 0.3125rem) calc(0.75em + 0.3125rem);}.was-validated .form-select:valid:focus, .form-select.is-valid:focus{border-color: var(--form-valid-border-color); box-shadow: 0 0 0 0.25rem rgba(var(--success-rgb), 0.25);}.was-validated .form-control-color:valid, .form-control-color.is-valid{width: calc(3rem + calc(1.5em + 0.625rem));}.was-validated .form-check-input:valid, .form-check-input.is-valid{border-color: var(--form-valid-border-color);}.was-validated .form-check-input:valid:checked, .form-check-input.is-valid:checked{background-color: var(--form-valid-color);}.was-validated .form-check-input:valid:focus, .form-check-input.is-valid:focus{box-shadow: 0 0 0 0.25rem rgba(var(--success-rgb), 0.25);}.was-validated .form-check-input:valid ~ .form-check-label, .form-check-input.is-valid ~ .form-check-label{color: var(--form-valid-color);}.form-check-inline .form-check-input ~ .valid-feedback{margin-left: .5em;}.was-validated .input-group > .form-control:not(:focus):valid, .input-group > .form-control:not(:focus).is-valid, .was-validated .input-group > .form-select:not(:focus):valid, .input-group > .form-select:not(:focus).is-valid, .was-validated .input-group > .form-floating:not(:focus-within):valid, .input-group > .form-floating:not(:focus-within).is-valid{z-index: 3;}.invalid-feedback{display: none; width: 100%; margin-top: 0.25rem; font-size: 0.8125rem; color: var(--form-invalid-color);}.invalid-tooltip{position: absolute; top: 100%; z-index: 5; display: none; max-width: 100%; padding: 4px 8px; margin-top: .1rem; font-size: 0.8125rem; color: #fff; background-color: var(--danger); border-radius: var(--border-radius);}.was-validated :invalid ~ .invalid-feedback, .was-validated :invalid ~ .invalid-tooltip, .is-invalid ~ .invalid-feedback, .is-invalid ~ .invalid-tooltip{display: block;}.was-validated .form-control:invalid, .form-control.is-invalid{border-color: var(--form-invalid-border-color); padding-right: calc(1.5em + 0.625rem); background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e"); background-repeat: no-repeat; background-position: right calc(0.375em + 0.15625rem) center; background-size: calc(0.75em + 0.3125rem) calc(0.75em + 0.3125rem);}.was-validated .form-control:invalid:focus, .form-control.is-invalid:focus{border-color: var(--form-invalid-border-color); box-shadow: 0 0 0 0.25rem rgba(var(--danger-rgb), 0.25);}.was-validated textarea.form-control:invalid, textarea.form-control.is-invalid{padding-right: calc(1.5em + 0.625rem); background-position: top calc(0.375em + 0.15625rem) right calc(0.375em + 0.15625rem);}.was-validated .form-select:invalid, .form-select.is-invalid{border-color: var(--form-invalid-border-color);}.was-validated .form-select:invalid:not([multiple]):not([size]), .was-validated .form-select:invalid:not([multiple])[size="1"], .form-select.is-invalid:not([multiple]):not([size]), .form-select.is-invalid:not([multiple])[size="1"]{--form-select-bg-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e"); padding-right: 3.4375rem; background-position: right 0.625rem center, center right 1.875rem; background-size: 16px 12px, calc(0.75em + 0.3125rem) calc(0.75em + 0.3125rem);}.was-validated .form-select:invalid:focus, .form-select.is-invalid:focus{border-color: var(--form-invalid-border-color); box-shadow: 0 0 0 0.25rem rgba(var(--danger-rgb), 0.25);}.was-validated .form-control-color:invalid, .form-control-color.is-invalid{width: calc(3rem + calc(1.5em + 0.625rem));}.was-validated .form-check-input:invalid, .form-check-input.is-invalid{border-color: var(--form-invalid-border-color);}.was-validated .form-check-input:invalid:checked, .form-check-input.is-invalid:checked{background-color: var(--form-invalid-color);}.was-validated .form-check-input:invalid:focus, .form-check-input.is-invalid:focus{box-shadow: 0 0 0 0.25rem rgba(var(--danger-rgb), 0.25);}.was-validated .form-check-input:invalid ~ .form-check-label, .form-check-input.is-invalid ~ .form-check-label{color: var(--form-invalid-color);}.form-check-inline .form-check-input ~ .invalid-feedback{margin-left: .5em;}.was-validated .input-group > .form-control:not(:focus):invalid, .input-group > .form-control:not(:focus).is-invalid, .was-validated .input-group > .form-select:not(:focus):invalid, .input-group > .form-select:not(:focus).is-invalid, .was-validated .input-group > .form-floating:not(:focus-within):invalid, .input-group > .form-floating:not(:focus-within).is-invalid{z-index: 4;}.btn{--btn-padding-x: 0.625rem; --btn-padding-y: 0.3125rem; --btn-font-family: ; --btn-font-size: 0.875rem; --btn-font-weight: 500; --btn-line-height: 1.5; --btn-color: var(--body-color); --btn-bg: transparent; --btn-border-width: var(--border-width); --btn-border-color: transparent; --btn-border-radius: var(--border-radius); --btn-hover-border-color: transparent; --btn-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075); --btn-disabled-opacity: 0.5; --btn-focus-box-shadow: 0 0 0 0.25rem rgba(var(--btn-focus-shadow-rgb), .5); display: inline-block; padding: var(--btn-padding-y) var(--btn-padding-x); font-family: var(--btn-font-family); font-size: var(--btn-font-size); font-weight: var(--btn-font-weight); line-height: var(--btn-line-height); color: var(--btn-color); text-align: center; vertical-align: middle; cursor: pointer; user-select: none; border: var(--btn-border-width) solid var(--btn-border-color); border-radius: var(--btn-border-radius); background-color: var(--btn-bg); transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.btn{transition: none;}}.btn:hover{color: var(--btn-hover-color); background-color: var(--btn-hover-bg); border-color: var(--btn-hover-border-color);}.btn-check + .btn:hover{color: var(--btn-color); background-color: var(--btn-bg); border-color: var(--btn-border-color);}.btn:focus-visible{color: var(--btn-hover-color); background-color: var(--btn-hover-bg); border-color: var(--btn-hover-border-color); outline: 0; box-shadow: var(--btn-focus-box-shadow);}.btn-check:focus-visible + .btn{border-color: var(--btn-hover-border-color); outline: 0; box-shadow: var(--btn-focus-box-shadow);}.btn-check:checked + .btn, :not(.btn-check) + .btn:active, .btn:first-child:active, .btn.active, .btn.show{color: var(--btn-active-color); background-color: var(--btn-active-bg); border-color: var(--btn-active-border-color);}.btn-check:checked + .btn:focus-visible, :not(.btn-check) + .btn:active:focus-visible, .btn:first-child:active:focus-visible, .btn.active:focus-visible, .btn.show:focus-visible{box-shadow: var(--btn-focus-box-shadow);}.btn-check:checked:focus-visible + .btn{box-shadow: var(--btn-focus-box-shadow);}.btn:disabled, .btn.disabled, fieldset:disabled .btn{color: var(--btn-disabled-color); pointer-events: none; background-color: var(--btn-disabled-bg); border-color: var(--btn-disabled-border-color); opacity: var(--btn-disabled-opacity);}.btn-link{--btn-font-weight: 400; --btn-color: var(--link-color); --btn-bg: transparent; --btn-border-color: transparent; --btn-hover-color: var(--link-hover-color); --btn-hover-border-color: transparent; --btn-active-color: var(--link-hover-color); --btn-active-border-color: transparent; --btn-disabled-color: #6c757d; --btn-disabled-border-color: transparent; --btn-box-shadow: 0 0 0 #000; --btn-focus-shadow-rgb: 125, 114, 160; text-decoration: none;}.btn-link:hover, .btn-link:focus-visible{text-decoration: none;}.btn-link:focus-visible{color: var(--btn-color);}.btn-link:hover{color: var(--btn-hover-color);}.btn-lg, .btn-group-lg > .btn{--btn-padding-y: 0.375rem; --btn-padding-x: 0.75rem; --btn-font-size: 1.09375rem; --btn-border-radius: 0.25rem;}.btn-sm, .btn-group-sm > .btn{--btn-padding-y: 0.1875rem; --btn-padding-x: 0.5rem; --btn-font-size: 0.8125rem; --btn-border-radius: var(--border-radius-sm);}.fade{transition: opacity 0.15s linear;}@media (prefers-reduced-motion: reduce){.fade{transition: none;}}.fade:not(.show){opacity: 0;}.collapse:not(.show){display: none;}.collapsing{height: 0; overflow: hidden; transition: height 0.35s ease;}@media (prefers-reduced-motion: reduce){.collapsing{transition: none;}}.collapsing.collapse-horizontal{width: 0; height: auto; transition: width 0.35s ease;}@media (prefers-reduced-motion: reduce){.collapsing.collapse-horizontal{transition: none;}}.dropup, .dropend, .dropdown, .dropstart, .dropup-center, .dropdown-center{position: relative;}.dropdown-toggle{white-space: nowrap;}.dropdown-toggle::after{display: inline-block; margin-left: 3.4px; vertical-align: 3.4px; content: ""; border-top: 4px solid; border-right: 4px solid transparent; border-bottom: 0; border-left: 4px solid transparent;}.dropdown-toggle:empty::after{margin-left: 0;}.dropdown-menu{--dropdown-zindex: 1000; --dropdown-min-width: 10rem; --dropdown-padding-x: 0; --dropdown-padding-y: 0.5rem; --dropdown-spacer: 0.125rem; --dropdown-font-size: 0.875rem; --dropdown-color: var(--body-color); --dropdown-bg: var(--body-bg); --dropdown-border-color: #dee2e6; --dropdown-border-radius: var(--border-radius); --dropdown-border-width: var(--border-width); --dropdown-inner-border-radius: calc(var(--border-radius) - var(--border-width)); --dropdown-divider-bg: #dee2e6; --dropdown-divider-margin-y: 8px; --dropdown-box-shadow: var(--box-shadow); --dropdown-link-color: #495057; --dropdown-link-hover-color: #212529; --dropdown-link-hover-bg: rgba(0, 0, 0, 0.08); --dropdown-link-active-color: #212529; --dropdown-link-active-bg: transparent; --dropdown-link-disabled-color: rgba(73, 80, 87, 0.76); --dropdown-item-padding-x: 20px; --dropdown-item-padding-y: 3px; --dropdown-header-color: #6c757d; --dropdown-header-padding-x: 20px; --dropdown-header-padding-y: 0.5rem; position: absolute; z-index: var(--dropdown-zindex); display: none; min-width: var(--dropdown-min-width); padding: var(--dropdown-padding-y) var(--dropdown-padding-x); margin: 0; font-size: var(--dropdown-font-size); color: var(--dropdown-color); text-align: left; list-style: none; background-color: var(--dropdown-bg); background-clip: padding-box; border: var(--dropdown-border-width) solid var(--dropdown-border-color); border-radius: var(--dropdown-border-radius);}.dropdown-menu[data-bs-popper]{top: 100%; left: 0; margin-top: var(--dropdown-spacer);}.dropdown-menu-start{--bs-position: start;}.dropdown-menu-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-end{--bs-position: end;}.dropdown-menu-end[data-bs-popper]{right: 0; left: auto;}@media (min-width: 576px){.dropdown-menu-sm-start{--bs-position: start;}.dropdown-menu-sm-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-sm-end{--bs-position: end;}.dropdown-menu-sm-end[data-bs-popper]{right: 0; left: auto;}}@media (min-width: 768px){.dropdown-menu-md-start{--bs-position: start;}.dropdown-menu-md-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-md-end{--bs-position: end;}.dropdown-menu-md-end[data-bs-popper]{right: 0; left: auto;}}@media (min-width: 992px){.dropdown-menu-lg-start{--bs-position: start;}.dropdown-menu-lg-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-lg-end{--bs-position: end;}.dropdown-menu-lg-end[data-bs-popper]{right: 0; left: auto;}}@media (min-width: 1200px){.dropdown-menu-xl-start{--bs-position: start;}.dropdown-menu-xl-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-xl-end{--bs-position: end;}.dropdown-menu-xl-end[data-bs-popper]{right: 0; left: auto;}}@media (min-width: 1400px){.dropdown-menu-xxl-start{--bs-position: start;}.dropdown-menu-xxl-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-xxl-end{--bs-position: end;}.dropdown-menu-xxl-end[data-bs-popper]{right: 0; left: auto;}}.dropup .dropdown-menu[data-bs-popper]{top: auto; bottom: 100%; margin-top: 0; margin-bottom: var(--dropdown-spacer);}.dropup .dropdown-toggle::after{display: inline-block; margin-left: 3.4px; vertical-align: 3.4px; content: ""; border-top: 0; border-right: 4px solid transparent; border-bottom: 4px solid; border-left: 4px solid transparent;}.dropup .dropdown-toggle:empty::after{margin-left: 0;}.dropend .dropdown-menu[data-bs-popper]{top: 0; right: auto; left: 100%; margin-top: 0; margin-left: var(--dropdown-spacer);}.dropend .dropdown-toggle::after{display: inline-block; margin-left: 3.4px; vertical-align: 3.4px; content: ""; border-top: 4px solid transparent; border-right: 0; border-bottom: 4px solid transparent; border-left: 4px solid;}.dropend .dropdown-toggle:empty::after{margin-left: 0;}.dropend .dropdown-toggle::after{vertical-align: 0;}.dropstart .dropdown-menu[data-bs-popper]{top: 0; right: 100%; left: auto; margin-top: 0; margin-right: var(--dropdown-spacer);}.dropstart .dropdown-toggle::after{display: inline-block; margin-left: 3.4px; vertical-align: 3.4px; content: "";}.dropstart .dropdown-toggle::after{display: none;}.dropstart .dropdown-toggle::before{display: inline-block; margin-right: 3.4px; vertical-align: 3.4px; content: ""; border-top: 4px solid transparent; border-right: 4px solid; border-bottom: 4px solid transparent;}.dropstart .dropdown-toggle:empty::after{margin-left: 0;}.dropstart .dropdown-toggle::before{vertical-align: 0;}.dropdown-divider{height: 0; margin: var(--dropdown-divider-margin-y) 0; overflow: hidden; border-top: 1px solid var(--dropdown-divider-bg); opacity: 1;}.dropdown-item{display: block; width: 100%; padding: var(--dropdown-item-padding-y) var(--dropdown-item-padding-x); clear: both; font-weight: 400; color: var(--dropdown-link-color); text-align: inherit; white-space: nowrap; background-color: transparent; border: 0; border-radius: var(--dropdown-item-border-radius, 0);}.dropdown-item:hover, .dropdown-item:focus{color: var(--dropdown-link-hover-color); background-color: var(--dropdown-link-hover-bg);}.dropdown-item.active, .dropdown-item:active{color: var(--dropdown-link-active-color); text-decoration: none; background-color: var(--dropdown-link-active-bg);}.dropdown-item.disabled, .dropdown-item:disabled{color: var(--dropdown-link-disabled-color); pointer-events: none; background-color: transparent;}.dropdown-menu.show{display: block;}.dropdown-header{display: block; padding: var(--dropdown-header-padding-y) var(--dropdown-header-padding-x); margin-bottom: 0; font-size: 0.8125rem; color: var(--dropdown-header-color); white-space: nowrap;}.dropdown-item-text{display: block; padding: var(--dropdown-item-padding-y) var(--dropdown-item-padding-x); color: var(--dropdown-link-color);}.dropdown-menu-dark{--dropdown-color: #dee2e6; --dropdown-bg: #343a40; --dropdown-border-color: #dee2e6; --dropdown-box-shadow: ; --dropdown-link-color: #dee2e6; --dropdown-link-hover-color: #FFFFFF; --dropdown-divider-bg: #dee2e6; --dropdown-link-hover-bg: rgba(255, 255, 255, 0.15); --dropdown-link-active-color: #212529; --dropdown-link-active-bg: transparent; --dropdown-link-disabled-color: #adb5bd; --dropdown-header-color: #adb5bd;}.btn-group, .btn-group-vertical{position: relative; display: -webkit-inline-box; display: -webkit-inline-flex; display: inline-flex; vertical-align: middle;}.btn-group > .btn, .btn-group-vertical > .btn{position: relative; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;}.btn-group > .btn-check:checked + .btn, .btn-group > .btn-check:focus + .btn, .btn-group > .btn:hover, .btn-group > .btn:focus, .btn-group > .btn:active, .btn-group > .btn.active, .btn-group-vertical > .btn-check:checked + .btn, .btn-group-vertical > .btn-check:focus + .btn, .btn-group-vertical > .btn:hover, .btn-group-vertical > .btn:focus, .btn-group-vertical > .btn:active, .btn-group-vertical > .btn.active{z-index: 1;}.btn-toolbar{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; -webkit-box-pack: start; justify-content: flex-start;}.btn-toolbar .input-group{width: auto;}.btn-group{border-radius: var(--border-radius);}.btn-group > :not(.btn-check:first-child) + .btn, .btn-group > .btn-group:not(:first-child){margin-left: calc(var(--border-width) * -1);}.btn-group > .btn:not(:last-child):not(.dropdown-toggle), .btn-group > .btn.dropdown-toggle-split:first-child, .btn-group > .btn-group:not(:last-child) > .btn{border-top-right-radius: 0; border-bottom-right-radius: 0;}.btn-group > .btn:nth-child(n + 3), .btn-group > :not(.btn-check) + .btn, .btn-group > .btn-group:not(:first-child) > .btn{border-top-left-radius: 0; border-bottom-left-radius: 0;}.dropdown-toggle-split{padding-right: 0.46875rem; padding-left: 0.46875rem;}.dropdown-toggle-split::after, .dropup .dropdown-toggle-split::after, .dropend .dropdown-toggle-split::after{margin-left: 0;}.dropstart .dropdown-toggle-split::before{margin-right: 0;}.btn-sm + .dropdown-toggle-split, .btn-group-sm > .btn + .dropdown-toggle-split{padding-right: 0.375rem; padding-left: 0.375rem;}.btn-lg + .dropdown-toggle-split, .btn-group-lg > .btn + .dropdown-toggle-split{padding-right: 0.5625rem; padding-left: 0.5625rem;}.btn-group-vertical{-webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; align-items: flex-start; justify-content: center;}.btn-group-vertical > .btn, .btn-group-vertical > .btn-group{width: 100%;}.btn-group-vertical > .btn:not(:first-child), .btn-group-vertical > .btn-group:not(:first-child){margin-top: calc(var(--border-width) * -1);}.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle), .btn-group-vertical > .btn-group:not(:last-child) > .btn{border-bottom-right-radius: 0; border-bottom-left-radius: 0;}.btn-group-vertical > .btn ~ .btn, .btn-group-vertical > .btn-group:not(:first-child) > .btn{border-top-left-radius: 0; border-top-right-radius: 0;}.nav{--nav-link-padding-x: 1rem; --nav-link-padding-y: 0.5rem; --nav-link-font-weight: ; --nav-link-color: var(--link-color); --nav-link-hover-color: var(--link-hover-color); --nav-link-disabled-color: var(--secondary-color); display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; padding-left: 0; margin-bottom: 0; list-style: none;}.nav-link{display: block; padding: var(--nav-link-padding-y) var(--nav-link-padding-x); font-size: var(--nav-link-font-size); font-weight: var(--nav-link-font-weight); color: var(--nav-link-color); background: none; border: 0; transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.nav-link{transition: none;}}.nav-link:hover, .nav-link:focus{color: var(--nav-link-hover-color);}.nav-link:focus-visible{outline: 0; box-shadow: 0 0 0 0.25rem rgba(113, 99, 158, 0.25);}.nav-link.disabled, .nav-link:disabled{color: var(--nav-link-disabled-color); pointer-events: none; cursor: default;}.nav-tabs{--nav-tabs-border-width: var(--border-width); --nav-tabs-border-color: var(--border-color); --nav-tabs-border-radius: var(--border-radius); --nav-tabs-link-hover-border-color: var(--secondary-bg) var(--secondary-bg) var(--border-color); --nav-tabs-link-active-color: var(--emphasis-color); --nav-tabs-link-active-bg: #FFFFFF; --nav-tabs-link-active-border-color: var(--border-color) var(--border-color) #FFFFFF; border-bottom: var(--nav-tabs-border-width) solid var(--nav-tabs-border-color);}.nav-tabs .nav-link{margin-bottom: calc(-1 * var(--nav-tabs-border-width)); border: var(--nav-tabs-border-width) solid transparent; border-top-left-radius: var(--nav-tabs-border-radius); border-top-right-radius: var(--nav-tabs-border-radius);}.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus{isolation: isolate; border-color: var(--nav-tabs-link-hover-border-color);}.nav-tabs .nav-link.active, .nav-tabs .nav-item.show .nav-link{color: var(--nav-tabs-link-active-color); background-color: var(--nav-tabs-link-active-bg); border-color: var(--nav-tabs-link-active-border-color);}.nav-tabs .dropdown-menu{margin-top: calc(-1 * var(--nav-tabs-border-width)); border-top-left-radius: 0; border-top-right-radius: 0;}.nav-pills{--nav-pills-border-radius: 0; --nav-pills-link-active-color: #FFFFFF; --nav-pills-link-active-bg: #71639e;}.nav-pills .nav-link{border-radius: var(--nav-pills-border-radius);}.nav-pills .nav-link.active, .nav-pills .show > .nav-link{color: var(--nav-pills-link-active-color); background-color: var(--nav-pills-link-active-bg);}.nav-underline{--nav-underline-gap: 1rem; --nav-underline-border-width: 0.125rem; --nav-underline-link-active-color: var(--emphasis-color); gap: var(--nav-underline-gap);}.nav-underline .nav-link{padding-right: 0; padding-left: 0; border-bottom: var(--nav-underline-border-width) solid transparent;}.nav-underline .nav-link:hover, .nav-underline .nav-link:focus{border-bottom-color: currentcolor;}.nav-underline .nav-link.active, .nav-underline .show > .nav-link{font-weight: 500; color: var(--nav-underline-link-active-color); border-bottom-color: currentcolor;}.nav-fill > .nav-link, .nav-fill .nav-item{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; text-align: center;}.nav-justified > .nav-link, .nav-justified .nav-item{flex-basis: 0; flex-grow: 1; text-align: center;}.nav-fill .nav-item .nav-link, .nav-justified .nav-item .nav-link{width: 100%;}.tab-content > .tab-pane{display: none;}.tab-content > .active{display: block;}.navbar{--navbar-padding-x: 0; --navbar-padding-y: 8px; --navbar-color: rgba(var(--emphasis-color-rgb), 0.65); --navbar-hover-color: rgba(var(--emphasis-color-rgb), 0.8); --navbar-disabled-color: rgba(var(--emphasis-color-rgb), 0.3); --navbar-active-color: rgba(var(--emphasis-color-rgb), 1); --navbar-brand-padding-y: 0.3359375rem; --navbar-brand-margin-end: 1rem; --navbar-brand-font-size: 1.09375rem; --navbar-brand-color: rgba(var(--emphasis-color-rgb), 1); --navbar-brand-hover-color: rgba(var(--emphasis-color-rgb), 1); --navbar-nav-link-padding-x: 0.5rem; --navbar-toggler-padding-y: 0.25rem; --navbar-toggler-padding-x: 0.75rem; --navbar-toggler-font-size: 1.09375rem; --navbar-toggler-icon-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2873, 80, 87, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e"); --navbar-toggler-border-color: rgba(var(--emphasis-color-rgb), 0.15); --navbar-toggler-border-radius: var(--border-radius); --navbar-toggler-focus-width: 0.25rem; --navbar-toggler-transition: box-shadow 0.15s ease-in-out; position: relative; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; align-items: center; justify-content: space-between; padding: var(--navbar-padding-y) var(--navbar-padding-x);}.navbar > .container, .navbar > .container-fluid, .navbar > .container-sm, .navbar > .container-md, .navbar > .container-lg, .navbar > .container-xl, .navbar > .container-xxl{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: inherit; flex-wrap: inherit; align-items: center; justify-content: space-between;}.navbar-brand{padding-top: var(--navbar-brand-padding-y); padding-bottom: var(--navbar-brand-padding-y); margin-right: var(--navbar-brand-margin-end); font-size: var(--navbar-brand-font-size); color: var(--navbar-brand-color); white-space: nowrap;}.navbar-brand:hover, .navbar-brand:focus{color: var(--navbar-brand-hover-color);}.navbar-nav{--nav-link-padding-x: 0; --nav-link-padding-y: 0.5rem; --nav-link-font-weight: ; --nav-link-color: var(--navbar-color); --nav-link-hover-color: var(--navbar-hover-color); --nav-link-disabled-color: var(--navbar-disabled-color); display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; padding-left: 0; margin-bottom: 0; list-style: none;}.navbar-nav .nav-link.active, .navbar-nav .nav-link.show{color: var(--navbar-active-color);}.navbar-nav .dropdown-menu{position: static;}.navbar-text{padding-top: 0.5rem; padding-bottom: 0.5rem; color: var(--navbar-color);}.navbar-text a, .navbar-text a:hover, .navbar-text a:focus{color: var(--navbar-active-color);}.navbar-collapse{flex-basis: 100%; flex-grow: 1; align-items: center;}.navbar-toggler{padding: var(--navbar-toggler-padding-y) var(--navbar-toggler-padding-x); font-size: var(--navbar-toggler-font-size); line-height: 1; color: var(--navbar-color); background-color: transparent; border: var(--border-width) solid var(--navbar-toggler-border-color); border-radius: var(--navbar-toggler-border-radius); transition: var(--navbar-toggler-transition);}@media (prefers-reduced-motion: reduce){.navbar-toggler{transition: none;}}.navbar-toggler:hover{text-decoration: none;}.navbar-toggler:focus{text-decoration: none; outline: 0; box-shadow: 0 0 0 var(--navbar-toggler-focus-width);}.navbar-toggler-icon{display: inline-block; width: 1.5em; height: 1.5em; vertical-align: middle; background-image: var(--navbar-toggler-icon-bg); background-repeat: no-repeat; background-position: center; background-size: 100%;}.navbar-nav-scroll{max-height: var(--scroll-height, 75vh); overflow-y: auto;}@media (min-width: 576px){.navbar-expand-sm{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-sm .navbar-nav{flex-direction: row;}.navbar-expand-sm .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-sm .navbar-nav .nav-link{padding-right: var(--navbar-nav-link-padding-x); padding-left: var(--navbar-nav-link-padding-x);}.navbar-expand-sm .navbar-nav-scroll{overflow: visible;}.navbar-expand-sm .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-sm .navbar-toggler{display: none;}.navbar-expand-sm .offcanvas{position: static; z-index: auto; flex-grow: 1; width: auto !important; height: auto !important; visibility: visible !important; background-color: transparent !important; border: 0 !important; transform: none !important; transition: none;}.navbar-expand-sm .offcanvas .offcanvas-header{display: none;}.navbar-expand-sm .offcanvas .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}}@media (min-width: 768px){.navbar-expand-md{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-md .navbar-nav{flex-direction: row;}.navbar-expand-md .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-md .navbar-nav .nav-link{padding-right: var(--navbar-nav-link-padding-x); padding-left: var(--navbar-nav-link-padding-x);}.navbar-expand-md .navbar-nav-scroll{overflow: visible;}.navbar-expand-md .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-md .navbar-toggler{display: none;}.navbar-expand-md .offcanvas{position: static; z-index: auto; flex-grow: 1; width: auto !important; height: auto !important; visibility: visible !important; background-color: transparent !important; border: 0 !important; transform: none !important; transition: none;}.navbar-expand-md .offcanvas .offcanvas-header{display: none;}.navbar-expand-md .offcanvas .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}}@media (min-width: 992px){.navbar-expand-lg{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-lg .navbar-nav{flex-direction: row;}.navbar-expand-lg .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-lg .navbar-nav .nav-link{padding-right: var(--navbar-nav-link-padding-x); padding-left: var(--navbar-nav-link-padding-x);}.navbar-expand-lg .navbar-nav-scroll{overflow: visible;}.navbar-expand-lg .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-lg .navbar-toggler{display: none;}.navbar-expand-lg .offcanvas{position: static; z-index: auto; flex-grow: 1; width: auto !important; height: auto !important; visibility: visible !important; background-color: transparent !important; border: 0 !important; transform: none !important; transition: none;}.navbar-expand-lg .offcanvas .offcanvas-header{display: none;}.navbar-expand-lg .offcanvas .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}}@media (min-width: 1200px){.navbar-expand-xl{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-xl .navbar-nav{flex-direction: row;}.navbar-expand-xl .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-xl .navbar-nav .nav-link{padding-right: var(--navbar-nav-link-padding-x); padding-left: var(--navbar-nav-link-padding-x);}.navbar-expand-xl .navbar-nav-scroll{overflow: visible;}.navbar-expand-xl .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-xl .navbar-toggler{display: none;}.navbar-expand-xl .offcanvas{position: static; z-index: auto; flex-grow: 1; width: auto !important; height: auto !important; visibility: visible !important; background-color: transparent !important; border: 0 !important; transform: none !important; transition: none;}.navbar-expand-xl .offcanvas .offcanvas-header{display: none;}.navbar-expand-xl .offcanvas .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}}@media (min-width: 1400px){.navbar-expand-xxl{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-xxl .navbar-nav{flex-direction: row;}.navbar-expand-xxl .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-xxl .navbar-nav .nav-link{padding-right: var(--navbar-nav-link-padding-x); padding-left: var(--navbar-nav-link-padding-x);}.navbar-expand-xxl .navbar-nav-scroll{overflow: visible;}.navbar-expand-xxl .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-xxl .navbar-toggler{display: none;}.navbar-expand-xxl .offcanvas{position: static; z-index: auto; flex-grow: 1; width: auto !important; height: auto !important; visibility: visible !important; background-color: transparent !important; border: 0 !important; transform: none !important; transition: none;}.navbar-expand-xxl .offcanvas .offcanvas-header{display: none;}.navbar-expand-xxl .offcanvas .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}}.navbar-expand{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand .navbar-nav{flex-direction: row;}.navbar-expand .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand .navbar-nav .nav-link{padding-right: var(--navbar-nav-link-padding-x); padding-left: var(--navbar-nav-link-padding-x);}.navbar-expand .navbar-nav-scroll{overflow: visible;}.navbar-expand .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand .navbar-toggler{display: none;}.navbar-expand .offcanvas{position: static; z-index: auto; flex-grow: 1; width: auto !important; height: auto !important; visibility: visible !important; background-color: transparent !important; border: 0 !important; transform: none !important; transition: none;}.navbar-expand .offcanvas .offcanvas-header{display: none;}.navbar-expand .offcanvas .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}.navbar-dark, .navbar[data-bs-theme="dark"]{--navbar-color: rgba(255, 255, 255, 0.55); --navbar-hover-color: rgba(255, 255, 255, 0.75); --navbar-disabled-color: rgba(255, 255, 255, 0.25); --navbar-active-color: #FFFFFF; --navbar-brand-color: #FFFFFF; --navbar-brand-hover-color: #FFFFFF; --navbar-toggler-border-color: rgba(255, 255, 255, 0.1); --navbar-toggler-icon-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");}.card{--card-spacer-y: 16px; --card-spacer-x: 16px; --card-title-spacer-y: 8px; --card-title-color: ; --card-subtitle-color: ; --card-border-width: var(--border-width); --card-border-color: var(--border-color-translucent); --card-border-radius: var(--border-radius); --card-box-shadow: ; --card-inner-border-radius: calc(var(--border-radius) - (var(--border-width))); --card-cap-padding-y: 16px; --card-cap-padding-x: 16px; --card-cap-bg: rgba(var(--body-color-rgb), 0.03); --card-cap-color: ; --card-height: ; --card-color: ; --card-bg: white; --card-img-overlay-padding: 16px; --card-group-margin: 16px; position: relative; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; min-width: 0; height: var(--card-height); color: var(--body-color); word-wrap: break-word; background-color: var(--card-bg); background-clip: border-box; border: var(--card-border-width) solid var(--card-border-color); border-radius: var(--card-border-radius);}.card > hr{margin-right: 0; margin-left: 0;}.card > .list-group{border-top: inherit; border-bottom: inherit;}.card > .list-group:first-child{border-top-width: 0; border-top-left-radius: var(--card-inner-border-radius); border-top-right-radius: var(--card-inner-border-radius);}.card > .list-group:last-child{border-bottom-width: 0; border-bottom-right-radius: var(--card-inner-border-radius); border-bottom-left-radius: var(--card-inner-border-radius);}.card > .card-header + .list-group, .card > .list-group + .card-footer{border-top: 0;}.card-body{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; padding: var(--card-spacer-y) var(--card-spacer-x); color: var(--card-color);}.card-title{margin-bottom: var(--card-title-spacer-y); color: var(--card-title-color);}.card-subtitle{margin-top: calc(-.5 * var(--card-title-spacer-y)); margin-bottom: 0; color: var(--card-subtitle-color);}.card-text:last-child{margin-bottom: 0;}.card-link + .card-link{margin-left: var(--card-spacer-x);}.card-header{padding: var(--card-cap-padding-y) var(--card-cap-padding-x); margin-bottom: 0; color: var(--card-cap-color); background-color: var(--card-cap-bg); border-bottom: var(--card-border-width) solid var(--card-border-color);}.card-header:first-child{border-radius: var(--card-inner-border-radius) var(--card-inner-border-radius) 0 0;}.card-footer{padding: var(--card-cap-padding-y) var(--card-cap-padding-x); color: var(--card-cap-color); background-color: var(--card-cap-bg); border-top: var(--card-border-width) solid var(--card-border-color);}.card-footer:last-child{border-radius: 0 0 var(--card-inner-border-radius) var(--card-inner-border-radius);}.card-header-tabs{margin-right: calc(-.5 * var(--card-cap-padding-x)); margin-bottom: calc(-1 * var(--card-cap-padding-y)); margin-left: calc(-.5 * var(--card-cap-padding-x)); border-bottom: 0;}.card-header-tabs .nav-link.active{background-color: var(--card-bg); border-bottom-color: var(--card-bg);}.card-header-pills{margin-right: calc(-.5 * var(--card-cap-padding-x)); margin-left: calc(-.5 * var(--card-cap-padding-x));}.card-img-overlay{position: absolute; top: 0; right: 0; bottom: 0; left: 0; padding: var(--card-img-overlay-padding); border-radius: var(--card-inner-border-radius);}.card-img, .card-img-top, .card-img-bottom{width: 100%;}.card-img, .card-img-top{border-top-left-radius: var(--card-inner-border-radius); border-top-right-radius: var(--card-inner-border-radius);}.card-img, .card-img-bottom{border-bottom-right-radius: var(--card-inner-border-radius); border-bottom-left-radius: var(--card-inner-border-radius);}.card-group > .card{margin-bottom: var(--card-group-margin);}@media (min-width: 576px){.card-group{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: row wrap; flex-flow: row wrap;}.card-group > .card{flex: 1 0 0%; margin-bottom: 0;}.card-group > .card + .card{margin-left: 0; border-left: 0;}.card-group > .card:not(:last-child){border-top-right-radius: 0; border-bottom-right-radius: 0;}.card-group > .card:not(:last-child) .card-img-top, .card-group > .card:not(:last-child) .card-header{border-top-right-radius: 0;}.card-group > .card:not(:last-child) .card-img-bottom, .card-group > .card:not(:last-child) .card-footer{border-bottom-right-radius: 0;}.card-group > .card:not(:first-child){border-top-left-radius: 0; border-bottom-left-radius: 0;}.card-group > .card:not(:first-child) .card-img-top, .card-group > .card:not(:first-child) .card-header{border-top-left-radius: 0;}.card-group > .card:not(:first-child) .card-img-bottom, .card-group > .card:not(:first-child) .card-footer{border-bottom-left-radius: 0;}}.accordion{--accordion-color: var(--body-color); --accordion-bg: var(--body-bg); --accordion-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease; --accordion-border-color: var(--border-color); --accordion-border-width: var(--border-width); --accordion-border-radius: var(--border-radius); --accordion-inner-border-radius: calc(var(--border-radius) - (var(--border-width))); --accordion-btn-padding-x: 1.25rem; --accordion-btn-padding-y: 1rem; --accordion-btn-color: var(--body-color); --accordion-btn-bg: var(--accordion-bg); --accordion-btn-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='none' stroke='%23495057' stroke-linecap='round' stroke-linejoin='round'%3e%3cpath d='M2 5L8 11L14 5'/%3e%3c/svg%3e"); --accordion-btn-icon-width: 1.25rem; --accordion-btn-icon-transform: rotate(-180deg); --accordion-btn-icon-transition: transform 0.2s ease-in-out; --accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='none' stroke='%232d283f' stroke-linecap='round' stroke-linejoin='round'%3e%3cpath d='M2 5L8 11L14 5'/%3e%3c/svg%3e"); --accordion-btn-focus-box-shadow: 0 0 0 0.25rem rgba(113, 99, 158, 0.25); --accordion-body-padding-x: 1.25rem; --accordion-body-padding-y: 1rem; --accordion-active-color: var(--primary-text-emphasis); --accordion-active-bg: var(--primary-bg-subtle);}.accordion-button{position: relative; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; width: 100%; padding: var(--accordion-btn-padding-y) var(--accordion-btn-padding-x); font-size: 0.875rem; color: var(--accordion-btn-color); text-align: left; background-color: var(--accordion-btn-bg); border: 0; border-radius: 0; overflow-anchor: none; transition: var(--accordion-transition);}@media (prefers-reduced-motion: reduce){.accordion-button{transition: none;}}.accordion-button:not(.collapsed){color: var(--accordion-active-color); background-color: var(--accordion-active-bg); box-shadow: inset 0 calc(-1 * var(--accordion-border-width)) 0 var(--accordion-border-color);}.accordion-button:not(.collapsed)::after{background-image: var(--accordion-btn-active-icon); transform: var(--accordion-btn-icon-transform);}.accordion-button::after{flex-shrink: 0; width: var(--accordion-btn-icon-width); height: var(--accordion-btn-icon-width); margin-left: auto; content: ""; background-image: var(--accordion-btn-icon); background-repeat: no-repeat; background-size: var(--accordion-btn-icon-width); transition: var(--accordion-btn-icon-transition);}@media (prefers-reduced-motion: reduce){.accordion-button::after{transition: none;}}.accordion-button:hover{z-index: 2;}.accordion-button:focus{z-index: 3; outline: 0; box-shadow: var(--accordion-btn-focus-box-shadow);}.accordion-header{margin-bottom: 0;}.accordion-item{color: var(--accordion-color); background-color: var(--accordion-bg); border: var(--accordion-border-width) solid var(--accordion-border-color);}.accordion-item:first-of-type{border-top-left-radius: var(--accordion-border-radius); border-top-right-radius: var(--accordion-border-radius);}.accordion-item:first-of-type > .accordion-header .accordion-button{border-top-left-radius: var(--accordion-inner-border-radius); border-top-right-radius: var(--accordion-inner-border-radius);}.accordion-item:not(:first-of-type){border-top: 0;}.accordion-item:last-of-type{border-bottom-right-radius: var(--accordion-border-radius); border-bottom-left-radius: var(--accordion-border-radius);}.accordion-item:last-of-type > .accordion-header .accordion-button.collapsed{border-bottom-right-radius: var(--accordion-inner-border-radius); border-bottom-left-radius: var(--accordion-inner-border-radius);}.accordion-item:last-of-type > .accordion-collapse{border-bottom-right-radius: var(--accordion-border-radius); border-bottom-left-radius: var(--accordion-border-radius);}.accordion-body{padding: var(--accordion-body-padding-y) var(--accordion-body-padding-x);}.accordion-flush > .accordion-item{border-right: 0; border-left: 0; border-radius: 0;}.accordion-flush > .accordion-item:first-child{border-top: 0;}.accordion-flush > .accordion-item:last-child{border-bottom: 0;}.accordion-flush > .accordion-item > .accordion-header .accordion-button, .accordion-flush > .accordion-item > .accordion-header .accordion-button.collapsed{border-radius: 0;}.accordion-flush > .accordion-item > .accordion-collapse{border-radius: 0;}.breadcrumb{--breadcrumb-padding-x: 0; --breadcrumb-padding-y: 0; --breadcrumb-margin-bottom: 0; --breadcrumb-bg: white; --breadcrumb-border-radius: ; --breadcrumb-divider-color: var(--secondary-color); --breadcrumb-item-padding-x: 0.5rem; --breadcrumb-item-active-color: var(--secondary-color); display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; padding: var(--breadcrumb-padding-y) var(--breadcrumb-padding-x); margin-bottom: var(--breadcrumb-margin-bottom); font-size: var(--breadcrumb-font-size); list-style: none; background-color: var(--breadcrumb-bg); border-radius: var(--breadcrumb-border-radius);}.breadcrumb-item + .breadcrumb-item{padding-left: var(--breadcrumb-item-padding-x);}.breadcrumb-item + .breadcrumb-item::before{float: left; padding-right: var(--breadcrumb-item-padding-x); color: var(--breadcrumb-divider-color); content: var(--breadcrumb-divider, "/") ;}.breadcrumb-item.active{color: var(--breadcrumb-item-active-color);}.pagination{--pagination-padding-x: 0.75rem; --pagination-padding-y: 0.375rem; --pagination-font-size: 0.875rem; --pagination-color: var(--link-color); --pagination-bg: var(--body-bg); --pagination-border-width: var(--border-width); --pagination-border-color: var(--border-color); --pagination-border-radius: var(--border-radius); --pagination-hover-color: var(--link-hover-color); --pagination-hover-bg: var(--tertiary-bg); --pagination-hover-border-color: var(--border-color); --pagination-focus-color: var(--link-hover-color); --pagination-focus-bg: var(--secondary-bg); --pagination-focus-box-shadow: 0 0 0 0.25rem rgba(113, 99, 158, 0.25); --pagination-active-color: #212529; --pagination-active-bg: #dddbe8; --pagination-active-border-color: #dddbe8; --pagination-disabled-color: var(--secondary-color); --pagination-disabled-bg: var(--secondary-bg); --pagination-disabled-border-color: var(--border-color); display: -webkit-box; display: -webkit-flex; display: flex; padding-left: 0; list-style: none;}.page-link{position: relative; display: block; padding: var(--pagination-padding-y) var(--pagination-padding-x); font-size: var(--pagination-font-size); color: var(--pagination-color); background-color: var(--pagination-bg); border: var(--pagination-border-width) solid var(--pagination-border-color); transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.page-link{transition: none;}}.page-link:hover{z-index: 2; color: var(--pagination-hover-color); background-color: var(--pagination-hover-bg); border-color: var(--pagination-hover-border-color);}.page-link:focus{z-index: 3; color: var(--pagination-focus-color); background-color: var(--pagination-focus-bg); outline: 0; box-shadow: var(--pagination-focus-box-shadow);}.page-link.active, .active > .page-link{z-index: 3; color: var(--pagination-active-color); background-color: var(--pagination-active-bg); border-color: var(--pagination-active-border-color);}.page-link.disabled, .disabled > .page-link{color: var(--pagination-disabled-color); pointer-events: none; background-color: var(--pagination-disabled-bg); border-color: var(--pagination-disabled-border-color);}.page-item:not(:first-child) .page-link{margin-left: calc(var(--border-width) * -1);}.page-item:first-child .page-link{border-top-left-radius: var(--pagination-border-radius); border-bottom-left-radius: var(--pagination-border-radius);}.page-item:last-child .page-link{border-top-right-radius: var(--pagination-border-radius); border-bottom-right-radius: var(--pagination-border-radius);}.pagination-lg{--pagination-padding-x: 1.5rem; --pagination-padding-y: 0.75rem; --pagination-font-size: 1.09375rem; --pagination-border-radius: var(--border-radius-lg);}.pagination-sm{--pagination-padding-x: 0.5rem; --pagination-padding-y: 0.25rem; --pagination-font-size: 0.8125rem; --pagination-border-radius: var(--border-radius-sm);}.badge{--badge-padding-x: 0.82em; --badge-padding-y: 0.25em; --badge-font-size: 0.75em; --badge-font-weight: 500; --badge-color: inherit; --badge-border-radius: var(--border-radius); display: inline-block; padding: var(--badge-padding-y) var(--badge-padding-x); font-size: var(--badge-font-size); font-weight: var(--badge-font-weight); line-height: 1; color: var(--badge-color); text-align: center; white-space: nowrap; vertical-align: baseline; border-radius: var(--badge-border-radius);}.badge:empty{display: none;}.btn .badge{position: relative; top: -1px;}.alert{--alert-bg: transparent; --alert-padding-x: 16px; --alert-padding-y: 16px; --alert-margin-bottom: 1rem; --alert-color: inherit; --alert-border-color: transparent; --alert-border: var(--border-width) solid var(--alert-border-color); --alert-border-radius: var(--border-radius); --alert-link-color: inherit; position: relative; padding: var(--alert-padding-y) var(--alert-padding-x); margin-bottom: var(--alert-margin-bottom); color: var(--alert-color); background-color: var(--alert-bg); border: var(--alert-border); border-radius: var(--alert-border-radius);}.alert-heading{color: inherit;}.alert-link{font-weight: 500; color: var(--alert-link-color);}.alert-dismissible{padding-right: 48px;}.alert-dismissible .btn-close{position: absolute; top: 0; right: 0; z-index: 2; padding: 20px 16px;}.alert-primary{--alert-color: var(--primary-text-emphasis); --alert-bg: var(--primary-bg-subtle); --alert-border-color: var(--primary-border-subtle); --alert-link-color: var(--primary-text-emphasis);}.alert-secondary{--alert-color: var(--secondary-text-emphasis); --alert-bg: var(--secondary-bg-subtle); --alert-border-color: var(--secondary-border-subtle); --alert-link-color: var(--secondary-text-emphasis);}.alert-success{--alert-color: var(--success-text-emphasis); --alert-bg: var(--success-bg-subtle); --alert-border-color: var(--success-border-subtle); --alert-link-color: var(--success-text-emphasis);}.alert-info{--alert-color: var(--info-text-emphasis); --alert-bg: var(--info-bg-subtle); --alert-border-color: var(--info-border-subtle); --alert-link-color: var(--info-text-emphasis);}.alert-warning{--alert-color: var(--warning-text-emphasis); --alert-bg: var(--warning-bg-subtle); --alert-border-color: var(--warning-border-subtle); --alert-link-color: var(--warning-text-emphasis);}.alert-danger{--alert-color: var(--danger-text-emphasis); --alert-bg: var(--danger-bg-subtle); --alert-border-color: var(--danger-border-subtle); --alert-link-color: var(--danger-text-emphasis);}.alert-light{--alert-color: var(--light-text-emphasis); --alert-bg: var(--light-bg-subtle); --alert-border-color: var(--light-border-subtle); --alert-link-color: var(--light-text-emphasis);}.alert-dark{--alert-color: var(--dark-text-emphasis); --alert-bg: var(--dark-bg-subtle); --alert-border-color: var(--dark-border-subtle); --alert-link-color: var(--dark-text-emphasis);}@keyframes progress-bar-stripes{0%{background-position-x: 1rem;}}.progress, .progress-stacked{--progress-height: 1rem; --progress-font-size: 0.65625rem; --progress-bg: var(--secondary-bg); --progress-border-radius: var(--border-radius); --progress-box-shadow: var(--box-shadow-inset); --progress-bar-color: #FFFFFF; --progress-bar-bg: #71639e; --progress-bar-transition: width 0.6s ease; display: -webkit-box; display: -webkit-flex; display: flex; height: var(--progress-height); overflow: hidden; font-size: var(--progress-font-size); background-color: var(--progress-bg); border-radius: var(--progress-border-radius);}.progress-bar{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; justify-content: center; overflow: hidden; color: var(--progress-bar-color); text-align: center; white-space: nowrap; background-color: var(--progress-bar-bg); transition: var(--progress-bar-transition);}@media (prefers-reduced-motion: reduce){.progress-bar{transition: none;}}.progress-bar-striped{background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); background-size: var(--progress-height) var(--progress-height);}.progress-stacked > .progress{overflow: visible;}.progress-stacked > .progress > .progress-bar{width: 100%;}.progress-bar-animated{animation: 1s linear infinite progress-bar-stripes;}@media (prefers-reduced-motion: reduce){.progress-bar-animated{animation: none;}}.list-group{--list-group-color: var(--body-color); --list-group-bg: white; --list-group-border-color: var(--border-color); --list-group-border-width: var(--border-width); --list-group-border-radius: var(--border-radius); --list-group-item-padding-x: 16px; --list-group-item-padding-y: 8px; --list-group-action-color: var(--secondary-color); --list-group-action-hover-color: #212529; --list-group-action-hover-bg: rgba(0, 0, 0, 0.08); --list-group-action-active-color: var(--body-color); --list-group-action-active-bg: var(--secondary-bg); --list-group-disabled-color: var(--secondary-color); --list-group-disabled-bg: white; --list-group-active-color: #212529; --list-group-active-bg: #d4ebfa; --list-group-active-border-color: #d4ebfa; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; padding-left: 0; margin-bottom: 0; border-radius: var(--list-group-border-radius);}.list-group-numbered{list-style-type: none; counter-reset: section;}.list-group-numbered > .list-group-item::before{content: counters(section, ".") ". "; counter-increment: section;}.list-group-item-action{width: 100%; color: var(--list-group-action-color); text-align: inherit;}.list-group-item-action:hover, .list-group-item-action:focus{z-index: 1; color: var(--list-group-action-hover-color); text-decoration: none; background-color: var(--list-group-action-hover-bg);}.list-group-item-action:active{color: var(--list-group-action-active-color); background-color: var(--list-group-action-active-bg);}.list-group-item{position: relative; display: block; padding: var(--list-group-item-padding-y) var(--list-group-item-padding-x); color: var(--list-group-color); background-color: var(--list-group-bg); border: var(--list-group-border-width) solid var(--list-group-border-color);}.list-group-item:first-child{border-top-left-radius: inherit; border-top-right-radius: inherit;}.list-group-item:last-child{border-bottom-right-radius: inherit; border-bottom-left-radius: inherit;}.list-group-item.disabled, .list-group-item:disabled{color: var(--list-group-disabled-color); pointer-events: none; background-color: var(--list-group-disabled-bg);}.list-group-item.active{z-index: 2; color: var(--list-group-active-color); background-color: var(--list-group-active-bg); border-color: var(--list-group-active-border-color);}.list-group-item + .list-group-item{border-top-width: 0;}.list-group-item + .list-group-item.active{margin-top: calc(-1 * var(--list-group-border-width)); border-top-width: var(--list-group-border-width);}.list-group-horizontal{flex-direction: row;}.list-group-horizontal > .list-group-item:first-child:not(:last-child){border-bottom-left-radius: var(--list-group-border-radius); border-top-right-radius: 0;}.list-group-horizontal > .list-group-item:last-child:not(:first-child){border-top-right-radius: var(--list-group-border-radius); border-bottom-left-radius: 0;}.list-group-horizontal > .list-group-item.active{margin-top: 0;}.list-group-horizontal > .list-group-item + .list-group-item{border-top-width: var(--list-group-border-width); border-left-width: 0;}.list-group-horizontal > .list-group-item + .list-group-item.active{margin-left: calc(-1 * var(--list-group-border-width)); border-left-width: var(--list-group-border-width);}@media (min-width: 576px){.list-group-horizontal-sm{flex-direction: row;}.list-group-horizontal-sm > .list-group-item:first-child:not(:last-child){border-bottom-left-radius: var(--list-group-border-radius); border-top-right-radius: 0;}.list-group-horizontal-sm > .list-group-item:last-child:not(:first-child){border-top-right-radius: var(--list-group-border-radius); border-bottom-left-radius: 0;}.list-group-horizontal-sm > .list-group-item.active{margin-top: 0;}.list-group-horizontal-sm > .list-group-item + .list-group-item{border-top-width: var(--list-group-border-width); border-left-width: 0;}.list-group-horizontal-sm > .list-group-item + .list-group-item.active{margin-left: calc(-1 * var(--list-group-border-width)); border-left-width: var(--list-group-border-width);}}@media (min-width: 768px){.list-group-horizontal-md{flex-direction: row;}.list-group-horizontal-md > .list-group-item:first-child:not(:last-child){border-bottom-left-radius: var(--list-group-border-radius); border-top-right-radius: 0;}.list-group-horizontal-md > .list-group-item:last-child:not(:first-child){border-top-right-radius: var(--list-group-border-radius); border-bottom-left-radius: 0;}.list-group-horizontal-md > .list-group-item.active{margin-top: 0;}.list-group-horizontal-md > .list-group-item + .list-group-item{border-top-width: var(--list-group-border-width); border-left-width: 0;}.list-group-horizontal-md > .list-group-item + .list-group-item.active{margin-left: calc(-1 * var(--list-group-border-width)); border-left-width: var(--list-group-border-width);}}@media (min-width: 992px){.list-group-horizontal-lg{flex-direction: row;}.list-group-horizontal-lg > .list-group-item:first-child:not(:last-child){border-bottom-left-radius: var(--list-group-border-radius); border-top-right-radius: 0;}.list-group-horizontal-lg > .list-group-item:last-child:not(:first-child){border-top-right-radius: var(--list-group-border-radius); border-bottom-left-radius: 0;}.list-group-horizontal-lg > .list-group-item.active{margin-top: 0;}.list-group-horizontal-lg > .list-group-item + .list-group-item{border-top-width: var(--list-group-border-width); border-left-width: 0;}.list-group-horizontal-lg > .list-group-item + .list-group-item.active{margin-left: calc(-1 * var(--list-group-border-width)); border-left-width: var(--list-group-border-width);}}@media (min-width: 1200px){.list-group-horizontal-xl{flex-direction: row;}.list-group-horizontal-xl > .list-group-item:first-child:not(:last-child){border-bottom-left-radius: var(--list-group-border-radius); border-top-right-radius: 0;}.list-group-horizontal-xl > .list-group-item:last-child:not(:first-child){border-top-right-radius: var(--list-group-border-radius); border-bottom-left-radius: 0;}.list-group-horizontal-xl > .list-group-item.active{margin-top: 0;}.list-group-horizontal-xl > .list-group-item + .list-group-item{border-top-width: var(--list-group-border-width); border-left-width: 0;}.list-group-horizontal-xl > .list-group-item + .list-group-item.active{margin-left: calc(-1 * var(--list-group-border-width)); border-left-width: var(--list-group-border-width);}}@media (min-width: 1400px){.list-group-horizontal-xxl{flex-direction: row;}.list-group-horizontal-xxl > .list-group-item:first-child:not(:last-child){border-bottom-left-radius: var(--list-group-border-radius); border-top-right-radius: 0;}.list-group-horizontal-xxl > .list-group-item:last-child:not(:first-child){border-top-right-radius: var(--list-group-border-radius); border-bottom-left-radius: 0;}.list-group-horizontal-xxl > .list-group-item.active{margin-top: 0;}.list-group-horizontal-xxl > .list-group-item + .list-group-item{border-top-width: var(--list-group-border-width); border-left-width: 0;}.list-group-horizontal-xxl > .list-group-item + .list-group-item.active{margin-left: calc(-1 * var(--list-group-border-width)); border-left-width: var(--list-group-border-width);}}.list-group-flush{border-radius: 0;}.list-group-flush > .list-group-item{border-width: 0 0 var(--list-group-border-width);}.list-group-flush > .list-group-item:last-child{border-bottom-width: 0;}.list-group-item-primary{--list-group-color: var(--primary-text-emphasis); --list-group-bg: var(--primary-bg-subtle); --list-group-border-color: var(--primary-border-subtle); --list-group-action-hover-color: var(--emphasis-color); --list-group-action-hover-bg: var(--primary-border-subtle); --list-group-action-active-color: var(--emphasis-color); --list-group-action-active-bg: var(--primary-border-subtle); --list-group-active-color: var(--primary-bg-subtle); --list-group-active-bg: var(--primary-text-emphasis); --list-group-active-border-color: var(--primary-text-emphasis);}.list-group-item-secondary{--list-group-color: var(--secondary-text-emphasis); --list-group-bg: var(--secondary-bg-subtle); --list-group-border-color: var(--secondary-border-subtle); --list-group-action-hover-color: var(--emphasis-color); --list-group-action-hover-bg: var(--secondary-border-subtle); --list-group-action-active-color: var(--emphasis-color); --list-group-action-active-bg: var(--secondary-border-subtle); --list-group-active-color: var(--secondary-bg-subtle); --list-group-active-bg: var(--secondary-text-emphasis); --list-group-active-border-color: var(--secondary-text-emphasis);}.list-group-item-success{--list-group-color: var(--success-text-emphasis); --list-group-bg: var(--success-bg-subtle); --list-group-border-color: var(--success-border-subtle); --list-group-action-hover-color: var(--emphasis-color); --list-group-action-hover-bg: var(--success-border-subtle); --list-group-action-active-color: var(--emphasis-color); --list-group-action-active-bg: var(--success-border-subtle); --list-group-active-color: var(--success-bg-subtle); --list-group-active-bg: var(--success-text-emphasis); --list-group-active-border-color: var(--success-text-emphasis);}.list-group-item-info{--list-group-color: var(--info-text-emphasis); --list-group-bg: var(--info-bg-subtle); --list-group-border-color: var(--info-border-subtle); --list-group-action-hover-color: var(--emphasis-color); --list-group-action-hover-bg: var(--info-border-subtle); --list-group-action-active-color: var(--emphasis-color); --list-group-action-active-bg: var(--info-border-subtle); --list-group-active-color: var(--info-bg-subtle); --list-group-active-bg: var(--info-text-emphasis); --list-group-active-border-color: var(--info-text-emphasis);}.list-group-item-warning{--list-group-color: var(--warning-text-emphasis); --list-group-bg: var(--warning-bg-subtle); --list-group-border-color: var(--warning-border-subtle); --list-group-action-hover-color: var(--emphasis-color); --list-group-action-hover-bg: var(--warning-border-subtle); --list-group-action-active-color: var(--emphasis-color); --list-group-action-active-bg: var(--warning-border-subtle); --list-group-active-color: var(--warning-bg-subtle); --list-group-active-bg: var(--warning-text-emphasis); --list-group-active-border-color: var(--warning-text-emphasis);}.list-group-item-danger{--list-group-color: var(--danger-text-emphasis); --list-group-bg: var(--danger-bg-subtle); --list-group-border-color: var(--danger-border-subtle); --list-group-action-hover-color: var(--emphasis-color); --list-group-action-hover-bg: var(--danger-border-subtle); --list-group-action-active-color: var(--emphasis-color); --list-group-action-active-bg: var(--danger-border-subtle); --list-group-active-color: var(--danger-bg-subtle); --list-group-active-bg: var(--danger-text-emphasis); --list-group-active-border-color: var(--danger-text-emphasis);}.list-group-item-light{--list-group-color: var(--light-text-emphasis); --list-group-bg: var(--light-bg-subtle); --list-group-border-color: var(--light-border-subtle); --list-group-action-hover-color: var(--emphasis-color); --list-group-action-hover-bg: var(--light-border-subtle); --list-group-action-active-color: var(--emphasis-color); --list-group-action-active-bg: var(--light-border-subtle); --list-group-active-color: var(--light-bg-subtle); --list-group-active-bg: var(--light-text-emphasis); --list-group-active-border-color: var(--light-text-emphasis);}.list-group-item-dark{--list-group-color: var(--dark-text-emphasis); --list-group-bg: var(--dark-bg-subtle); --list-group-border-color: var(--dark-border-subtle); --list-group-action-hover-color: var(--emphasis-color); --list-group-action-hover-bg: var(--dark-border-subtle); --list-group-action-active-color: var(--emphasis-color); --list-group-action-active-bg: var(--dark-border-subtle); --list-group-active-color: var(--dark-bg-subtle); --list-group-active-bg: var(--dark-text-emphasis); --list-group-active-border-color: var(--dark-text-emphasis);}.btn-close{--btn-close-color: #000000; --btn-close-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000000'%3e%3cpath d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/%3e%3c/svg%3e"); --btn-close-opacity: 0.5; --btn-close-hover-opacity: 0.75; --btn-close-focus-shadow: 0 0 0 0.25rem rgba(113, 99, 158, 0.25); --btn-close-focus-opacity: 1; --btn-close-disabled-opacity: 0.25; --btn-close-white-filter: invert(1) grayscale(100%) brightness(200%); box-sizing: content-box; width: 1em; height: 1em; padding: 0.25em 0.25em; color: var(--btn-close-color); background: transparent var(--btn-close-bg) center/1em auto no-repeat; border: 0; border-radius: 0.25rem; opacity: var(--btn-close-opacity);}.btn-close:hover{color: var(--btn-close-color); text-decoration: none; opacity: var(--btn-close-hover-opacity);}.btn-close:focus{outline: 0; box-shadow: var(--btn-close-focus-shadow); opacity: var(--btn-close-focus-opacity);}.btn-close:disabled, .btn-close.disabled{pointer-events: none; user-select: none; opacity: var(--btn-close-disabled-opacity);}.btn-close-white{filter: var(--btn-close-white-filter);}.toast{--toast-zindex: 1090; --toast-padding-x: 1.5rem; --toast-padding-y: 0.5rem; --toast-spacing: 32px; --toast-max-width: 320px; --toast-font-size: 0.875rem; --toast-color: ; --toast-bg: rgba(255, 255, 255, 0.7); --toast-border-width: var(--border-width); --toast-border-color: var(--border-color-translucent); --toast-border-radius: var(--border-radius); --toast-box-shadow: var(--box-shadow); --toast-header-color: var(--secondary-color); --toast-header-bg: rgba(255, 255, 255, 0.7); --toast-header-border-color: var(--border-color-translucent); width: var(--toast-max-width); max-width: 100%; font-size: var(--toast-font-size); color: var(--toast-color); pointer-events: auto; background-color: var(--toast-bg); background-clip: padding-box; border: var(--toast-border-width) solid var(--toast-border-color); box-shadow: var(--toast-box-shadow); border-radius: var(--toast-border-radius);}.toast.showing{opacity: 0;}.toast:not(.show){display: none;}.toast-container{--toast-zindex: 1090; position: absolute; z-index: var(--toast-zindex); width: max-content; max-width: 100%; pointer-events: none;}.toast-container > :not(:last-child){margin-bottom: var(--toast-spacing);}.toast-header{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; padding: var(--toast-padding-y) var(--toast-padding-x); color: var(--toast-header-color); background-color: var(--toast-header-bg); background-clip: padding-box; border-bottom: var(--toast-border-width) solid var(--toast-header-border-color); border-top-left-radius: calc(var(--toast-border-radius) - var(--toast-border-width)); border-top-right-radius: calc(var(--toast-border-radius) - var(--toast-border-width));}.toast-header .btn-close{margin-right: calc(-.5 * var(--toast-padding-x)); margin-left: var(--toast-padding-x);}.toast-body{padding: var(--toast-padding-x); word-wrap: break-word;}.modal{--modal-zindex: 1055; --modal-width: 650px; --modal-padding: 16px; --modal-margin: 0.5rem; --modal-color: ; --modal-bg: white; --modal-border-color: var(--border-color-translucent); --modal-border-width: var(--border-width); --modal-border-radius: 0.25rem; --modal-box-shadow: var(--box-shadow-sm); --modal-inner-border-radius: calc(0.25rem - (var(--border-width))); --modal-header-padding-x: 16px; --modal-header-padding-y: 16px; --modal-header-padding: 16px 16px; --modal-header-border-color: var(--border-color); --modal-header-border-width: var(--border-width); --modal-title-line-height: 1.5; --modal-footer-gap: 1px; --modal-footer-bg: ; --modal-footer-border-color: var(--border-color); --modal-footer-border-width: var(--border-width); position: fixed; top: 0; left: 0; z-index: var(--modal-zindex); display: none; width: 100%; height: 100%; overflow-x: hidden; overflow-y: auto; outline: 0;}.modal-dialog{position: relative; width: auto; margin: var(--modal-margin); pointer-events: none;}.modal.fade .modal-dialog{transition: transform 0.3s ease-out; transform: translate(0, -50px);}@media (prefers-reduced-motion: reduce){.modal.fade .modal-dialog{transition: none;}}.modal.show .modal-dialog{transform: none;}.modal.modal-static .modal-dialog{transform: none;}.modal-dialog-scrollable{height: calc(100% - var(--modal-margin) * 2);}.modal-dialog-scrollable .modal-content{max-height: 100%; overflow: hidden;}.modal-dialog-scrollable .modal-body{overflow-y: auto;}.modal-dialog-centered{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; min-height: calc(100% - var(--modal-margin) * 2);}.modal-content{position: relative; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; width: 100%; color: var(--modal-color); pointer-events: auto; background-color: var(--modal-bg); background-clip: padding-box; border: var(--modal-border-width) solid var(--modal-border-color); border-radius: var(--modal-border-radius); outline: 0;}.modal-backdrop{--backdrop-zindex: 1050; --backdrop-bg: #000000; --backdrop-opacity: 0.5; position: fixed; top: 0; left: 0; z-index: var(--backdrop-zindex); width: 100vw; height: 100vh; background-color: var(--backdrop-bg);}.modal-backdrop.fade{opacity: 0;}.modal-backdrop.show{opacity: var(--backdrop-opacity);}.modal-header{display: -webkit-box; display: -webkit-flex; display: flex; flex-shrink: 0; align-items: center; padding: var(--modal-header-padding); border-bottom: var(--modal-header-border-width) solid var(--modal-header-border-color); border-top-left-radius: var(--modal-inner-border-radius); border-top-right-radius: var(--modal-inner-border-radius);}.modal-header .btn-close{padding: calc(var(--modal-header-padding-y) * .5) calc(var(--modal-header-padding-x) * .5); margin: calc(-.5 * var(--modal-header-padding-y)) calc(-.5 * var(--modal-header-padding-x)) calc(-.5 * var(--modal-header-padding-y)) auto;}.modal-title{margin-bottom: 0; line-height: var(--modal-title-line-height);}.modal-body{position: relative; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; padding: var(--modal-padding);}.modal-footer{display: -webkit-box; display: -webkit-flex; display: flex; flex-shrink: 0; -webkit-flex-wrap: wrap; flex-wrap: wrap; align-items: center; -webkit-box-pack: end; justify-content: flex-end; padding: calc(var(--modal-padding) - var(--modal-footer-gap) * .5); background-color: var(--modal-footer-bg); border-top: var(--modal-footer-border-width) solid var(--modal-footer-border-color); border-bottom-right-radius: var(--modal-inner-border-radius); border-bottom-left-radius: var(--modal-inner-border-radius);}.modal-footer > *{margin: calc(var(--modal-footer-gap) * .5);}@media (min-width: 576px){.modal{--modal-margin: 1.75rem; --modal-box-shadow: var(--box-shadow);}.modal-dialog{max-width: var(--modal-width); margin-right: auto; margin-left: auto;}.modal-sm{--modal-width: 300px;}}@media (min-width: 992px){.modal-lg, .modal-xl{--modal-width: 980px;}}@media (min-width: 1200px){.modal-xl{--modal-width: 1140px;}}.modal-fullscreen{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen .modal-header, .modal-fullscreen .modal-footer{border-radius: 0;}.modal-fullscreen .modal-body{overflow-y: auto;}@media (max-width: 575.98px){.modal-fullscreen-sm-down{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen-sm-down .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen-sm-down .modal-header, .modal-fullscreen-sm-down .modal-footer{border-radius: 0;}.modal-fullscreen-sm-down .modal-body{overflow-y: auto;}}@media (max-width: 767.98px){.modal-fullscreen-md-down{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen-md-down .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen-md-down .modal-header, .modal-fullscreen-md-down .modal-footer{border-radius: 0;}.modal-fullscreen-md-down .modal-body{overflow-y: auto;}}@media (max-width: 991.98px){.modal-fullscreen-lg-down{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen-lg-down .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen-lg-down .modal-header, .modal-fullscreen-lg-down .modal-footer{border-radius: 0;}.modal-fullscreen-lg-down .modal-body{overflow-y: auto;}}@media (max-width: 1199.98px){.modal-fullscreen-xl-down{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen-xl-down .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen-xl-down .modal-header, .modal-fullscreen-xl-down .modal-footer{border-radius: 0;}.modal-fullscreen-xl-down .modal-body{overflow-y: auto;}}@media (max-width: 1399.98px){.modal-fullscreen-xxl-down{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen-xxl-down .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen-xxl-down .modal-header, .modal-fullscreen-xxl-down .modal-footer{border-radius: 0;}.modal-fullscreen-xxl-down .modal-body{overflow-y: auto;}}.tooltip{--tooltip-zindex: 1080; --tooltip-max-width: 400px; --tooltip-padding-x: 8px; --tooltip-padding-y: 4px; --tooltip-margin: ; --tooltip-font-size: 0.8125rem; --tooltip-color: #e9ecef; --tooltip-bg: var(--emphasis-color); --tooltip-border-radius: var(--border-radius); --tooltip-opacity: 1; --tooltip-arrow-width: 0.8rem; --tooltip-arrow-height: 0.4rem; z-index: var(--tooltip-zindex); display: block; margin: var(--tooltip-margin); font-family: var(--font-sans-serif); font-style: normal; font-weight: 400; line-height: 1.5; text-align: left; text-align: start; text-decoration: none; text-shadow: none; text-transform: none; letter-spacing: normal; word-break: normal; white-space: normal; word-spacing: normal; line-break: auto; font-size: var(--tooltip-font-size); word-wrap: break-word; opacity: 0;}.tooltip.show{opacity: var(--tooltip-opacity);}.tooltip .tooltip-arrow{display: block; width: var(--tooltip-arrow-width); height: var(--tooltip-arrow-height);}.tooltip .tooltip-arrow::before{position: absolute; content: ""; border-color: transparent; border-style: solid;}.bs-tooltip-top .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^="top"] .tooltip-arrow{bottom: calc(-1 * var(--tooltip-arrow-height));}.bs-tooltip-top .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^="top"] .tooltip-arrow::before{top: -1px; border-width: var(--tooltip-arrow-height) calc(var(--tooltip-arrow-width) * .5) 0; border-top-color: var(--tooltip-bg);}.bs-tooltip-end .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^="right"] .tooltip-arrow{left: calc(-1 * var(--tooltip-arrow-height)); width: var(--tooltip-arrow-height); height: var(--tooltip-arrow-width);}.bs-tooltip-end .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^="right"] .tooltip-arrow::before{right: -1px; border-width: calc(var(--tooltip-arrow-width) * .5) var(--tooltip-arrow-height) calc(var(--tooltip-arrow-width) * .5) 0; border-right-color: var(--tooltip-bg);}.bs-tooltip-bottom .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^="bottom"] .tooltip-arrow{top: calc(-1 * var(--tooltip-arrow-height));}.bs-tooltip-bottom .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^="bottom"] .tooltip-arrow::before{bottom: -1px; border-width: 0 calc(var(--tooltip-arrow-width) * .5) var(--tooltip-arrow-height); border-bottom-color: var(--tooltip-bg);}.bs-tooltip-start .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^="left"] .tooltip-arrow{right: calc(-1 * var(--tooltip-arrow-height)); width: var(--tooltip-arrow-height); height: var(--tooltip-arrow-width);}.bs-tooltip-start .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^="left"] .tooltip-arrow::before{left: -1px; border-width: calc(var(--tooltip-arrow-width) * .5) 0 calc(var(--tooltip-arrow-width) * .5) var(--tooltip-arrow-height); border-left-color: var(--tooltip-bg);}.tooltip-inner{max-width: var(--tooltip-max-width); padding: var(--tooltip-padding-y) var(--tooltip-padding-x); color: var(--tooltip-color); text-align: center; background-color: var(--tooltip-bg); border-radius: var(--tooltip-border-radius);}.popover{--popover-zindex: 1070; --popover-max-width: 276px; --popover-font-size: 0.8125rem; --popover-bg: #FFFFFF; --popover-border-width: var(--border-width); --popover-border-color: #dee2e6; --popover-border-radius: 0.25rem; --popover-inner-border-radius: calc(0.25rem - var(--border-width)); --popover-box-shadow: var(--box-shadow); --popover-header-padding-x: 16px; --popover-header-padding-y: 0.5rem; --popover-header-font-size: 0.875rem; --popover-header-color: #212529; --popover-header-bg: var(--secondary-bg); --popover-body-padding-x: 16px; --popover-body-padding-y: 16px; --popover-body-color: var(--body-color); --popover-arrow-width: 1rem; --popover-arrow-height: 0.5rem; --popover-arrow-border: var(--popover-border-color); z-index: var(--popover-zindex); display: block; max-width: var(--popover-max-width); font-family: var(--font-sans-serif); font-style: normal; font-weight: 400; line-height: 1.5; text-align: left; text-align: start; text-decoration: none; text-shadow: none; text-transform: none; letter-spacing: normal; word-break: normal; white-space: normal; word-spacing: normal; line-break: auto; font-size: var(--popover-font-size); word-wrap: break-word; background-color: var(--popover-bg); background-clip: padding-box; border: var(--popover-border-width) solid var(--popover-border-color); border-radius: var(--popover-border-radius);}.popover .popover-arrow{display: block; width: var(--popover-arrow-width); height: var(--popover-arrow-height);}.popover .popover-arrow::before, .popover .popover-arrow::after{position: absolute; display: block; content: ""; border-color: transparent; border-style: solid; border-width: 0;}.bs-popover-top > .popover-arrow, .bs-popover-auto[data-popper-placement^="top"] > .popover-arrow{bottom: calc(-1 * (var(--popover-arrow-height)) - var(--popover-border-width));}.bs-popover-top > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="top"] > .popover-arrow::before, .bs-popover-top > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="top"] > .popover-arrow::after{border-width: var(--popover-arrow-height) calc(var(--popover-arrow-width) * .5) 0;}.bs-popover-top > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="top"] > .popover-arrow::before{bottom: 0; border-top-color: var(--popover-arrow-border);}.bs-popover-top > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="top"] > .popover-arrow::after{bottom: var(--popover-border-width); border-top-color: var(--popover-bg);}.bs-popover-end > .popover-arrow, .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow{left: calc(-1 * (var(--popover-arrow-height)) - var(--popover-border-width)); width: var(--popover-arrow-height); height: var(--popover-arrow-width);}.bs-popover-end > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::before, .bs-popover-end > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::after{border-width: calc(var(--popover-arrow-width) * .5) var(--popover-arrow-height) calc(var(--popover-arrow-width) * .5) 0;}.bs-popover-end > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::before{left: 0; border-right-color: var(--popover-arrow-border);}.bs-popover-end > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::after{left: var(--popover-border-width); border-right-color: var(--popover-bg);}.bs-popover-bottom > .popover-arrow, .bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow{top: calc(-1 * (var(--popover-arrow-height)) - var(--popover-border-width));}.bs-popover-bottom > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::before, .bs-popover-bottom > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::after{border-width: 0 calc(var(--popover-arrow-width) * .5) var(--popover-arrow-height);}.bs-popover-bottom > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::before{top: 0; border-bottom-color: var(--popover-arrow-border);}.bs-popover-bottom > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::after{top: var(--popover-border-width); border-bottom-color: var(--popover-bg);}.bs-popover-bottom .popover-header::before, .bs-popover-auto[data-popper-placement^="bottom"] .popover-header::before{position: absolute; top: 0; left: 50%; display: block; width: var(--popover-arrow-width); margin-left: calc(-.5 * var(--popover-arrow-width)); content: ""; border-bottom: var(--popover-border-width) solid var(--popover-header-bg);}.bs-popover-start > .popover-arrow, .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow{right: calc(-1 * (var(--popover-arrow-height)) - var(--popover-border-width)); width: var(--popover-arrow-height); height: var(--popover-arrow-width);}.bs-popover-start > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::before, .bs-popover-start > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::after{border-width: calc(var(--popover-arrow-width) * .5) 0 calc(var(--popover-arrow-width) * .5) var(--popover-arrow-height);}.bs-popover-start > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::before{right: 0; border-left-color: var(--popover-arrow-border);}.bs-popover-start > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::after{right: var(--popover-border-width); border-left-color: var(--popover-bg);}.popover-header{padding: var(--popover-header-padding-y) var(--popover-header-padding-x); margin-bottom: 0; font-size: var(--popover-header-font-size); color: var(--popover-header-color); background-color: var(--popover-header-bg); border-bottom: var(--popover-border-width) solid var(--popover-border-color); border-top-left-radius: var(--popover-inner-border-radius); border-top-right-radius: var(--popover-inner-border-radius);}.popover-header:empty{display: none;}.popover-body{padding: var(--popover-body-padding-y) var(--popover-body-padding-x); color: var(--popover-body-color);}.carousel{position: relative;}.carousel.pointer-event{touch-action: pan-y;}.carousel-inner{position: relative; width: 100%; overflow: hidden;}.carousel-inner::after{display: block; clear: both; content: "";}.carousel-item{position: relative; display: none; float: left; width: 100%; margin-right: -100%; backface-visibility: hidden; transition: transform 0.6s ease-in-out;}@media (prefers-reduced-motion: reduce){.carousel-item{transition: none;}}.carousel-item.active, .carousel-item-next, .carousel-item-prev{display: block;}.carousel-item-next:not(.carousel-item-start), .active.carousel-item-end{transform: translateX(100%);}.carousel-item-prev:not(.carousel-item-end), .active.carousel-item-start{transform: translateX(-100%);}.carousel-fade .carousel-item{opacity: 0; transition-property: opacity; transform: none;}.carousel-fade .carousel-item.active, .carousel-fade .carousel-item-next.carousel-item-start, .carousel-fade .carousel-item-prev.carousel-item-end{z-index: 1; opacity: 1;}.carousel-fade .active.carousel-item-start, .carousel-fade .active.carousel-item-end{z-index: 0; opacity: 0; transition: opacity 0s 0.6s;}@media (prefers-reduced-motion: reduce){.carousel-fade .active.carousel-item-start, .carousel-fade .active.carousel-item-end{transition: none;}}.carousel-control-prev, .carousel-control-next{position: absolute; top: 0; bottom: 0; z-index: 1; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; justify-content: center; width: 15%; padding: 0; color: #FFFFFF; text-align: center; background: none; border: 0; opacity: 0.5; transition: opacity 0.15s ease;}@media (prefers-reduced-motion: reduce){.carousel-control-prev, .carousel-control-next{transition: none;}}.carousel-control-prev:hover, .carousel-control-prev:focus, .carousel-control-next:hover, .carousel-control-next:focus{color: #FFFFFF; text-decoration: none; outline: 0; opacity: 0.9;}.carousel-control-prev{left: 0;}.carousel-control-next{right: 0;}.carousel-control-prev-icon, .carousel-control-next-icon{display: inline-block; width: 2rem; height: 2rem; background-repeat: no-repeat; background-position: 50%; background-size: 100% 100%;}.carousel-control-prev-icon{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23FFFFFF'%3e%3cpath d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/%3e%3c/svg%3e") ;}.carousel-control-next-icon{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23FFFFFF'%3e%3cpath d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e") ;}.carousel-indicators{position: absolute; right: 0; bottom: 0; left: 0; z-index: 2; display: -webkit-box; display: -webkit-flex; display: flex; justify-content: center; padding: 0; margin-right: 15%; margin-bottom: 1rem; margin-left: 15%;}.carousel-indicators [data-bs-target]{box-sizing: content-box; -webkit-box-flex: 0; -webkit-flex: 0 1 auto; flex: 0 1 auto; width: 30px; height: 3px; padding: 0; margin-right: 3px; margin-left: 3px; text-indent: -999px; cursor: pointer; background-color: #FFFFFF; background-clip: padding-box; border: 0; border-top: 10px solid transparent; border-bottom: 10px solid transparent; opacity: 0.5; transition: opacity 0.6s ease;}@media (prefers-reduced-motion: reduce){.carousel-indicators [data-bs-target]{transition: none;}}.carousel-indicators .active{opacity: 1;}.carousel-caption{position: absolute; right: 15%; bottom: 1.25rem; left: 15%; padding-top: 1.25rem; padding-bottom: 1.25rem; color: #FFFFFF; text-align: center;}.carousel-dark .carousel-control-prev-icon, .carousel-dark .carousel-control-next-icon{filter: invert(1) grayscale(100);}.carousel-dark .carousel-indicators [data-bs-target]{background-color: #000000;}.carousel-dark .carousel-caption{color: #000000;}.spinner-grow, .spinner-border{display: inline-block; width: var(--spinner-width); height: var(--spinner-height); vertical-align: var(--spinner-vertical-align); border-radius: 50%; animation: var(--spinner-animation-speed) linear infinite var(--spinner-animation-name);}@keyframes spinner-border{to{transform: rotate(360deg) ;}}.spinner-border{--spinner-width: 2rem; --spinner-height: 2rem; --spinner-vertical-align: -0.125em; --spinner-border-width: 0.25em; --spinner-animation-speed: 0.75s; --spinner-animation-name: spinner-border; border: var(--spinner-border-width) solid currentcolor; border-right-color: transparent;}.spinner-border-sm{--spinner-width: 1rem; --spinner-height: 1rem; --spinner-border-width: 0.2em;}@keyframes spinner-grow{0%{transform: scale(0);}50%{opacity: 1; transform: none;}}.spinner-grow{--spinner-width: 2rem; --spinner-height: 2rem; --spinner-vertical-align: -0.125em; --spinner-animation-speed: 0.75s; --spinner-animation-name: spinner-grow; background-color: currentcolor; opacity: 0;}.spinner-grow-sm{--spinner-width: 1rem; --spinner-height: 1rem;}@media (prefers-reduced-motion: reduce){.spinner-border, .spinner-grow{--spinner-animation-speed: 1.5s;}}.offcanvas, .offcanvas-xxl, .offcanvas-xl, .offcanvas-lg, .offcanvas-md, .offcanvas-sm{--offcanvas-zindex: 1045; --offcanvas-width: 400px; --offcanvas-height: 30vh; --offcanvas-padding-x: 16px; --offcanvas-padding-y: 16px; --offcanvas-color: var(--body-color); --offcanvas-bg: var(--body-bg); --offcanvas-border-width: var(--border-width); --offcanvas-border-color: var(--border-color-translucent); --offcanvas-box-shadow: var(--box-shadow-sm); --offcanvas-transition: transform 0.3s ease-in-out; --offcanvas-title-line-height: 1.5;}@media (max-width: 575.98px){.offcanvas-sm{position: fixed; bottom: 0; z-index: var(--offcanvas-zindex); display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; max-width: 100%; color: var(--offcanvas-color); visibility: hidden; background-color: var(--offcanvas-bg); background-clip: padding-box; outline: 0; transition: var(--offcanvas-transition);}}@media (max-width: 575.98px) and (prefers-reduced-motion: reduce){.offcanvas-sm{transition: none;}}@media (max-width: 575.98px){.offcanvas-sm.offcanvas-start{top: 0; left: 0; width: var(--offcanvas-width); border-right: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(-100%);}.offcanvas-sm.offcanvas-end{top: 0; right: 0; width: var(--offcanvas-width); border-left: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(100%);}.offcanvas-sm.offcanvas-top{top: 0; right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-bottom: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(-100%);}.offcanvas-sm.offcanvas-bottom{right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-top: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(100%);}.offcanvas-sm.showing, .offcanvas-sm.show:not(.hiding){transform: none;}.offcanvas-sm.showing, .offcanvas-sm.hiding, .offcanvas-sm.show{visibility: visible;}}@media (min-width: 576px){.offcanvas-sm{--offcanvas-height: auto; --offcanvas-border-width: 0; background-color: transparent !important;}.offcanvas-sm .offcanvas-header{display: none;}.offcanvas-sm .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible; background-color: transparent !important;}}@media (max-width: 767.98px){.offcanvas-md{position: fixed; bottom: 0; z-index: var(--offcanvas-zindex); display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; max-width: 100%; color: var(--offcanvas-color); visibility: hidden; background-color: var(--offcanvas-bg); background-clip: padding-box; outline: 0; transition: var(--offcanvas-transition);}}@media (max-width: 767.98px) and (prefers-reduced-motion: reduce){.offcanvas-md{transition: none;}}@media (max-width: 767.98px){.offcanvas-md.offcanvas-start{top: 0; left: 0; width: var(--offcanvas-width); border-right: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(-100%);}.offcanvas-md.offcanvas-end{top: 0; right: 0; width: var(--offcanvas-width); border-left: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(100%);}.offcanvas-md.offcanvas-top{top: 0; right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-bottom: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(-100%);}.offcanvas-md.offcanvas-bottom{right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-top: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(100%);}.offcanvas-md.showing, .offcanvas-md.show:not(.hiding){transform: none;}.offcanvas-md.showing, .offcanvas-md.hiding, .offcanvas-md.show{visibility: visible;}}@media (min-width: 768px){.offcanvas-md{--offcanvas-height: auto; --offcanvas-border-width: 0; background-color: transparent !important;}.offcanvas-md .offcanvas-header{display: none;}.offcanvas-md .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible; background-color: transparent !important;}}@media (max-width: 991.98px){.offcanvas-lg{position: fixed; bottom: 0; z-index: var(--offcanvas-zindex); display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; max-width: 100%; color: var(--offcanvas-color); visibility: hidden; background-color: var(--offcanvas-bg); background-clip: padding-box; outline: 0; transition: var(--offcanvas-transition);}}@media (max-width: 991.98px) and (prefers-reduced-motion: reduce){.offcanvas-lg{transition: none;}}@media (max-width: 991.98px){.offcanvas-lg.offcanvas-start{top: 0; left: 0; width: var(--offcanvas-width); border-right: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(-100%);}.offcanvas-lg.offcanvas-end{top: 0; right: 0; width: var(--offcanvas-width); border-left: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(100%);}.offcanvas-lg.offcanvas-top{top: 0; right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-bottom: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(-100%);}.offcanvas-lg.offcanvas-bottom{right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-top: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(100%);}.offcanvas-lg.showing, .offcanvas-lg.show:not(.hiding){transform: none;}.offcanvas-lg.showing, .offcanvas-lg.hiding, .offcanvas-lg.show{visibility: visible;}}@media (min-width: 992px){.offcanvas-lg{--offcanvas-height: auto; --offcanvas-border-width: 0; background-color: transparent !important;}.offcanvas-lg .offcanvas-header{display: none;}.offcanvas-lg .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible; background-color: transparent !important;}}@media (max-width: 1199.98px){.offcanvas-xl{position: fixed; bottom: 0; z-index: var(--offcanvas-zindex); display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; max-width: 100%; color: var(--offcanvas-color); visibility: hidden; background-color: var(--offcanvas-bg); background-clip: padding-box; outline: 0; transition: var(--offcanvas-transition);}}@media (max-width: 1199.98px) and (prefers-reduced-motion: reduce){.offcanvas-xl{transition: none;}}@media (max-width: 1199.98px){.offcanvas-xl.offcanvas-start{top: 0; left: 0; width: var(--offcanvas-width); border-right: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(-100%);}.offcanvas-xl.offcanvas-end{top: 0; right: 0; width: var(--offcanvas-width); border-left: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(100%);}.offcanvas-xl.offcanvas-top{top: 0; right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-bottom: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(-100%);}.offcanvas-xl.offcanvas-bottom{right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-top: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(100%);}.offcanvas-xl.showing, .offcanvas-xl.show:not(.hiding){transform: none;}.offcanvas-xl.showing, .offcanvas-xl.hiding, .offcanvas-xl.show{visibility: visible;}}@media (min-width: 1200px){.offcanvas-xl{--offcanvas-height: auto; --offcanvas-border-width: 0; background-color: transparent !important;}.offcanvas-xl .offcanvas-header{display: none;}.offcanvas-xl .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible; background-color: transparent !important;}}@media (max-width: 1399.98px){.offcanvas-xxl{position: fixed; bottom: 0; z-index: var(--offcanvas-zindex); display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; max-width: 100%; color: var(--offcanvas-color); visibility: hidden; background-color: var(--offcanvas-bg); background-clip: padding-box; outline: 0; transition: var(--offcanvas-transition);}}@media (max-width: 1399.98px) and (prefers-reduced-motion: reduce){.offcanvas-xxl{transition: none;}}@media (max-width: 1399.98px){.offcanvas-xxl.offcanvas-start{top: 0; left: 0; width: var(--offcanvas-width); border-right: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(-100%);}.offcanvas-xxl.offcanvas-end{top: 0; right: 0; width: var(--offcanvas-width); border-left: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(100%);}.offcanvas-xxl.offcanvas-top{top: 0; right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-bottom: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(-100%);}.offcanvas-xxl.offcanvas-bottom{right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-top: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(100%);}.offcanvas-xxl.showing, .offcanvas-xxl.show:not(.hiding){transform: none;}.offcanvas-xxl.showing, .offcanvas-xxl.hiding, .offcanvas-xxl.show{visibility: visible;}}@media (min-width: 1400px){.offcanvas-xxl{--offcanvas-height: auto; --offcanvas-border-width: 0; background-color: transparent !important;}.offcanvas-xxl .offcanvas-header{display: none;}.offcanvas-xxl .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible; background-color: transparent !important;}}.offcanvas{position: fixed; bottom: 0; z-index: var(--offcanvas-zindex); display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; max-width: 100%; color: var(--offcanvas-color); visibility: hidden; background-color: var(--offcanvas-bg); background-clip: padding-box; outline: 0; transition: var(--offcanvas-transition);}@media (prefers-reduced-motion: reduce){.offcanvas{transition: none;}}.offcanvas.offcanvas-start{top: 0; left: 0; width: var(--offcanvas-width); border-right: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(-100%);}.offcanvas.offcanvas-end{top: 0; right: 0; width: var(--offcanvas-width); border-left: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateX(100%);}.offcanvas.offcanvas-top{top: 0; right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-bottom: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(-100%);}.offcanvas.offcanvas-bottom{right: 0; left: 0; height: var(--offcanvas-height); max-height: 100%; border-top: var(--offcanvas-border-width) solid var(--offcanvas-border-color); transform: translateY(100%);}.offcanvas.showing, .offcanvas.show:not(.hiding){transform: none;}.offcanvas.showing, .offcanvas.hiding, .offcanvas.show{visibility: visible;}.offcanvas-backdrop{position: fixed; top: 0; left: 0; z-index: 1040; width: 100vw; height: 100vh; background-color: #000000;}.offcanvas-backdrop.fade{opacity: 0;}.offcanvas-backdrop.show{opacity: 0.5;}.offcanvas-header{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; padding: var(--offcanvas-padding-y) var(--offcanvas-padding-x);}.offcanvas-header .btn-close{padding: calc(var(--offcanvas-padding-y) * .5) calc(var(--offcanvas-padding-x) * .5); margin: calc(-.5 * var(--offcanvas-padding-y)) calc(-.5 * var(--offcanvas-padding-x)) calc(-.5 * var(--offcanvas-padding-y)) auto;}.offcanvas-title{margin-bottom: 0; line-height: var(--offcanvas-title-line-height);}.offcanvas-body{flex-grow: 1; padding: var(--offcanvas-padding-y) var(--offcanvas-padding-x); overflow-y: auto;}.placeholder{display: inline-block; min-height: 1em; vertical-align: middle; cursor: wait; background-color: currentcolor; opacity: 0.5;}.placeholder.btn::before{display: inline-block; content: "";}.placeholder-xs{min-height: .6em;}.placeholder-sm{min-height: .8em;}.placeholder-lg{min-height: 1.2em;}.placeholder-glow .placeholder{animation: placeholder-glow 2s ease-in-out infinite;}@keyframes placeholder-glow{50%{opacity: 0.2;}}.placeholder-wave{mask-image: linear-gradient(130deg, #000000 55%, rgba(0, 0, 0, 0.8) 75%, #000000 95%); mask-size: 200% 100%; animation: placeholder-wave 2s linear infinite;}@keyframes placeholder-wave{100%{mask-position: -200% 0%;}}.clearfix::after{display: block; clear: both; content: "";}.text-bg-primary{color: #FFFFFF !important; background-color: RGBA(var(--primary-rgb), var(--bg-opacity, 1)) !important;}.text-bg-secondary{color: #000000 !important; background-color: RGBA(var(--secondary-rgb), var(--bg-opacity, 1)) !important;}.text-bg-success{color: #FFFFFF !important; background-color: RGBA(var(--success-rgb), var(--bg-opacity, 1)) !important;}.text-bg-info{color: #FFFFFF !important; background-color: RGBA(var(--info-rgb), var(--bg-opacity, 1)) !important;}.text-bg-warning{color: #000000 !important; background-color: RGBA(var(--warning-rgb), var(--bg-opacity, 1)) !important;}.text-bg-danger{color: #FFFFFF !important; background-color: RGBA(var(--danger-rgb), var(--bg-opacity, 1)) !important;}.text-bg-light{color: #000000 !important; background-color: RGBA(var(--light-rgb), var(--bg-opacity, 1)) !important;}.text-bg-dark{color: #FFFFFF !important; background-color: RGBA(var(--dark-rgb), var(--bg-opacity, 1)) !important;}.link-primary{color: RGBA(var(--primary-rgb), var(--link-opacity, 1)) !important; text-decoration-color: RGBA(var(--primary-rgb), var(--link-underline-opacity, 1)) !important;}.link-primary:hover, .link-primary:focus{color: RGBA(79, 69, 111, var(--link-opacity, 1)) !important; text-decoration-color: RGBA(79, 69, 111, var(--link-underline-opacity, 1)) !important;}.link-secondary{color: RGBA(var(--secondary-rgb), var(--link-opacity, 1)) !important; text-decoration-color: RGBA(var(--secondary-rgb), var(--link-underline-opacity, 1)) !important;}.link-secondary:hover, .link-secondary:focus{color: RGBA(232, 235, 238, var(--link-opacity, 1)) !important; text-decoration-color: RGBA(232, 235, 238, var(--link-underline-opacity, 1)) !important;}.link-success{color: RGBA(var(--success-rgb), var(--link-opacity, 1)) !important; text-decoration-color: RGBA(var(--success-rgb), var(--link-underline-opacity, 1)) !important;}.link-success:hover, .link-success:focus{color: RGBA(28, 117, 48, var(--link-opacity, 1)) !important; text-decoration-color: RGBA(28, 117, 48, var(--link-underline-opacity, 1)) !important;}.link-info{color: RGBA(var(--info-rgb), var(--link-opacity, 1)) !important; text-decoration-color: RGBA(var(--info-rgb), var(--link-underline-opacity, 1)) !important;}.link-info:hover, .link-info:focus{color: RGBA(16, 113, 129, var(--link-opacity, 1)) !important; text-decoration-color: RGBA(16, 113, 129, var(--link-underline-opacity, 1)) !important;}.link-warning{color: RGBA(var(--warning-rgb), var(--link-opacity, 1)) !important; text-decoration-color: RGBA(var(--warning-rgb), var(--link-underline-opacity, 1)) !important;}.link-warning:hover, .link-warning:focus{color: RGBA(255, 197, 77, var(--link-opacity, 1)) !important; text-decoration-color: RGBA(255, 197, 77, var(--link-underline-opacity, 1)) !important;}.link-danger{color: RGBA(var(--danger-rgb), var(--link-opacity, 1)) !important; text-decoration-color: RGBA(var(--danger-rgb), var(--link-underline-opacity, 1)) !important;}.link-danger:hover, .link-danger:focus{color: RGBA(154, 37, 48, var(--link-opacity, 1)) !important; text-decoration-color: RGBA(154, 37, 48, var(--link-underline-opacity, 1)) !important;}.link-light{color: RGBA(var(--light-rgb), var(--link-opacity, 1)) !important; text-decoration-color: RGBA(var(--light-rgb), var(--link-underline-opacity, 1)) !important;}.link-light:hover, .link-light:focus{color: RGBA(250, 251, 252, var(--link-opacity, 1)) !important; text-decoration-color: RGBA(250, 251, 252, var(--link-underline-opacity, 1)) !important;}.link-dark{color: RGBA(var(--dark-rgb), var(--link-opacity, 1)) !important; text-decoration-color: RGBA(var(--dark-rgb), var(--link-underline-opacity, 1)) !important;}.link-dark:hover, .link-dark:focus{color: RGBA(23, 26, 29, var(--link-opacity, 1)) !important; text-decoration-color: RGBA(23, 26, 29, var(--link-underline-opacity, 1)) !important;}.link-body-emphasis{color: RGBA(var(--emphasis-color-rgb), var(--link-opacity, 1)) !important; text-decoration-color: RGBA(var(--emphasis-color-rgb), var(--link-underline-opacity, 1)) !important;}.link-body-emphasis:hover, .link-body-emphasis:focus{color: RGBA(var(--emphasis-color-rgb), var(--link-opacity, 0.75)) !important; text-decoration-color: RGBA(var(--emphasis-color-rgb), var(--link-underline-opacity, 0.75)) !important;}.focus-ring:focus{outline: 0; box-shadow: var(--focus-ring-x, 0) var(--focus-ring-y, 0) var(--focus-ring-blur, 0) var(--focus-ring-width) var(--focus-ring-color);}.icon-link{display: -webkit-inline-box; display: -webkit-inline-flex; display: inline-flex; gap: 0.375rem; align-items: center; text-decoration-color: rgba(var(--link-color-rgb), var(--link-opacity, 0.5)); text-underline-offset: 0.25em; backface-visibility: hidden;}.icon-link > .bi{flex-shrink: 0; width: 1em; height: 1em; fill: currentcolor; transition: 0.2s ease-in-out transform;}@media (prefers-reduced-motion: reduce){.icon-link > .bi{transition: none;}}.icon-link-hover:hover > .bi, .icon-link-hover:focus-visible > .bi{transform: var(--icon-link-transform, translate3d(0.25em, 0, 0));}.ratio{position: relative; width: 100%;}.ratio::before{display: block; padding-top: var(--aspect-ratio); content: "";}.ratio > *{position: absolute; top: 0; left: 0; width: 100%; height: 100%;}.ratio-1x1{--aspect-ratio: 100%;}.ratio-4x3{--aspect-ratio: calc(3 / 4 * 100%);}.ratio-16x9{--aspect-ratio: calc(9 / 16 * 100%);}.ratio-21x9{--aspect-ratio: calc(9 / 21 * 100%);}.fixed-top{position: fixed; top: 0; right: 0; left: 0; z-index: 1030;}.fixed-bottom{position: fixed; right: 0; bottom: 0; left: 0; z-index: 1030;}.sticky-top{position: sticky; top: 0; z-index: 1020;}.sticky-bottom{position: sticky; bottom: 0; z-index: 1020;}@media (min-width: 576px){.sticky-sm-top{position: sticky; top: 0; z-index: 1020;}.sticky-sm-bottom{position: sticky; bottom: 0; z-index: 1020;}}@media (min-width: 768px){.sticky-md-top{position: sticky; top: 0; z-index: 1020;}.sticky-md-bottom{position: sticky; bottom: 0; z-index: 1020;}}@media (min-width: 992px){.sticky-lg-top{position: sticky; top: 0; z-index: 1020;}.sticky-lg-bottom{position: sticky; bottom: 0; z-index: 1020;}}@media (min-width: 1200px){.sticky-xl-top{position: sticky; top: 0; z-index: 1020;}.sticky-xl-bottom{position: sticky; bottom: 0; z-index: 1020;}}@media (min-width: 1400px){.sticky-xxl-top{position: sticky; top: 0; z-index: 1020;}.sticky-xxl-bottom{position: sticky; bottom: 0; z-index: 1020;}}.hstack{display: -webkit-box; display: -webkit-flex; display: flex; flex-direction: row; align-items: center; align-self: stretch;}.vstack{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; align-self: stretch;}.visually-hidden, .visually-hidden-focusable:not(:focus):not(:focus-within){width: 1px !important; height: 1px !important; padding: 0 !important; margin: -1px !important; overflow: hidden !important; clip: rect(0, 0, 0, 0) !important; white-space: nowrap !important; border: 0 !important;}.visually-hidden:not(caption), .visually-hidden-focusable:not(:focus):not(:focus-within):not(caption){position: absolute !important;}.stretched-link::after{position: absolute; top: 0; right: 0; bottom: 0; left: 0; z-index: 1; content: "";}.text-truncate{overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}.vr{display: inline-block; align-self: stretch; width: var(--border-width); min-height: 1em; background-color: currentcolor; opacity: 0.25;}

/* /web/static/src/scss/utilities_custom.scss */
 .opacity-0-hover:hover, .opacity-trigger-hover:hover .opacity-0-hover{opacity: 0 !important;}.opacity-25-hover:hover, .opacity-trigger-hover:hover .opacity-25-hover{opacity: 0.25 !important;}.opacity-50-hover:hover, .opacity-trigger-hover:hover .opacity-50-hover{opacity: 0.5 !important;}.opacity-75-hover:hover, .opacity-trigger-hover:hover .opacity-75-hover{opacity: 0.75 !important;}.opacity-100-hover:hover, .opacity-trigger-hover:hover .opacity-100-hover{opacity: 1 !important;}.opacity-disabled-hover:hover, .opacity-trigger-hover:hover .opacity-disabled-hover{opacity: 0.5 !important;}.opacity-muted-hover:hover, .opacity-trigger-hover:hover .opacity-muted-hover{opacity: 0.76 !important;}.d-empty-none:empty{display: none !important;}.smaller{font-size: 0.75rem;}

/* /web/static/src/scss/utilities_custom_backend.scss */
 

/* /web/static/lib/bootstrap/scss/utilities/_api.scss */
 .align-baseline{vertical-align: baseline !important;}.align-top{vertical-align: top !important;}.align-middle{vertical-align: middle !important;}.align-bottom{vertical-align: bottom !important;}.align-text-bottom{vertical-align: text-bottom !important;}.align-text-top{vertical-align: text-top !important;}.float-start{float: left !important;}.float-end{float: right !important;}.float-none{float: none !important;}.object-fit-contain{object-fit: contain !important;}.object-fit-cover{object-fit: cover !important;}.object-fit-fill{object-fit: fill !important;}.object-fit-scale{object-fit: scale-down !important;}.object-fit-none{object-fit: none !important;}.opacity-0{opacity: 0 !important;}.opacity-25{opacity: 0.25 !important;}.opacity-50{opacity: 0.5 !important;}.opacity-75{opacity: 0.75 !important;}.opacity-100{opacity: 1 !important;}.opacity-disabled{opacity: 0.5 !important;}.opacity-muted{opacity: 0.76 !important;}.overflow-auto{overflow: auto !important;}.overflow-hidden{overflow: hidden !important;}.overflow-visible{overflow: visible !important;}.overflow-scroll{overflow: scroll !important;}.overflow-x-auto{overflow-x: auto !important;}.overflow-x-hidden{overflow-x: hidden !important;}.overflow-x-visible{overflow-x: visible !important;}.overflow-x-scroll{overflow-x: scroll !important;}.overflow-y-auto{overflow-y: auto !important;}.overflow-y-hidden{overflow-y: hidden !important;}.overflow-y-visible{overflow-y: visible !important;}.overflow-y-scroll{overflow-y: scroll !important;}.d-inline{display: inline !important;}.d-inline-block{display: inline-block !important;}.d-block{display: block !important;}.d-grid{display: grid !important;}.d-inline-grid{display: inline-grid !important;}.d-table{display: table !important;}.d-table-row{display: table-row !important;}.d-table-cell{display: table-cell !important;}.d-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-none{display: none !important;}.d-contents{display: contents !important;}.shadow{box-shadow: var(--box-shadow) !important;}.shadow-sm{box-shadow: var(--box-shadow-sm) !important;}.shadow-lg{box-shadow: var(--box-shadow-lg) !important;}.shadow-none{box-shadow: none !important;}.focus-ring-primary{--focus-ring-color: rgba(var(--primary-rgb), var(--focus-ring-opacity));}.focus-ring-secondary{--focus-ring-color: rgba(var(--secondary-rgb), var(--focus-ring-opacity));}.focus-ring-success{--focus-ring-color: rgba(var(--success-rgb), var(--focus-ring-opacity));}.focus-ring-info{--focus-ring-color: rgba(var(--info-rgb), var(--focus-ring-opacity));}.focus-ring-warning{--focus-ring-color: rgba(var(--warning-rgb), var(--focus-ring-opacity));}.focus-ring-danger{--focus-ring-color: rgba(var(--danger-rgb), var(--focus-ring-opacity));}.focus-ring-light{--focus-ring-color: rgba(var(--light-rgb), var(--focus-ring-opacity));}.focus-ring-dark{--focus-ring-color: rgba(var(--dark-rgb), var(--focus-ring-opacity));}.position-static{position: static !important;}.position-relative{position: relative !important;}.position-absolute{position: absolute !important;}.position-fixed{position: fixed !important;}.position-sticky{position: sticky !important;}.top-0{top: 0 !important;}.top-50{top: 50% !important;}.top-100{top: 100% !important;}.bottom-0{bottom: 0 !important;}.bottom-50{bottom: 50% !important;}.bottom-100{bottom: 100% !important;}.start-0{left: 0 !important;}.start-50{left: 50% !important;}.start-100{left: 100% !important;}.end-0{right: 0 !important;}.end-50{right: 50% !important;}.end-100{right: 100% !important;}.translate-middle{transform: translate(-50%, -50%) !important;}.translate-middle-x{transform: translateX(-50%) !important;}.translate-middle-y{transform: translateY(-50%) !important;}.border{border: var(--border-width) var(--border-style) var(--border-color) !important;}.border-0{border: 0 !important;}.border-top{border-top: var(--border-width) var(--border-style) var(--border-color) !important;}.border-top-0{border-top: 0 !important;}.border-end{border-right: var(--border-width) var(--border-style) var(--border-color) !important;}.border-end-0{border-right: 0 !important;}.border-bottom{border-bottom: var(--border-width) var(--border-style) var(--border-color) !important;}.border-bottom-0{border-bottom: 0 !important;}.border-start{border-left: var(--border-width) var(--border-style) var(--border-color) !important;}.border-start-0{border-left: 0 !important;}.border-primary{--border-opacity: 1; border-color: rgba(var(--primary-rgb), var(--border-opacity)) !important;}.border-secondary{--border-opacity: 1; border-color: rgba(var(--secondary-rgb), var(--border-opacity)) !important;}.border-success{--border-opacity: 1; border-color: rgba(var(--success-rgb), var(--border-opacity)) !important;}.border-info{--border-opacity: 1; border-color: rgba(var(--info-rgb), var(--border-opacity)) !important;}.border-warning{--border-opacity: 1; border-color: rgba(var(--warning-rgb), var(--border-opacity)) !important;}.border-danger{--border-opacity: 1; border-color: rgba(var(--danger-rgb), var(--border-opacity)) !important;}.border-light{--border-opacity: 1; border-color: rgba(var(--light-rgb), var(--border-opacity)) !important;}.border-dark{--border-opacity: 1; border-color: rgba(var(--dark-rgb), var(--border-opacity)) !important;}.border-black{--border-opacity: 1; border-color: rgba(var(--black-rgb), var(--border-opacity)) !important;}.border-white{--border-opacity: 1; border-color: rgba(var(--white-rgb), var(--border-opacity)) !important;}.border-transparent{--border-opacity: 1; border-color: transparent !important;}.border-primary-subtle{border-color: var(--primary-border-subtle) !important;}.border-secondary-subtle{border-color: var(--secondary-border-subtle) !important;}.border-success-subtle{border-color: var(--success-border-subtle) !important;}.border-info-subtle{border-color: var(--info-border-subtle) !important;}.border-warning-subtle{border-color: var(--warning-border-subtle) !important;}.border-danger-subtle{border-color: var(--danger-border-subtle) !important;}.border-light-subtle{border-color: var(--light-border-subtle) !important;}.border-dark-subtle{border-color: var(--dark-border-subtle) !important;}.border-1{border-width: 1px !important;}.border-2{border-width: 2px !important;}.border-3{border-width: 3px !important;}.border-4{border-width: 4px !important;}.border-5{border-width: 5px !important;}.border-opacity-10{--border-opacity: 0.1;}.border-opacity-25{--border-opacity: 0.25;}.border-opacity-50{--border-opacity: 0.5;}.border-opacity-75{--border-opacity: 0.75;}.border-opacity-100{--border-opacity: 1;}.w-0{width: 0 !important;}.w-25{width: 25% !important;}.w-50{width: 50% !important;}.w-75{width: 75% !important;}.w-100{width: 100% !important;}.w-auto{width: auto !important;}.mw-0{max-width: 0 !important;}.mw-25{max-width: 25% !important;}.mw-50{max-width: 50% !important;}.mw-75{max-width: 75% !important;}.mw-100{max-width: 100% !important;}.mw-auto{max-width: auto !important;}.vw-100{width: 100vw !important;}.min-vw-100{min-width: 100vw !important;}.h-0{height: 0 !important;}.h-25{height: 25% !important;}.h-50{height: 50% !important;}.h-75{height: 75% !important;}.h-100{height: 100% !important;}.h-auto{height: auto !important;}.mh-0{max-height: 0 !important;}.mh-25{max-height: 25% !important;}.mh-50{max-height: 50% !important;}.mh-75{max-height: 75% !important;}.mh-100{max-height: 100% !important;}.mh-auto{max-height: auto !important;}.vh-100{height: 100vh !important;}.min-vh-100{min-height: 100vh !important;}.flex-fill{flex: 1 1 auto !important;}.flex-row{flex-direction: row !important;}.flex-column{flex-direction: column !important;}.flex-row-reverse{flex-direction: row-reverse !important;}.flex-column-reverse{flex-direction: column-reverse !important;}.flex-grow-0{flex-grow: 0 !important;}.flex-grow-1{flex-grow: 1 !important;}.flex-shrink-0{flex-shrink: 0 !important;}.flex-shrink-1{flex-shrink: 1 !important;}.flex-wrap{flex-wrap: wrap !important;}.flex-nowrap{flex-wrap: nowrap !important;}.flex-wrap-reverse{flex-wrap: wrap-reverse !important;}.justify-content-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-center{justify-content: center !important;}.justify-content-between{justify-content: space-between !important;}.justify-content-around{justify-content: space-around !important;}.justify-content-evenly{justify-content: space-evenly !important;}.align-items-start{align-items: flex-start !important;}.align-items-end{align-items: flex-end !important;}.align-items-center{align-items: center !important;}.align-items-baseline{align-items: baseline !important;}.align-items-stretch{align-items: stretch !important;}.align-content-start{align-content: flex-start !important;}.align-content-end{align-content: flex-end !important;}.align-content-center{align-content: center !important;}.align-content-between{align-content: space-between !important;}.align-content-around{align-content: space-around !important;}.align-content-stretch{align-content: stretch !important;}.align-self-auto{align-self: auto !important;}.align-self-start{align-self: flex-start !important;}.align-self-end{align-self: flex-end !important;}.align-self-center{align-self: center !important;}.align-self-baseline{align-self: baseline !important;}.align-self-stretch{align-self: stretch !important;}.order-first{order: -1 !important;}.order-last{order: 13 !important;}.order-0{order: 0 !important;}.order-1{order: 1 !important;}.order-2{order: 2 !important;}.order-3{order: 3 !important;}.order-4{order: 4 !important;}.order-5{order: 5 !important;}.order-6{order: 6 !important;}.order-7{order: 7 !important;}.order-8{order: 8 !important;}.order-9{order: 9 !important;}.order-10{order: 10 !important;}.order-11{order: 11 !important;}.order-12{order: 12 !important;}.m-0{margin: 0 !important;}.m-1{margin: 4px !important;}.m-2{margin: 8px !important;}.m-3{margin: 16px !important;}.m-4{margin: 24px !important;}.m-5{margin: 48px !important;}.m-auto{margin: auto !important;}.mx-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-1{margin-right: 4px !important; margin-left: 4px !important;}.mx-2{margin-right: 8px !important; margin-left: 8px !important;}.mx-3{margin-right: 16px !important; margin-left: 16px !important;}.mx-4{margin-right: 24px !important; margin-left: 24px !important;}.mx-5{margin-right: 48px !important; margin-left: 48px !important;}.mx-auto{margin-right: auto !important; margin-left: auto !important;}.my-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-1{margin-top: 4px !important; margin-bottom: 4px !important;}.my-2{margin-top: 8px !important; margin-bottom: 8px !important;}.my-3{margin-top: 16px !important; margin-bottom: 16px !important;}.my-4{margin-top: 24px !important; margin-bottom: 24px !important;}.my-5{margin-top: 48px !important; margin-bottom: 48px !important;}.my-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-0{margin-top: 0 !important;}.mt-1{margin-top: 4px !important;}.mt-2{margin-top: 8px !important;}.mt-3{margin-top: 16px !important;}.mt-4{margin-top: 24px !important;}.mt-5{margin-top: 48px !important;}.mt-auto{margin-top: auto !important;}.me-0{margin-right: 0 !important;}.me-1{margin-right: 4px !important;}.me-2{margin-right: 8px !important;}.me-3{margin-right: 16px !important;}.me-4{margin-right: 24px !important;}.me-5{margin-right: 48px !important;}.me-auto{margin-right: auto !important;}.mb-0{margin-bottom: 0 !important;}.mb-1{margin-bottom: 4px !important;}.mb-2{margin-bottom: 8px !important;}.mb-3{margin-bottom: 16px !important;}.mb-4{margin-bottom: 24px !important;}.mb-5{margin-bottom: 48px !important;}.mb-auto{margin-bottom: auto !important;}.ms-0{margin-left: 0 !important;}.ms-1{margin-left: 4px !important;}.ms-2{margin-left: 8px !important;}.ms-3{margin-left: 16px !important;}.ms-4{margin-left: 24px !important;}.ms-5{margin-left: 48px !important;}.ms-auto{margin-left: auto !important;}.m-n1{margin: -4px !important;}.m-n2{margin: -8px !important;}.m-n3{margin: -16px !important;}.m-n4{margin: -24px !important;}.m-n5{margin: -48px !important;}.mx-n1{margin-right: -4px !important; margin-left: -4px !important;}.mx-n2{margin-right: -8px !important; margin-left: -8px !important;}.mx-n3{margin-right: -16px !important; margin-left: -16px !important;}.mx-n4{margin-right: -24px !important; margin-left: -24px !important;}.mx-n5{margin-right: -48px !important; margin-left: -48px !important;}.my-n1{margin-top: -4px !important; margin-bottom: -4px !important;}.my-n2{margin-top: -8px !important; margin-bottom: -8px !important;}.my-n3{margin-top: -16px !important; margin-bottom: -16px !important;}.my-n4{margin-top: -24px !important; margin-bottom: -24px !important;}.my-n5{margin-top: -48px !important; margin-bottom: -48px !important;}.mt-n1{margin-top: -4px !important;}.mt-n2{margin-top: -8px !important;}.mt-n3{margin-top: -16px !important;}.mt-n4{margin-top: -24px !important;}.mt-n5{margin-top: -48px !important;}.me-n1{margin-right: -4px !important;}.me-n2{margin-right: -8px !important;}.me-n3{margin-right: -16px !important;}.me-n4{margin-right: -24px !important;}.me-n5{margin-right: -48px !important;}.mb-n1{margin-bottom: -4px !important;}.mb-n2{margin-bottom: -8px !important;}.mb-n3{margin-bottom: -16px !important;}.mb-n4{margin-bottom: -24px !important;}.mb-n5{margin-bottom: -48px !important;}.ms-n1{margin-left: -4px !important;}.ms-n2{margin-left: -8px !important;}.ms-n3{margin-left: -16px !important;}.ms-n4{margin-left: -24px !important;}.ms-n5{margin-left: -48px !important;}.p-0{padding: 0 !important;}.p-1{padding: 4px !important;}.p-2{padding: 8px !important;}.p-3{padding: 16px !important;}.p-4{padding: 24px !important;}.p-5{padding: 48px !important;}.px-0{padding-right: 0 !important; padding-left: 0 !important;}.px-1{padding-right: 4px !important; padding-left: 4px !important;}.px-2{padding-right: 8px !important; padding-left: 8px !important;}.px-3{padding-right: 16px !important; padding-left: 16px !important;}.px-4{padding-right: 24px !important; padding-left: 24px !important;}.px-5{padding-right: 48px !important; padding-left: 48px !important;}.py-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-1{padding-top: 4px !important; padding-bottom: 4px !important;}.py-2{padding-top: 8px !important; padding-bottom: 8px !important;}.py-3{padding-top: 16px !important; padding-bottom: 16px !important;}.py-4{padding-top: 24px !important; padding-bottom: 24px !important;}.py-5{padding-top: 48px !important; padding-bottom: 48px !important;}.pt-0{padding-top: 0 !important;}.pt-1{padding-top: 4px !important;}.pt-2{padding-top: 8px !important;}.pt-3{padding-top: 16px !important;}.pt-4{padding-top: 24px !important;}.pt-5{padding-top: 48px !important;}.pe-0{padding-right: 0 !important;}.pe-1{padding-right: 4px !important;}.pe-2{padding-right: 8px !important;}.pe-3{padding-right: 16px !important;}.pe-4{padding-right: 24px !important;}.pe-5{padding-right: 48px !important;}.pb-0{padding-bottom: 0 !important;}.pb-1{padding-bottom: 4px !important;}.pb-2{padding-bottom: 8px !important;}.pb-3{padding-bottom: 16px !important;}.pb-4{padding-bottom: 24px !important;}.pb-5{padding-bottom: 48px !important;}.ps-0{padding-left: 0 !important;}.ps-1{padding-left: 4px !important;}.ps-2{padding-left: 8px !important;}.ps-3{padding-left: 16px !important;}.ps-4{padding-left: 24px !important;}.ps-5{padding-left: 48px !important;}.gap-0{gap: 0 !important;}.gap-1{gap: 4px !important;}.gap-2{gap: 8px !important;}.gap-3{gap: 16px !important;}.gap-4{gap: 24px !important;}.gap-5{gap: 48px !important;}.row-gap-0{row-gap: 0 !important;}.row-gap-1{row-gap: 4px !important;}.row-gap-2{row-gap: 8px !important;}.row-gap-3{row-gap: 16px !important;}.row-gap-4{row-gap: 24px !important;}.row-gap-5{row-gap: 48px !important;}.column-gap-0{column-gap: 0 !important;}.column-gap-1{column-gap: 4px !important;}.column-gap-2{column-gap: 8px !important;}.column-gap-3{column-gap: 16px !important;}.column-gap-4{column-gap: 24px !important;}.column-gap-5{column-gap: 48px !important;}.font-monospace{font-family: var(--font-monospace) !important;}.font-sans-serif{font-family: var(--font-sans-serif) !important;}.fs-1{font-size: calc(1.3rem + 0.6vw) !important;}.fs-2{font-size: calc(1.25625rem + 0.075vw) !important;}.fs-3{font-size: 1.1375rem !important;}.fs-4{font-size: 1.05rem !important;}.fs-5{font-size: 0.9625rem !important;}.fs-6{font-size: 0.875rem !important;}.fst-italic{font-style: italic !important;}.fst-normal{font-style: normal !important;}.fw-lighter{font-weight: lighter !important;}.fw-light{font-weight: 300 !important;}.fw-normal{font-weight: 400 !important;}.fw-medium{font-weight: 500 !important;}.fw-semibold{font-weight: 600 !important;}.fw-bold{font-weight: 500 !important;}.fw-bolder{font-weight: bolder !important;}.lh-1{line-height: 1 !important;}.lh-sm{line-height: 1.25 !important;}.lh-base{line-height: 1.5 !important;}.lh-lg{line-height: 2 !important;}.text-start{text-align: left !important;}.text-end{text-align: right !important;}.text-center{text-align: center !important;}.text-decoration-none{text-decoration: none !important;}.text-decoration-underline{text-decoration: underline !important;}.text-decoration-line-through{text-decoration: line-through !important;}.text-lowercase{text-transform: lowercase !important;}.text-uppercase{text-transform: uppercase !important;}.text-capitalize{text-transform: capitalize !important;}.text-wrap{white-space: normal !important;}.text-nowrap{white-space: nowrap !important;}.text-prewrap{white-space: pre-wrap !important;}.text-break{word-wrap: break-word !important; word-break: break-word !important;}.text-body{--text-opacity: 1; color: #495057 !important;}.text-muted{--text-opacity: 1; color: rgba(73, 80, 87, 0.76) !important;}.text-reset{--text-opacity: 1; color: inherit !important;}.text-opacity-25{--text-opacity: 0.25;}.text-opacity-50{--text-opacity: 0.5;}.text-opacity-75{--text-opacity: 0.75;}.text-opacity-100{--text-opacity: 1;}.text-primary-emphasis{color: var(--primary-text-emphasis) !important;}.text-secondary-emphasis{color: var(--secondary-text-emphasis) !important;}.text-success-emphasis{color: var(--success-text-emphasis) !important;}.text-info-emphasis{color: var(--info-text-emphasis) !important;}.text-warning-emphasis{color: var(--warning-text-emphasis) !important;}.text-danger-emphasis{color: var(--danger-text-emphasis) !important;}.text-light-emphasis{color: var(--light-text-emphasis) !important;}.text-dark-emphasis{color: var(--dark-text-emphasis) !important;}.link-opacity-10{--link-opacity: 0.1;}.link-opacity-10-hover:hover{--link-opacity: 0.1;}.link-opacity-25{--link-opacity: 0.25;}.link-opacity-25-hover:hover{--link-opacity: 0.25;}.link-opacity-50{--link-opacity: 0.5;}.link-opacity-50-hover:hover{--link-opacity: 0.5;}.link-opacity-75{--link-opacity: 0.75;}.link-opacity-75-hover:hover{--link-opacity: 0.75;}.link-opacity-100{--link-opacity: 1;}.link-opacity-100-hover:hover{--link-opacity: 1;}.link-offset-1{text-underline-offset: 0.125em !important;}.link-offset-1-hover:hover{text-underline-offset: 0.125em !important;}.link-offset-2{text-underline-offset: 0.25em !important;}.link-offset-2-hover:hover{text-underline-offset: 0.25em !important;}.link-offset-3{text-underline-offset: 0.375em !important;}.link-offset-3-hover:hover{text-underline-offset: 0.375em !important;}.link-underline-primary{--link-underline-opacity: 1; text-decoration-color: rgba(var(--primary-rgb), var(--link-underline-opacity)) !important;}.link-underline-secondary{--link-underline-opacity: 1; text-decoration-color: rgba(var(--secondary-rgb), var(--link-underline-opacity)) !important;}.link-underline-success{--link-underline-opacity: 1; text-decoration-color: rgba(var(--success-rgb), var(--link-underline-opacity)) !important;}.link-underline-info{--link-underline-opacity: 1; text-decoration-color: rgba(var(--info-rgb), var(--link-underline-opacity)) !important;}.link-underline-warning{--link-underline-opacity: 1; text-decoration-color: rgba(var(--warning-rgb), var(--link-underline-opacity)) !important;}.link-underline-danger{--link-underline-opacity: 1; text-decoration-color: rgba(var(--danger-rgb), var(--link-underline-opacity)) !important;}.link-underline-light{--link-underline-opacity: 1; text-decoration-color: rgba(var(--light-rgb), var(--link-underline-opacity)) !important;}.link-underline-dark{--link-underline-opacity: 1; text-decoration-color: rgba(var(--dark-rgb), var(--link-underline-opacity)) !important;}.link-underline{--link-underline-opacity: 1; text-decoration-color: rgba(var(--link-color-rgb), var(--link-underline-opacity, 1)) !important;}.link-underline-opacity-0{--link-underline-opacity: 0;}.link-underline-opacity-0-hover:hover{--link-underline-opacity: 0;}.link-underline-opacity-10{--link-underline-opacity: 0.1;}.link-underline-opacity-10-hover:hover{--link-underline-opacity: 0.1;}.link-underline-opacity-25{--link-underline-opacity: 0.25;}.link-underline-opacity-25-hover:hover{--link-underline-opacity: 0.25;}.link-underline-opacity-50{--link-underline-opacity: 0.5;}.link-underline-opacity-50-hover:hover{--link-underline-opacity: 0.5;}.link-underline-opacity-75{--link-underline-opacity: 0.75;}.link-underline-opacity-75-hover:hover{--link-underline-opacity: 0.75;}.link-underline-opacity-100{--link-underline-opacity: 1;}.link-underline-opacity-100-hover:hover{--link-underline-opacity: 1;}.bg-opacity-0{--bg-opacity: 0;}.bg-opacity-25{--bg-opacity: 0.25;}.bg-opacity-50{--bg-opacity: 0.5;}.bg-opacity-75{--bg-opacity: 0.75;}.bg-opacity-100{--bg-opacity: 1;}.bg-opacity-disabled{--bg-opacity: 0.5;}.bg-opacity-muted{--bg-opacity: 0.76;}.bg-primary-subtle{background-color: var(--primary-bg-subtle) !important;}.bg-secondary-subtle{background-color: var(--secondary-bg-subtle) !important;}.bg-success-subtle{background-color: var(--success-bg-subtle) !important;}.bg-info-subtle{background-color: var(--info-bg-subtle) !important;}.bg-warning-subtle{background-color: var(--warning-bg-subtle) !important;}.bg-danger-subtle{background-color: var(--danger-bg-subtle) !important;}.bg-light-subtle{background-color: var(--light-bg-subtle) !important;}.bg-dark-subtle{background-color: var(--dark-bg-subtle) !important;}.bg-gradient{background-image: var(--gradient) !important;}.user-select-all{user-select: all !important;}.user-select-auto{user-select: auto !important;}.user-select-none{user-select: none !important;}.pe-none{pointer-events: none !important;}.pe-auto{pointer-events: auto !important;}.rounded{border-radius: var(--border-radius) !important;}.rounded-0{border-radius: 0 !important;}.rounded-1{border-radius: var(--border-radius-sm) !important;}.rounded-2{border-radius: var(--border-radius) !important;}.rounded-3{border-radius: var(--border-radius-lg) !important;}.rounded-4{border-radius: var(--border-radius-xl) !important;}.rounded-5{border-radius: var(--border-radius-xxl) !important;}.rounded-circle{border-radius: 50% !important;}.rounded-pill{border-radius: var(--border-radius-pill) !important;}.rounded-top{border-top-left-radius: 0.25rem !important; border-top-right-radius: 0.25rem !important;}.rounded-top-0{border-top-left-radius: 0 !important; border-top-right-radius: 0 !important;}.rounded-top-1{border-top-left-radius: 0.1875rem !important; border-top-right-radius: 0.1875rem !important;}.rounded-top-2{border-top-left-radius: 0.25rem !important; border-top-right-radius: 0.25rem !important;}.rounded-top-3{border-top-left-radius: 0.375rem !important; border-top-right-radius: 0.375rem !important;}.rounded-top-circle{border-top-left-radius: 50% !important; border-top-right-radius: 50% !important;}.rounded-top-pill{border-top-left-radius: 50rem !important; border-top-right-radius: 50rem !important;}.rounded-end{border-top-right-radius: 0.25rem !important; border-bottom-right-radius: 0.25rem !important;}.rounded-end-0{border-top-right-radius: 0 !important; border-bottom-right-radius: 0 !important;}.rounded-end-1{border-top-right-radius: 0.1875rem !important; border-bottom-right-radius: 0.1875rem !important;}.rounded-end-2{border-top-right-radius: 0.25rem !important; border-bottom-right-radius: 0.25rem !important;}.rounded-end-3{border-top-right-radius: 0.375rem !important; border-bottom-right-radius: 0.375rem !important;}.rounded-end-circle{border-top-right-radius: 50% !important; border-bottom-right-radius: 50% !important;}.rounded-end-pill{border-top-right-radius: 50rem !important; border-bottom-right-radius: 50rem !important;}.rounded-bottom{border-bottom-right-radius: 0.25rem !important; border-bottom-left-radius: 0.25rem !important;}.rounded-bottom-0{border-bottom-right-radius: 0 !important; border-bottom-left-radius: 0 !important;}.rounded-bottom-1{border-bottom-right-radius: 0.1875rem !important; border-bottom-left-radius: 0.1875rem !important;}.rounded-bottom-2{border-bottom-right-radius: 0.25rem !important; border-bottom-left-radius: 0.25rem !important;}.rounded-bottom-3{border-bottom-right-radius: 0.375rem !important; border-bottom-left-radius: 0.375rem !important;}.rounded-bottom-circle{border-bottom-right-radius: 50% !important; border-bottom-left-radius: 50% !important;}.rounded-bottom-pill{border-bottom-right-radius: 50rem !important; border-bottom-left-radius: 50rem !important;}.rounded-start{border-bottom-left-radius: 0.25rem !important; border-top-left-radius: 0.25rem !important;}.rounded-start-0{border-bottom-left-radius: 0 !important; border-top-left-radius: 0 !important;}.rounded-start-1{border-bottom-left-radius: 0.1875rem !important; border-top-left-radius: 0.1875rem !important;}.rounded-start-2{border-bottom-left-radius: 0.25rem !important; border-top-left-radius: 0.25rem !important;}.rounded-start-3{border-bottom-left-radius: 0.375rem !important; border-top-left-radius: 0.375rem !important;}.rounded-start-circle{border-bottom-left-radius: 50% !important; border-top-left-radius: 50% !important;}.rounded-start-pill{border-bottom-left-radius: 50rem !important; border-top-left-radius: 50rem !important;}.visible{visibility: visible !important;}.invisible{visibility: hidden !important;}.z-n1{z-index: -1 !important;}.z-0{z-index: 0 !important;}.z-1{z-index: 1 !important;}.z-2{z-index: 2 !important;}.z-3{z-index: 3 !important;}.cursor-default{cursor: default !important;}.cursor-pointer{cursor: pointer !important;}.flex-basis-0{flex-basis: 0 !important;}.flex-basis-25{flex-basis: 25% !important;}.flex-basis-50{flex-basis: 50% !important;}.flex-basis-75{flex-basis: 75% !important;}.flex-basis-100{flex-basis: 100% !important;}.flex-basis-auto{flex-basis: auto !important;}.transition-none{transition: none !important;}.transition-base{transition: all 0.2s ease-in-out !important;}.transition-fade{transition: opacity 0.15s linear !important;}.min-w-0{min-width: 0 !important;}@media (min-width: 576px){.float-sm-start{float: left !important;}.float-sm-end{float: right !important;}.float-sm-none{float: none !important;}.object-fit-sm-contain{object-fit: contain !important;}.object-fit-sm-cover{object-fit: cover !important;}.object-fit-sm-fill{object-fit: fill !important;}.object-fit-sm-scale{object-fit: scale-down !important;}.object-fit-sm-none{object-fit: none !important;}.d-sm-inline{display: inline !important;}.d-sm-inline-block{display: inline-block !important;}.d-sm-block{display: block !important;}.d-sm-grid{display: grid !important;}.d-sm-inline-grid{display: inline-grid !important;}.d-sm-table{display: table !important;}.d-sm-table-row{display: table-row !important;}.d-sm-table-cell{display: table-cell !important;}.d-sm-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-sm-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-sm-none{display: none !important;}.d-sm-contents{display: contents !important;}.position-sm-static{position: static !important;}.position-sm-relative{position: relative !important;}.position-sm-absolute{position: absolute !important;}.position-sm-fixed{position: fixed !important;}.position-sm-sticky{position: sticky !important;}.w-sm-0{width: 0 !important;}.w-sm-25{width: 25% !important;}.w-sm-50{width: 50% !important;}.w-sm-75{width: 75% !important;}.w-sm-100{width: 100% !important;}.w-sm-auto{width: auto !important;}.mw-sm-0{max-width: 0 !important;}.mw-sm-25{max-width: 25% !important;}.mw-sm-50{max-width: 50% !important;}.mw-sm-75{max-width: 75% !important;}.mw-sm-100{max-width: 100% !important;}.mw-sm-auto{max-width: auto !important;}.h-sm-0{height: 0 !important;}.h-sm-25{height: 25% !important;}.h-sm-50{height: 50% !important;}.h-sm-75{height: 75% !important;}.h-sm-100{height: 100% !important;}.h-sm-auto{height: auto !important;}.mh-sm-0{max-height: 0 !important;}.mh-sm-25{max-height: 25% !important;}.mh-sm-50{max-height: 50% !important;}.mh-sm-75{max-height: 75% !important;}.mh-sm-100{max-height: 100% !important;}.mh-sm-auto{max-height: auto !important;}.flex-sm-fill{flex: 1 1 auto !important;}.flex-sm-row{flex-direction: row !important;}.flex-sm-column{flex-direction: column !important;}.flex-sm-row-reverse{flex-direction: row-reverse !important;}.flex-sm-column-reverse{flex-direction: column-reverse !important;}.flex-sm-grow-0{flex-grow: 0 !important;}.flex-sm-grow-1{flex-grow: 1 !important;}.flex-sm-shrink-0{flex-shrink: 0 !important;}.flex-sm-shrink-1{flex-shrink: 1 !important;}.flex-sm-wrap{flex-wrap: wrap !important;}.flex-sm-nowrap{flex-wrap: nowrap !important;}.flex-sm-wrap-reverse{flex-wrap: wrap-reverse !important;}.justify-content-sm-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-sm-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-sm-center{justify-content: center !important;}.justify-content-sm-between{justify-content: space-between !important;}.justify-content-sm-around{justify-content: space-around !important;}.justify-content-sm-evenly{justify-content: space-evenly !important;}.align-items-sm-start{align-items: flex-start !important;}.align-items-sm-end{align-items: flex-end !important;}.align-items-sm-center{align-items: center !important;}.align-items-sm-baseline{align-items: baseline !important;}.align-items-sm-stretch{align-items: stretch !important;}.align-content-sm-start{align-content: flex-start !important;}.align-content-sm-end{align-content: flex-end !important;}.align-content-sm-center{align-content: center !important;}.align-content-sm-between{align-content: space-between !important;}.align-content-sm-around{align-content: space-around !important;}.align-content-sm-stretch{align-content: stretch !important;}.align-self-sm-auto{align-self: auto !important;}.align-self-sm-start{align-self: flex-start !important;}.align-self-sm-end{align-self: flex-end !important;}.align-self-sm-center{align-self: center !important;}.align-self-sm-baseline{align-self: baseline !important;}.align-self-sm-stretch{align-self: stretch !important;}.order-sm-first{order: -1 !important;}.order-sm-last{order: 13 !important;}.order-sm-0{order: 0 !important;}.order-sm-1{order: 1 !important;}.order-sm-2{order: 2 !important;}.order-sm-3{order: 3 !important;}.order-sm-4{order: 4 !important;}.order-sm-5{order: 5 !important;}.order-sm-6{order: 6 !important;}.order-sm-7{order: 7 !important;}.order-sm-8{order: 8 !important;}.order-sm-9{order: 9 !important;}.order-sm-10{order: 10 !important;}.order-sm-11{order: 11 !important;}.order-sm-12{order: 12 !important;}.m-sm-0{margin: 0 !important;}.m-sm-1{margin: 4px !important;}.m-sm-2{margin: 8px !important;}.m-sm-3{margin: 16px !important;}.m-sm-4{margin: 24px !important;}.m-sm-5{margin: 48px !important;}.m-sm-auto{margin: auto !important;}.mx-sm-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-sm-1{margin-right: 4px !important; margin-left: 4px !important;}.mx-sm-2{margin-right: 8px !important; margin-left: 8px !important;}.mx-sm-3{margin-right: 16px !important; margin-left: 16px !important;}.mx-sm-4{margin-right: 24px !important; margin-left: 24px !important;}.mx-sm-5{margin-right: 48px !important; margin-left: 48px !important;}.mx-sm-auto{margin-right: auto !important; margin-left: auto !important;}.my-sm-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-sm-1{margin-top: 4px !important; margin-bottom: 4px !important;}.my-sm-2{margin-top: 8px !important; margin-bottom: 8px !important;}.my-sm-3{margin-top: 16px !important; margin-bottom: 16px !important;}.my-sm-4{margin-top: 24px !important; margin-bottom: 24px !important;}.my-sm-5{margin-top: 48px !important; margin-bottom: 48px !important;}.my-sm-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-sm-0{margin-top: 0 !important;}.mt-sm-1{margin-top: 4px !important;}.mt-sm-2{margin-top: 8px !important;}.mt-sm-3{margin-top: 16px !important;}.mt-sm-4{margin-top: 24px !important;}.mt-sm-5{margin-top: 48px !important;}.mt-sm-auto{margin-top: auto !important;}.me-sm-0{margin-right: 0 !important;}.me-sm-1{margin-right: 4px !important;}.me-sm-2{margin-right: 8px !important;}.me-sm-3{margin-right: 16px !important;}.me-sm-4{margin-right: 24px !important;}.me-sm-5{margin-right: 48px !important;}.me-sm-auto{margin-right: auto !important;}.mb-sm-0{margin-bottom: 0 !important;}.mb-sm-1{margin-bottom: 4px !important;}.mb-sm-2{margin-bottom: 8px !important;}.mb-sm-3{margin-bottom: 16px !important;}.mb-sm-4{margin-bottom: 24px !important;}.mb-sm-5{margin-bottom: 48px !important;}.mb-sm-auto{margin-bottom: auto !important;}.ms-sm-0{margin-left: 0 !important;}.ms-sm-1{margin-left: 4px !important;}.ms-sm-2{margin-left: 8px !important;}.ms-sm-3{margin-left: 16px !important;}.ms-sm-4{margin-left: 24px !important;}.ms-sm-5{margin-left: 48px !important;}.ms-sm-auto{margin-left: auto !important;}.m-sm-n1{margin: -4px !important;}.m-sm-n2{margin: -8px !important;}.m-sm-n3{margin: -16px !important;}.m-sm-n4{margin: -24px !important;}.m-sm-n5{margin: -48px !important;}.mx-sm-n1{margin-right: -4px !important; margin-left: -4px !important;}.mx-sm-n2{margin-right: -8px !important; margin-left: -8px !important;}.mx-sm-n3{margin-right: -16px !important; margin-left: -16px !important;}.mx-sm-n4{margin-right: -24px !important; margin-left: -24px !important;}.mx-sm-n5{margin-right: -48px !important; margin-left: -48px !important;}.my-sm-n1{margin-top: -4px !important; margin-bottom: -4px !important;}.my-sm-n2{margin-top: -8px !important; margin-bottom: -8px !important;}.my-sm-n3{margin-top: -16px !important; margin-bottom: -16px !important;}.my-sm-n4{margin-top: -24px !important; margin-bottom: -24px !important;}.my-sm-n5{margin-top: -48px !important; margin-bottom: -48px !important;}.mt-sm-n1{margin-top: -4px !important;}.mt-sm-n2{margin-top: -8px !important;}.mt-sm-n3{margin-top: -16px !important;}.mt-sm-n4{margin-top: -24px !important;}.mt-sm-n5{margin-top: -48px !important;}.me-sm-n1{margin-right: -4px !important;}.me-sm-n2{margin-right: -8px !important;}.me-sm-n3{margin-right: -16px !important;}.me-sm-n4{margin-right: -24px !important;}.me-sm-n5{margin-right: -48px !important;}.mb-sm-n1{margin-bottom: -4px !important;}.mb-sm-n2{margin-bottom: -8px !important;}.mb-sm-n3{margin-bottom: -16px !important;}.mb-sm-n4{margin-bottom: -24px !important;}.mb-sm-n5{margin-bottom: -48px !important;}.ms-sm-n1{margin-left: -4px !important;}.ms-sm-n2{margin-left: -8px !important;}.ms-sm-n3{margin-left: -16px !important;}.ms-sm-n4{margin-left: -24px !important;}.ms-sm-n5{margin-left: -48px !important;}.p-sm-0{padding: 0 !important;}.p-sm-1{padding: 4px !important;}.p-sm-2{padding: 8px !important;}.p-sm-3{padding: 16px !important;}.p-sm-4{padding: 24px !important;}.p-sm-5{padding: 48px !important;}.px-sm-0{padding-right: 0 !important; padding-left: 0 !important;}.px-sm-1{padding-right: 4px !important; padding-left: 4px !important;}.px-sm-2{padding-right: 8px !important; padding-left: 8px !important;}.px-sm-3{padding-right: 16px !important; padding-left: 16px !important;}.px-sm-4{padding-right: 24px !important; padding-left: 24px !important;}.px-sm-5{padding-right: 48px !important; padding-left: 48px !important;}.py-sm-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-sm-1{padding-top: 4px !important; padding-bottom: 4px !important;}.py-sm-2{padding-top: 8px !important; padding-bottom: 8px !important;}.py-sm-3{padding-top: 16px !important; padding-bottom: 16px !important;}.py-sm-4{padding-top: 24px !important; padding-bottom: 24px !important;}.py-sm-5{padding-top: 48px !important; padding-bottom: 48px !important;}.pt-sm-0{padding-top: 0 !important;}.pt-sm-1{padding-top: 4px !important;}.pt-sm-2{padding-top: 8px !important;}.pt-sm-3{padding-top: 16px !important;}.pt-sm-4{padding-top: 24px !important;}.pt-sm-5{padding-top: 48px !important;}.pe-sm-0{padding-right: 0 !important;}.pe-sm-1{padding-right: 4px !important;}.pe-sm-2{padding-right: 8px !important;}.pe-sm-3{padding-right: 16px !important;}.pe-sm-4{padding-right: 24px !important;}.pe-sm-5{padding-right: 48px !important;}.pb-sm-0{padding-bottom: 0 !important;}.pb-sm-1{padding-bottom: 4px !important;}.pb-sm-2{padding-bottom: 8px !important;}.pb-sm-3{padding-bottom: 16px !important;}.pb-sm-4{padding-bottom: 24px !important;}.pb-sm-5{padding-bottom: 48px !important;}.ps-sm-0{padding-left: 0 !important;}.ps-sm-1{padding-left: 4px !important;}.ps-sm-2{padding-left: 8px !important;}.ps-sm-3{padding-left: 16px !important;}.ps-sm-4{padding-left: 24px !important;}.ps-sm-5{padding-left: 48px !important;}.gap-sm-0{gap: 0 !important;}.gap-sm-1{gap: 4px !important;}.gap-sm-2{gap: 8px !important;}.gap-sm-3{gap: 16px !important;}.gap-sm-4{gap: 24px !important;}.gap-sm-5{gap: 48px !important;}.row-gap-sm-0{row-gap: 0 !important;}.row-gap-sm-1{row-gap: 4px !important;}.row-gap-sm-2{row-gap: 8px !important;}.row-gap-sm-3{row-gap: 16px !important;}.row-gap-sm-4{row-gap: 24px !important;}.row-gap-sm-5{row-gap: 48px !important;}.column-gap-sm-0{column-gap: 0 !important;}.column-gap-sm-1{column-gap: 4px !important;}.column-gap-sm-2{column-gap: 8px !important;}.column-gap-sm-3{column-gap: 16px !important;}.column-gap-sm-4{column-gap: 24px !important;}.column-gap-sm-5{column-gap: 48px !important;}.text-sm-start{text-align: left !important;}.text-sm-end{text-align: right !important;}.text-sm-center{text-align: center !important;}.flex-basis-sm-0{flex-basis: 0 !important;}.flex-basis-sm-25{flex-basis: 25% !important;}.flex-basis-sm-50{flex-basis: 50% !important;}.flex-basis-sm-75{flex-basis: 75% !important;}.flex-basis-sm-100{flex-basis: 100% !important;}.flex-basis-sm-auto{flex-basis: auto !important;}}@media (min-width: 768px){.float-md-start{float: left !important;}.float-md-end{float: right !important;}.float-md-none{float: none !important;}.object-fit-md-contain{object-fit: contain !important;}.object-fit-md-cover{object-fit: cover !important;}.object-fit-md-fill{object-fit: fill !important;}.object-fit-md-scale{object-fit: scale-down !important;}.object-fit-md-none{object-fit: none !important;}.d-md-inline{display: inline !important;}.d-md-inline-block{display: inline-block !important;}.d-md-block{display: block !important;}.d-md-grid{display: grid !important;}.d-md-inline-grid{display: inline-grid !important;}.d-md-table{display: table !important;}.d-md-table-row{display: table-row !important;}.d-md-table-cell{display: table-cell !important;}.d-md-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-md-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-md-none{display: none !important;}.d-md-contents{display: contents !important;}.position-md-static{position: static !important;}.position-md-relative{position: relative !important;}.position-md-absolute{position: absolute !important;}.position-md-fixed{position: fixed !important;}.position-md-sticky{position: sticky !important;}.w-md-0{width: 0 !important;}.w-md-25{width: 25% !important;}.w-md-50{width: 50% !important;}.w-md-75{width: 75% !important;}.w-md-100{width: 100% !important;}.w-md-auto{width: auto !important;}.mw-md-0{max-width: 0 !important;}.mw-md-25{max-width: 25% !important;}.mw-md-50{max-width: 50% !important;}.mw-md-75{max-width: 75% !important;}.mw-md-100{max-width: 100% !important;}.mw-md-auto{max-width: auto !important;}.h-md-0{height: 0 !important;}.h-md-25{height: 25% !important;}.h-md-50{height: 50% !important;}.h-md-75{height: 75% !important;}.h-md-100{height: 100% !important;}.h-md-auto{height: auto !important;}.mh-md-0{max-height: 0 !important;}.mh-md-25{max-height: 25% !important;}.mh-md-50{max-height: 50% !important;}.mh-md-75{max-height: 75% !important;}.mh-md-100{max-height: 100% !important;}.mh-md-auto{max-height: auto !important;}.flex-md-fill{flex: 1 1 auto !important;}.flex-md-row{flex-direction: row !important;}.flex-md-column{flex-direction: column !important;}.flex-md-row-reverse{flex-direction: row-reverse !important;}.flex-md-column-reverse{flex-direction: column-reverse !important;}.flex-md-grow-0{flex-grow: 0 !important;}.flex-md-grow-1{flex-grow: 1 !important;}.flex-md-shrink-0{flex-shrink: 0 !important;}.flex-md-shrink-1{flex-shrink: 1 !important;}.flex-md-wrap{flex-wrap: wrap !important;}.flex-md-nowrap{flex-wrap: nowrap !important;}.flex-md-wrap-reverse{flex-wrap: wrap-reverse !important;}.justify-content-md-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-md-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-md-center{justify-content: center !important;}.justify-content-md-between{justify-content: space-between !important;}.justify-content-md-around{justify-content: space-around !important;}.justify-content-md-evenly{justify-content: space-evenly !important;}.align-items-md-start{align-items: flex-start !important;}.align-items-md-end{align-items: flex-end !important;}.align-items-md-center{align-items: center !important;}.align-items-md-baseline{align-items: baseline !important;}.align-items-md-stretch{align-items: stretch !important;}.align-content-md-start{align-content: flex-start !important;}.align-content-md-end{align-content: flex-end !important;}.align-content-md-center{align-content: center !important;}.align-content-md-between{align-content: space-between !important;}.align-content-md-around{align-content: space-around !important;}.align-content-md-stretch{align-content: stretch !important;}.align-self-md-auto{align-self: auto !important;}.align-self-md-start{align-self: flex-start !important;}.align-self-md-end{align-self: flex-end !important;}.align-self-md-center{align-self: center !important;}.align-self-md-baseline{align-self: baseline !important;}.align-self-md-stretch{align-self: stretch !important;}.order-md-first{order: -1 !important;}.order-md-last{order: 13 !important;}.order-md-0{order: 0 !important;}.order-md-1{order: 1 !important;}.order-md-2{order: 2 !important;}.order-md-3{order: 3 !important;}.order-md-4{order: 4 !important;}.order-md-5{order: 5 !important;}.order-md-6{order: 6 !important;}.order-md-7{order: 7 !important;}.order-md-8{order: 8 !important;}.order-md-9{order: 9 !important;}.order-md-10{order: 10 !important;}.order-md-11{order: 11 !important;}.order-md-12{order: 12 !important;}.m-md-0{margin: 0 !important;}.m-md-1{margin: 4px !important;}.m-md-2{margin: 8px !important;}.m-md-3{margin: 16px !important;}.m-md-4{margin: 24px !important;}.m-md-5{margin: 48px !important;}.m-md-auto{margin: auto !important;}.mx-md-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-md-1{margin-right: 4px !important; margin-left: 4px !important;}.mx-md-2{margin-right: 8px !important; margin-left: 8px !important;}.mx-md-3{margin-right: 16px !important; margin-left: 16px !important;}.mx-md-4{margin-right: 24px !important; margin-left: 24px !important;}.mx-md-5{margin-right: 48px !important; margin-left: 48px !important;}.mx-md-auto{margin-right: auto !important; margin-left: auto !important;}.my-md-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-md-1{margin-top: 4px !important; margin-bottom: 4px !important;}.my-md-2{margin-top: 8px !important; margin-bottom: 8px !important;}.my-md-3{margin-top: 16px !important; margin-bottom: 16px !important;}.my-md-4{margin-top: 24px !important; margin-bottom: 24px !important;}.my-md-5{margin-top: 48px !important; margin-bottom: 48px !important;}.my-md-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-md-0{margin-top: 0 !important;}.mt-md-1{margin-top: 4px !important;}.mt-md-2{margin-top: 8px !important;}.mt-md-3{margin-top: 16px !important;}.mt-md-4{margin-top: 24px !important;}.mt-md-5{margin-top: 48px !important;}.mt-md-auto{margin-top: auto !important;}.me-md-0{margin-right: 0 !important;}.me-md-1{margin-right: 4px !important;}.me-md-2{margin-right: 8px !important;}.me-md-3{margin-right: 16px !important;}.me-md-4{margin-right: 24px !important;}.me-md-5{margin-right: 48px !important;}.me-md-auto{margin-right: auto !important;}.mb-md-0{margin-bottom: 0 !important;}.mb-md-1{margin-bottom: 4px !important;}.mb-md-2{margin-bottom: 8px !important;}.mb-md-3{margin-bottom: 16px !important;}.mb-md-4{margin-bottom: 24px !important;}.mb-md-5{margin-bottom: 48px !important;}.mb-md-auto{margin-bottom: auto !important;}.ms-md-0{margin-left: 0 !important;}.ms-md-1{margin-left: 4px !important;}.ms-md-2{margin-left: 8px !important;}.ms-md-3{margin-left: 16px !important;}.ms-md-4{margin-left: 24px !important;}.ms-md-5{margin-left: 48px !important;}.ms-md-auto{margin-left: auto !important;}.m-md-n1{margin: -4px !important;}.m-md-n2{margin: -8px !important;}.m-md-n3{margin: -16px !important;}.m-md-n4{margin: -24px !important;}.m-md-n5{margin: -48px !important;}.mx-md-n1{margin-right: -4px !important; margin-left: -4px !important;}.mx-md-n2{margin-right: -8px !important; margin-left: -8px !important;}.mx-md-n3{margin-right: -16px !important; margin-left: -16px !important;}.mx-md-n4{margin-right: -24px !important; margin-left: -24px !important;}.mx-md-n5{margin-right: -48px !important; margin-left: -48px !important;}.my-md-n1{margin-top: -4px !important; margin-bottom: -4px !important;}.my-md-n2{margin-top: -8px !important; margin-bottom: -8px !important;}.my-md-n3{margin-top: -16px !important; margin-bottom: -16px !important;}.my-md-n4{margin-top: -24px !important; margin-bottom: -24px !important;}.my-md-n5{margin-top: -48px !important; margin-bottom: -48px !important;}.mt-md-n1{margin-top: -4px !important;}.mt-md-n2{margin-top: -8px !important;}.mt-md-n3{margin-top: -16px !important;}.mt-md-n4{margin-top: -24px !important;}.mt-md-n5{margin-top: -48px !important;}.me-md-n1{margin-right: -4px !important;}.me-md-n2{margin-right: -8px !important;}.me-md-n3{margin-right: -16px !important;}.me-md-n4{margin-right: -24px !important;}.me-md-n5{margin-right: -48px !important;}.mb-md-n1{margin-bottom: -4px !important;}.mb-md-n2{margin-bottom: -8px !important;}.mb-md-n3{margin-bottom: -16px !important;}.mb-md-n4{margin-bottom: -24px !important;}.mb-md-n5{margin-bottom: -48px !important;}.ms-md-n1{margin-left: -4px !important;}.ms-md-n2{margin-left: -8px !important;}.ms-md-n3{margin-left: -16px !important;}.ms-md-n4{margin-left: -24px !important;}.ms-md-n5{margin-left: -48px !important;}.p-md-0{padding: 0 !important;}.p-md-1{padding: 4px !important;}.p-md-2{padding: 8px !important;}.p-md-3{padding: 16px !important;}.p-md-4{padding: 24px !important;}.p-md-5{padding: 48px !important;}.px-md-0{padding-right: 0 !important; padding-left: 0 !important;}.px-md-1{padding-right: 4px !important; padding-left: 4px !important;}.px-md-2{padding-right: 8px !important; padding-left: 8px !important;}.px-md-3{padding-right: 16px !important; padding-left: 16px !important;}.px-md-4{padding-right: 24px !important; padding-left: 24px !important;}.px-md-5{padding-right: 48px !important; padding-left: 48px !important;}.py-md-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-md-1{padding-top: 4px !important; padding-bottom: 4px !important;}.py-md-2{padding-top: 8px !important; padding-bottom: 8px !important;}.py-md-3{padding-top: 16px !important; padding-bottom: 16px !important;}.py-md-4{padding-top: 24px !important; padding-bottom: 24px !important;}.py-md-5{padding-top: 48px !important; padding-bottom: 48px !important;}.pt-md-0{padding-top: 0 !important;}.pt-md-1{padding-top: 4px !important;}.pt-md-2{padding-top: 8px !important;}.pt-md-3{padding-top: 16px !important;}.pt-md-4{padding-top: 24px !important;}.pt-md-5{padding-top: 48px !important;}.pe-md-0{padding-right: 0 !important;}.pe-md-1{padding-right: 4px !important;}.pe-md-2{padding-right: 8px !important;}.pe-md-3{padding-right: 16px !important;}.pe-md-4{padding-right: 24px !important;}.pe-md-5{padding-right: 48px !important;}.pb-md-0{padding-bottom: 0 !important;}.pb-md-1{padding-bottom: 4px !important;}.pb-md-2{padding-bottom: 8px !important;}.pb-md-3{padding-bottom: 16px !important;}.pb-md-4{padding-bottom: 24px !important;}.pb-md-5{padding-bottom: 48px !important;}.ps-md-0{padding-left: 0 !important;}.ps-md-1{padding-left: 4px !important;}.ps-md-2{padding-left: 8px !important;}.ps-md-3{padding-left: 16px !important;}.ps-md-4{padding-left: 24px !important;}.ps-md-5{padding-left: 48px !important;}.gap-md-0{gap: 0 !important;}.gap-md-1{gap: 4px !important;}.gap-md-2{gap: 8px !important;}.gap-md-3{gap: 16px !important;}.gap-md-4{gap: 24px !important;}.gap-md-5{gap: 48px !important;}.row-gap-md-0{row-gap: 0 !important;}.row-gap-md-1{row-gap: 4px !important;}.row-gap-md-2{row-gap: 8px !important;}.row-gap-md-3{row-gap: 16px !important;}.row-gap-md-4{row-gap: 24px !important;}.row-gap-md-5{row-gap: 48px !important;}.column-gap-md-0{column-gap: 0 !important;}.column-gap-md-1{column-gap: 4px !important;}.column-gap-md-2{column-gap: 8px !important;}.column-gap-md-3{column-gap: 16px !important;}.column-gap-md-4{column-gap: 24px !important;}.column-gap-md-5{column-gap: 48px !important;}.text-md-start{text-align: left !important;}.text-md-end{text-align: right !important;}.text-md-center{text-align: center !important;}.flex-basis-md-0{flex-basis: 0 !important;}.flex-basis-md-25{flex-basis: 25% !important;}.flex-basis-md-50{flex-basis: 50% !important;}.flex-basis-md-75{flex-basis: 75% !important;}.flex-basis-md-100{flex-basis: 100% !important;}.flex-basis-md-auto{flex-basis: auto !important;}}@media (min-width: 992px){.float-lg-start{float: left !important;}.float-lg-end{float: right !important;}.float-lg-none{float: none !important;}.object-fit-lg-contain{object-fit: contain !important;}.object-fit-lg-cover{object-fit: cover !important;}.object-fit-lg-fill{object-fit: fill !important;}.object-fit-lg-scale{object-fit: scale-down !important;}.object-fit-lg-none{object-fit: none !important;}.d-lg-inline{display: inline !important;}.d-lg-inline-block{display: inline-block !important;}.d-lg-block{display: block !important;}.d-lg-grid{display: grid !important;}.d-lg-inline-grid{display: inline-grid !important;}.d-lg-table{display: table !important;}.d-lg-table-row{display: table-row !important;}.d-lg-table-cell{display: table-cell !important;}.d-lg-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-lg-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-lg-none{display: none !important;}.d-lg-contents{display: contents !important;}.position-lg-static{position: static !important;}.position-lg-relative{position: relative !important;}.position-lg-absolute{position: absolute !important;}.position-lg-fixed{position: fixed !important;}.position-lg-sticky{position: sticky !important;}.w-lg-0{width: 0 !important;}.w-lg-25{width: 25% !important;}.w-lg-50{width: 50% !important;}.w-lg-75{width: 75% !important;}.w-lg-100{width: 100% !important;}.w-lg-auto{width: auto !important;}.mw-lg-0{max-width: 0 !important;}.mw-lg-25{max-width: 25% !important;}.mw-lg-50{max-width: 50% !important;}.mw-lg-75{max-width: 75% !important;}.mw-lg-100{max-width: 100% !important;}.mw-lg-auto{max-width: auto !important;}.h-lg-0{height: 0 !important;}.h-lg-25{height: 25% !important;}.h-lg-50{height: 50% !important;}.h-lg-75{height: 75% !important;}.h-lg-100{height: 100% !important;}.h-lg-auto{height: auto !important;}.mh-lg-0{max-height: 0 !important;}.mh-lg-25{max-height: 25% !important;}.mh-lg-50{max-height: 50% !important;}.mh-lg-75{max-height: 75% !important;}.mh-lg-100{max-height: 100% !important;}.mh-lg-auto{max-height: auto !important;}.flex-lg-fill{flex: 1 1 auto !important;}.flex-lg-row{flex-direction: row !important;}.flex-lg-column{flex-direction: column !important;}.flex-lg-row-reverse{flex-direction: row-reverse !important;}.flex-lg-column-reverse{flex-direction: column-reverse !important;}.flex-lg-grow-0{flex-grow: 0 !important;}.flex-lg-grow-1{flex-grow: 1 !important;}.flex-lg-shrink-0{flex-shrink: 0 !important;}.flex-lg-shrink-1{flex-shrink: 1 !important;}.flex-lg-wrap{flex-wrap: wrap !important;}.flex-lg-nowrap{flex-wrap: nowrap !important;}.flex-lg-wrap-reverse{flex-wrap: wrap-reverse !important;}.justify-content-lg-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-lg-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-lg-center{justify-content: center !important;}.justify-content-lg-between{justify-content: space-between !important;}.justify-content-lg-around{justify-content: space-around !important;}.justify-content-lg-evenly{justify-content: space-evenly !important;}.align-items-lg-start{align-items: flex-start !important;}.align-items-lg-end{align-items: flex-end !important;}.align-items-lg-center{align-items: center !important;}.align-items-lg-baseline{align-items: baseline !important;}.align-items-lg-stretch{align-items: stretch !important;}.align-content-lg-start{align-content: flex-start !important;}.align-content-lg-end{align-content: flex-end !important;}.align-content-lg-center{align-content: center !important;}.align-content-lg-between{align-content: space-between !important;}.align-content-lg-around{align-content: space-around !important;}.align-content-lg-stretch{align-content: stretch !important;}.align-self-lg-auto{align-self: auto !important;}.align-self-lg-start{align-self: flex-start !important;}.align-self-lg-end{align-self: flex-end !important;}.align-self-lg-center{align-self: center !important;}.align-self-lg-baseline{align-self: baseline !important;}.align-self-lg-stretch{align-self: stretch !important;}.order-lg-first{order: -1 !important;}.order-lg-last{order: 13 !important;}.order-lg-0{order: 0 !important;}.order-lg-1{order: 1 !important;}.order-lg-2{order: 2 !important;}.order-lg-3{order: 3 !important;}.order-lg-4{order: 4 !important;}.order-lg-5{order: 5 !important;}.order-lg-6{order: 6 !important;}.order-lg-7{order: 7 !important;}.order-lg-8{order: 8 !important;}.order-lg-9{order: 9 !important;}.order-lg-10{order: 10 !important;}.order-lg-11{order: 11 !important;}.order-lg-12{order: 12 !important;}.m-lg-0{margin: 0 !important;}.m-lg-1{margin: 4px !important;}.m-lg-2{margin: 8px !important;}.m-lg-3{margin: 16px !important;}.m-lg-4{margin: 24px !important;}.m-lg-5{margin: 48px !important;}.m-lg-auto{margin: auto !important;}.mx-lg-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-lg-1{margin-right: 4px !important; margin-left: 4px !important;}.mx-lg-2{margin-right: 8px !important; margin-left: 8px !important;}.mx-lg-3{margin-right: 16px !important; margin-left: 16px !important;}.mx-lg-4{margin-right: 24px !important; margin-left: 24px !important;}.mx-lg-5{margin-right: 48px !important; margin-left: 48px !important;}.mx-lg-auto{margin-right: auto !important; margin-left: auto !important;}.my-lg-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-lg-1{margin-top: 4px !important; margin-bottom: 4px !important;}.my-lg-2{margin-top: 8px !important; margin-bottom: 8px !important;}.my-lg-3{margin-top: 16px !important; margin-bottom: 16px !important;}.my-lg-4{margin-top: 24px !important; margin-bottom: 24px !important;}.my-lg-5{margin-top: 48px !important; margin-bottom: 48px !important;}.my-lg-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-lg-0{margin-top: 0 !important;}.mt-lg-1{margin-top: 4px !important;}.mt-lg-2{margin-top: 8px !important;}.mt-lg-3{margin-top: 16px !important;}.mt-lg-4{margin-top: 24px !important;}.mt-lg-5{margin-top: 48px !important;}.mt-lg-auto{margin-top: auto !important;}.me-lg-0{margin-right: 0 !important;}.me-lg-1{margin-right: 4px !important;}.me-lg-2{margin-right: 8px !important;}.me-lg-3{margin-right: 16px !important;}.me-lg-4{margin-right: 24px !important;}.me-lg-5{margin-right: 48px !important;}.me-lg-auto{margin-right: auto !important;}.mb-lg-0{margin-bottom: 0 !important;}.mb-lg-1{margin-bottom: 4px !important;}.mb-lg-2{margin-bottom: 8px !important;}.mb-lg-3{margin-bottom: 16px !important;}.mb-lg-4{margin-bottom: 24px !important;}.mb-lg-5{margin-bottom: 48px !important;}.mb-lg-auto{margin-bottom: auto !important;}.ms-lg-0{margin-left: 0 !important;}.ms-lg-1{margin-left: 4px !important;}.ms-lg-2{margin-left: 8px !important;}.ms-lg-3{margin-left: 16px !important;}.ms-lg-4{margin-left: 24px !important;}.ms-lg-5{margin-left: 48px !important;}.ms-lg-auto{margin-left: auto !important;}.m-lg-n1{margin: -4px !important;}.m-lg-n2{margin: -8px !important;}.m-lg-n3{margin: -16px !important;}.m-lg-n4{margin: -24px !important;}.m-lg-n5{margin: -48px !important;}.mx-lg-n1{margin-right: -4px !important; margin-left: -4px !important;}.mx-lg-n2{margin-right: -8px !important; margin-left: -8px !important;}.mx-lg-n3{margin-right: -16px !important; margin-left: -16px !important;}.mx-lg-n4{margin-right: -24px !important; margin-left: -24px !important;}.mx-lg-n5{margin-right: -48px !important; margin-left: -48px !important;}.my-lg-n1{margin-top: -4px !important; margin-bottom: -4px !important;}.my-lg-n2{margin-top: -8px !important; margin-bottom: -8px !important;}.my-lg-n3{margin-top: -16px !important; margin-bottom: -16px !important;}.my-lg-n4{margin-top: -24px !important; margin-bottom: -24px !important;}.my-lg-n5{margin-top: -48px !important; margin-bottom: -48px !important;}.mt-lg-n1{margin-top: -4px !important;}.mt-lg-n2{margin-top: -8px !important;}.mt-lg-n3{margin-top: -16px !important;}.mt-lg-n4{margin-top: -24px !important;}.mt-lg-n5{margin-top: -48px !important;}.me-lg-n1{margin-right: -4px !important;}.me-lg-n2{margin-right: -8px !important;}.me-lg-n3{margin-right: -16px !important;}.me-lg-n4{margin-right: -24px !important;}.me-lg-n5{margin-right: -48px !important;}.mb-lg-n1{margin-bottom: -4px !important;}.mb-lg-n2{margin-bottom: -8px !important;}.mb-lg-n3{margin-bottom: -16px !important;}.mb-lg-n4{margin-bottom: -24px !important;}.mb-lg-n5{margin-bottom: -48px !important;}.ms-lg-n1{margin-left: -4px !important;}.ms-lg-n2{margin-left: -8px !important;}.ms-lg-n3{margin-left: -16px !important;}.ms-lg-n4{margin-left: -24px !important;}.ms-lg-n5{margin-left: -48px !important;}.p-lg-0{padding: 0 !important;}.p-lg-1{padding: 4px !important;}.p-lg-2{padding: 8px !important;}.p-lg-3{padding: 16px !important;}.p-lg-4{padding: 24px !important;}.p-lg-5{padding: 48px !important;}.px-lg-0{padding-right: 0 !important; padding-left: 0 !important;}.px-lg-1{padding-right: 4px !important; padding-left: 4px !important;}.px-lg-2{padding-right: 8px !important; padding-left: 8px !important;}.px-lg-3{padding-right: 16px !important; padding-left: 16px !important;}.px-lg-4{padding-right: 24px !important; padding-left: 24px !important;}.px-lg-5{padding-right: 48px !important; padding-left: 48px !important;}.py-lg-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-lg-1{padding-top: 4px !important; padding-bottom: 4px !important;}.py-lg-2{padding-top: 8px !important; padding-bottom: 8px !important;}.py-lg-3{padding-top: 16px !important; padding-bottom: 16px !important;}.py-lg-4{padding-top: 24px !important; padding-bottom: 24px !important;}.py-lg-5{padding-top: 48px !important; padding-bottom: 48px !important;}.pt-lg-0{padding-top: 0 !important;}.pt-lg-1{padding-top: 4px !important;}.pt-lg-2{padding-top: 8px !important;}.pt-lg-3{padding-top: 16px !important;}.pt-lg-4{padding-top: 24px !important;}.pt-lg-5{padding-top: 48px !important;}.pe-lg-0{padding-right: 0 !important;}.pe-lg-1{padding-right: 4px !important;}.pe-lg-2{padding-right: 8px !important;}.pe-lg-3{padding-right: 16px !important;}.pe-lg-4{padding-right: 24px !important;}.pe-lg-5{padding-right: 48px !important;}.pb-lg-0{padding-bottom: 0 !important;}.pb-lg-1{padding-bottom: 4px !important;}.pb-lg-2{padding-bottom: 8px !important;}.pb-lg-3{padding-bottom: 16px !important;}.pb-lg-4{padding-bottom: 24px !important;}.pb-lg-5{padding-bottom: 48px !important;}.ps-lg-0{padding-left: 0 !important;}.ps-lg-1{padding-left: 4px !important;}.ps-lg-2{padding-left: 8px !important;}.ps-lg-3{padding-left: 16px !important;}.ps-lg-4{padding-left: 24px !important;}.ps-lg-5{padding-left: 48px !important;}.gap-lg-0{gap: 0 !important;}.gap-lg-1{gap: 4px !important;}.gap-lg-2{gap: 8px !important;}.gap-lg-3{gap: 16px !important;}.gap-lg-4{gap: 24px !important;}.gap-lg-5{gap: 48px !important;}.row-gap-lg-0{row-gap: 0 !important;}.row-gap-lg-1{row-gap: 4px !important;}.row-gap-lg-2{row-gap: 8px !important;}.row-gap-lg-3{row-gap: 16px !important;}.row-gap-lg-4{row-gap: 24px !important;}.row-gap-lg-5{row-gap: 48px !important;}.column-gap-lg-0{column-gap: 0 !important;}.column-gap-lg-1{column-gap: 4px !important;}.column-gap-lg-2{column-gap: 8px !important;}.column-gap-lg-3{column-gap: 16px !important;}.column-gap-lg-4{column-gap: 24px !important;}.column-gap-lg-5{column-gap: 48px !important;}.text-lg-start{text-align: left !important;}.text-lg-end{text-align: right !important;}.text-lg-center{text-align: center !important;}.flex-basis-lg-0{flex-basis: 0 !important;}.flex-basis-lg-25{flex-basis: 25% !important;}.flex-basis-lg-50{flex-basis: 50% !important;}.flex-basis-lg-75{flex-basis: 75% !important;}.flex-basis-lg-100{flex-basis: 100% !important;}.flex-basis-lg-auto{flex-basis: auto !important;}}@media (min-width: 1200px){.float-xl-start{float: left !important;}.float-xl-end{float: right !important;}.float-xl-none{float: none !important;}.object-fit-xl-contain{object-fit: contain !important;}.object-fit-xl-cover{object-fit: cover !important;}.object-fit-xl-fill{object-fit: fill !important;}.object-fit-xl-scale{object-fit: scale-down !important;}.object-fit-xl-none{object-fit: none !important;}.d-xl-inline{display: inline !important;}.d-xl-inline-block{display: inline-block !important;}.d-xl-block{display: block !important;}.d-xl-grid{display: grid !important;}.d-xl-inline-grid{display: inline-grid !important;}.d-xl-table{display: table !important;}.d-xl-table-row{display: table-row !important;}.d-xl-table-cell{display: table-cell !important;}.d-xl-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-xl-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-xl-none{display: none !important;}.d-xl-contents{display: contents !important;}.position-xl-static{position: static !important;}.position-xl-relative{position: relative !important;}.position-xl-absolute{position: absolute !important;}.position-xl-fixed{position: fixed !important;}.position-xl-sticky{position: sticky !important;}.w-xl-0{width: 0 !important;}.w-xl-25{width: 25% !important;}.w-xl-50{width: 50% !important;}.w-xl-75{width: 75% !important;}.w-xl-100{width: 100% !important;}.w-xl-auto{width: auto !important;}.mw-xl-0{max-width: 0 !important;}.mw-xl-25{max-width: 25% !important;}.mw-xl-50{max-width: 50% !important;}.mw-xl-75{max-width: 75% !important;}.mw-xl-100{max-width: 100% !important;}.mw-xl-auto{max-width: auto !important;}.h-xl-0{height: 0 !important;}.h-xl-25{height: 25% !important;}.h-xl-50{height: 50% !important;}.h-xl-75{height: 75% !important;}.h-xl-100{height: 100% !important;}.h-xl-auto{height: auto !important;}.mh-xl-0{max-height: 0 !important;}.mh-xl-25{max-height: 25% !important;}.mh-xl-50{max-height: 50% !important;}.mh-xl-75{max-height: 75% !important;}.mh-xl-100{max-height: 100% !important;}.mh-xl-auto{max-height: auto !important;}.flex-xl-fill{flex: 1 1 auto !important;}.flex-xl-row{flex-direction: row !important;}.flex-xl-column{flex-direction: column !important;}.flex-xl-row-reverse{flex-direction: row-reverse !important;}.flex-xl-column-reverse{flex-direction: column-reverse !important;}.flex-xl-grow-0{flex-grow: 0 !important;}.flex-xl-grow-1{flex-grow: 1 !important;}.flex-xl-shrink-0{flex-shrink: 0 !important;}.flex-xl-shrink-1{flex-shrink: 1 !important;}.flex-xl-wrap{flex-wrap: wrap !important;}.flex-xl-nowrap{flex-wrap: nowrap !important;}.flex-xl-wrap-reverse{flex-wrap: wrap-reverse !important;}.justify-content-xl-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-xl-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-xl-center{justify-content: center !important;}.justify-content-xl-between{justify-content: space-between !important;}.justify-content-xl-around{justify-content: space-around !important;}.justify-content-xl-evenly{justify-content: space-evenly !important;}.align-items-xl-start{align-items: flex-start !important;}.align-items-xl-end{align-items: flex-end !important;}.align-items-xl-center{align-items: center !important;}.align-items-xl-baseline{align-items: baseline !important;}.align-items-xl-stretch{align-items: stretch !important;}.align-content-xl-start{align-content: flex-start !important;}.align-content-xl-end{align-content: flex-end !important;}.align-content-xl-center{align-content: center !important;}.align-content-xl-between{align-content: space-between !important;}.align-content-xl-around{align-content: space-around !important;}.align-content-xl-stretch{align-content: stretch !important;}.align-self-xl-auto{align-self: auto !important;}.align-self-xl-start{align-self: flex-start !important;}.align-self-xl-end{align-self: flex-end !important;}.align-self-xl-center{align-self: center !important;}.align-self-xl-baseline{align-self: baseline !important;}.align-self-xl-stretch{align-self: stretch !important;}.order-xl-first{order: -1 !important;}.order-xl-last{order: 13 !important;}.order-xl-0{order: 0 !important;}.order-xl-1{order: 1 !important;}.order-xl-2{order: 2 !important;}.order-xl-3{order: 3 !important;}.order-xl-4{order: 4 !important;}.order-xl-5{order: 5 !important;}.order-xl-6{order: 6 !important;}.order-xl-7{order: 7 !important;}.order-xl-8{order: 8 !important;}.order-xl-9{order: 9 !important;}.order-xl-10{order: 10 !important;}.order-xl-11{order: 11 !important;}.order-xl-12{order: 12 !important;}.m-xl-0{margin: 0 !important;}.m-xl-1{margin: 4px !important;}.m-xl-2{margin: 8px !important;}.m-xl-3{margin: 16px !important;}.m-xl-4{margin: 24px !important;}.m-xl-5{margin: 48px !important;}.m-xl-auto{margin: auto !important;}.mx-xl-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-xl-1{margin-right: 4px !important; margin-left: 4px !important;}.mx-xl-2{margin-right: 8px !important; margin-left: 8px !important;}.mx-xl-3{margin-right: 16px !important; margin-left: 16px !important;}.mx-xl-4{margin-right: 24px !important; margin-left: 24px !important;}.mx-xl-5{margin-right: 48px !important; margin-left: 48px !important;}.mx-xl-auto{margin-right: auto !important; margin-left: auto !important;}.my-xl-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-xl-1{margin-top: 4px !important; margin-bottom: 4px !important;}.my-xl-2{margin-top: 8px !important; margin-bottom: 8px !important;}.my-xl-3{margin-top: 16px !important; margin-bottom: 16px !important;}.my-xl-4{margin-top: 24px !important; margin-bottom: 24px !important;}.my-xl-5{margin-top: 48px !important; margin-bottom: 48px !important;}.my-xl-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-xl-0{margin-top: 0 !important;}.mt-xl-1{margin-top: 4px !important;}.mt-xl-2{margin-top: 8px !important;}.mt-xl-3{margin-top: 16px !important;}.mt-xl-4{margin-top: 24px !important;}.mt-xl-5{margin-top: 48px !important;}.mt-xl-auto{margin-top: auto !important;}.me-xl-0{margin-right: 0 !important;}.me-xl-1{margin-right: 4px !important;}.me-xl-2{margin-right: 8px !important;}.me-xl-3{margin-right: 16px !important;}.me-xl-4{margin-right: 24px !important;}.me-xl-5{margin-right: 48px !important;}.me-xl-auto{margin-right: auto !important;}.mb-xl-0{margin-bottom: 0 !important;}.mb-xl-1{margin-bottom: 4px !important;}.mb-xl-2{margin-bottom: 8px !important;}.mb-xl-3{margin-bottom: 16px !important;}.mb-xl-4{margin-bottom: 24px !important;}.mb-xl-5{margin-bottom: 48px !important;}.mb-xl-auto{margin-bottom: auto !important;}.ms-xl-0{margin-left: 0 !important;}.ms-xl-1{margin-left: 4px !important;}.ms-xl-2{margin-left: 8px !important;}.ms-xl-3{margin-left: 16px !important;}.ms-xl-4{margin-left: 24px !important;}.ms-xl-5{margin-left: 48px !important;}.ms-xl-auto{margin-left: auto !important;}.m-xl-n1{margin: -4px !important;}.m-xl-n2{margin: -8px !important;}.m-xl-n3{margin: -16px !important;}.m-xl-n4{margin: -24px !important;}.m-xl-n5{margin: -48px !important;}.mx-xl-n1{margin-right: -4px !important; margin-left: -4px !important;}.mx-xl-n2{margin-right: -8px !important; margin-left: -8px !important;}.mx-xl-n3{margin-right: -16px !important; margin-left: -16px !important;}.mx-xl-n4{margin-right: -24px !important; margin-left: -24px !important;}.mx-xl-n5{margin-right: -48px !important; margin-left: -48px !important;}.my-xl-n1{margin-top: -4px !important; margin-bottom: -4px !important;}.my-xl-n2{margin-top: -8px !important; margin-bottom: -8px !important;}.my-xl-n3{margin-top: -16px !important; margin-bottom: -16px !important;}.my-xl-n4{margin-top: -24px !important; margin-bottom: -24px !important;}.my-xl-n5{margin-top: -48px !important; margin-bottom: -48px !important;}.mt-xl-n1{margin-top: -4px !important;}.mt-xl-n2{margin-top: -8px !important;}.mt-xl-n3{margin-top: -16px !important;}.mt-xl-n4{margin-top: -24px !important;}.mt-xl-n5{margin-top: -48px !important;}.me-xl-n1{margin-right: -4px !important;}.me-xl-n2{margin-right: -8px !important;}.me-xl-n3{margin-right: -16px !important;}.me-xl-n4{margin-right: -24px !important;}.me-xl-n5{margin-right: -48px !important;}.mb-xl-n1{margin-bottom: -4px !important;}.mb-xl-n2{margin-bottom: -8px !important;}.mb-xl-n3{margin-bottom: -16px !important;}.mb-xl-n4{margin-bottom: -24px !important;}.mb-xl-n5{margin-bottom: -48px !important;}.ms-xl-n1{margin-left: -4px !important;}.ms-xl-n2{margin-left: -8px !important;}.ms-xl-n3{margin-left: -16px !important;}.ms-xl-n4{margin-left: -24px !important;}.ms-xl-n5{margin-left: -48px !important;}.p-xl-0{padding: 0 !important;}.p-xl-1{padding: 4px !important;}.p-xl-2{padding: 8px !important;}.p-xl-3{padding: 16px !important;}.p-xl-4{padding: 24px !important;}.p-xl-5{padding: 48px !important;}.px-xl-0{padding-right: 0 !important; padding-left: 0 !important;}.px-xl-1{padding-right: 4px !important; padding-left: 4px !important;}.px-xl-2{padding-right: 8px !important; padding-left: 8px !important;}.px-xl-3{padding-right: 16px !important; padding-left: 16px !important;}.px-xl-4{padding-right: 24px !important; padding-left: 24px !important;}.px-xl-5{padding-right: 48px !important; padding-left: 48px !important;}.py-xl-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-xl-1{padding-top: 4px !important; padding-bottom: 4px !important;}.py-xl-2{padding-top: 8px !important; padding-bottom: 8px !important;}.py-xl-3{padding-top: 16px !important; padding-bottom: 16px !important;}.py-xl-4{padding-top: 24px !important; padding-bottom: 24px !important;}.py-xl-5{padding-top: 48px !important; padding-bottom: 48px !important;}.pt-xl-0{padding-top: 0 !important;}.pt-xl-1{padding-top: 4px !important;}.pt-xl-2{padding-top: 8px !important;}.pt-xl-3{padding-top: 16px !important;}.pt-xl-4{padding-top: 24px !important;}.pt-xl-5{padding-top: 48px !important;}.pe-xl-0{padding-right: 0 !important;}.pe-xl-1{padding-right: 4px !important;}.pe-xl-2{padding-right: 8px !important;}.pe-xl-3{padding-right: 16px !important;}.pe-xl-4{padding-right: 24px !important;}.pe-xl-5{padding-right: 48px !important;}.pb-xl-0{padding-bottom: 0 !important;}.pb-xl-1{padding-bottom: 4px !important;}.pb-xl-2{padding-bottom: 8px !important;}.pb-xl-3{padding-bottom: 16px !important;}.pb-xl-4{padding-bottom: 24px !important;}.pb-xl-5{padding-bottom: 48px !important;}.ps-xl-0{padding-left: 0 !important;}.ps-xl-1{padding-left: 4px !important;}.ps-xl-2{padding-left: 8px !important;}.ps-xl-3{padding-left: 16px !important;}.ps-xl-4{padding-left: 24px !important;}.ps-xl-5{padding-left: 48px !important;}.gap-xl-0{gap: 0 !important;}.gap-xl-1{gap: 4px !important;}.gap-xl-2{gap: 8px !important;}.gap-xl-3{gap: 16px !important;}.gap-xl-4{gap: 24px !important;}.gap-xl-5{gap: 48px !important;}.row-gap-xl-0{row-gap: 0 !important;}.row-gap-xl-1{row-gap: 4px !important;}.row-gap-xl-2{row-gap: 8px !important;}.row-gap-xl-3{row-gap: 16px !important;}.row-gap-xl-4{row-gap: 24px !important;}.row-gap-xl-5{row-gap: 48px !important;}.column-gap-xl-0{column-gap: 0 !important;}.column-gap-xl-1{column-gap: 4px !important;}.column-gap-xl-2{column-gap: 8px !important;}.column-gap-xl-3{column-gap: 16px !important;}.column-gap-xl-4{column-gap: 24px !important;}.column-gap-xl-5{column-gap: 48px !important;}.text-xl-start{text-align: left !important;}.text-xl-end{text-align: right !important;}.text-xl-center{text-align: center !important;}.flex-basis-xl-0{flex-basis: 0 !important;}.flex-basis-xl-25{flex-basis: 25% !important;}.flex-basis-xl-50{flex-basis: 50% !important;}.flex-basis-xl-75{flex-basis: 75% !important;}.flex-basis-xl-100{flex-basis: 100% !important;}.flex-basis-xl-auto{flex-basis: auto !important;}}@media (min-width: 1400px){.float-xxl-start{float: left !important;}.float-xxl-end{float: right !important;}.float-xxl-none{float: none !important;}.object-fit-xxl-contain{object-fit: contain !important;}.object-fit-xxl-cover{object-fit: cover !important;}.object-fit-xxl-fill{object-fit: fill !important;}.object-fit-xxl-scale{object-fit: scale-down !important;}.object-fit-xxl-none{object-fit: none !important;}.d-xxl-inline{display: inline !important;}.d-xxl-inline-block{display: inline-block !important;}.d-xxl-block{display: block !important;}.d-xxl-grid{display: grid !important;}.d-xxl-inline-grid{display: inline-grid !important;}.d-xxl-table{display: table !important;}.d-xxl-table-row{display: table-row !important;}.d-xxl-table-cell{display: table-cell !important;}.d-xxl-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-xxl-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-xxl-none{display: none !important;}.d-xxl-contents{display: contents !important;}.position-xxl-static{position: static !important;}.position-xxl-relative{position: relative !important;}.position-xxl-absolute{position: absolute !important;}.position-xxl-fixed{position: fixed !important;}.position-xxl-sticky{position: sticky !important;}.w-xxl-0{width: 0 !important;}.w-xxl-25{width: 25% !important;}.w-xxl-50{width: 50% !important;}.w-xxl-75{width: 75% !important;}.w-xxl-100{width: 100% !important;}.w-xxl-auto{width: auto !important;}.mw-xxl-0{max-width: 0 !important;}.mw-xxl-25{max-width: 25% !important;}.mw-xxl-50{max-width: 50% !important;}.mw-xxl-75{max-width: 75% !important;}.mw-xxl-100{max-width: 100% !important;}.mw-xxl-auto{max-width: auto !important;}.h-xxl-0{height: 0 !important;}.h-xxl-25{height: 25% !important;}.h-xxl-50{height: 50% !important;}.h-xxl-75{height: 75% !important;}.h-xxl-100{height: 100% !important;}.h-xxl-auto{height: auto !important;}.mh-xxl-0{max-height: 0 !important;}.mh-xxl-25{max-height: 25% !important;}.mh-xxl-50{max-height: 50% !important;}.mh-xxl-75{max-height: 75% !important;}.mh-xxl-100{max-height: 100% !important;}.mh-xxl-auto{max-height: auto !important;}.flex-xxl-fill{flex: 1 1 auto !important;}.flex-xxl-row{flex-direction: row !important;}.flex-xxl-column{flex-direction: column !important;}.flex-xxl-row-reverse{flex-direction: row-reverse !important;}.flex-xxl-column-reverse{flex-direction: column-reverse !important;}.flex-xxl-grow-0{flex-grow: 0 !important;}.flex-xxl-grow-1{flex-grow: 1 !important;}.flex-xxl-shrink-0{flex-shrink: 0 !important;}.flex-xxl-shrink-1{flex-shrink: 1 !important;}.flex-xxl-wrap{flex-wrap: wrap !important;}.flex-xxl-nowrap{flex-wrap: nowrap !important;}.flex-xxl-wrap-reverse{flex-wrap: wrap-reverse !important;}.justify-content-xxl-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-xxl-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-xxl-center{justify-content: center !important;}.justify-content-xxl-between{justify-content: space-between !important;}.justify-content-xxl-around{justify-content: space-around !important;}.justify-content-xxl-evenly{justify-content: space-evenly !important;}.align-items-xxl-start{align-items: flex-start !important;}.align-items-xxl-end{align-items: flex-end !important;}.align-items-xxl-center{align-items: center !important;}.align-items-xxl-baseline{align-items: baseline !important;}.align-items-xxl-stretch{align-items: stretch !important;}.align-content-xxl-start{align-content: flex-start !important;}.align-content-xxl-end{align-content: flex-end !important;}.align-content-xxl-center{align-content: center !important;}.align-content-xxl-between{align-content: space-between !important;}.align-content-xxl-around{align-content: space-around !important;}.align-content-xxl-stretch{align-content: stretch !important;}.align-self-xxl-auto{align-self: auto !important;}.align-self-xxl-start{align-self: flex-start !important;}.align-self-xxl-end{align-self: flex-end !important;}.align-self-xxl-center{align-self: center !important;}.align-self-xxl-baseline{align-self: baseline !important;}.align-self-xxl-stretch{align-self: stretch !important;}.order-xxl-first{order: -1 !important;}.order-xxl-last{order: 13 !important;}.order-xxl-0{order: 0 !important;}.order-xxl-1{order: 1 !important;}.order-xxl-2{order: 2 !important;}.order-xxl-3{order: 3 !important;}.order-xxl-4{order: 4 !important;}.order-xxl-5{order: 5 !important;}.order-xxl-6{order: 6 !important;}.order-xxl-7{order: 7 !important;}.order-xxl-8{order: 8 !important;}.order-xxl-9{order: 9 !important;}.order-xxl-10{order: 10 !important;}.order-xxl-11{order: 11 !important;}.order-xxl-12{order: 12 !important;}.m-xxl-0{margin: 0 !important;}.m-xxl-1{margin: 4px !important;}.m-xxl-2{margin: 8px !important;}.m-xxl-3{margin: 16px !important;}.m-xxl-4{margin: 24px !important;}.m-xxl-5{margin: 48px !important;}.m-xxl-auto{margin: auto !important;}.mx-xxl-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-xxl-1{margin-right: 4px !important; margin-left: 4px !important;}.mx-xxl-2{margin-right: 8px !important; margin-left: 8px !important;}.mx-xxl-3{margin-right: 16px !important; margin-left: 16px !important;}.mx-xxl-4{margin-right: 24px !important; margin-left: 24px !important;}.mx-xxl-5{margin-right: 48px !important; margin-left: 48px !important;}.mx-xxl-auto{margin-right: auto !important; margin-left: auto !important;}.my-xxl-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-xxl-1{margin-top: 4px !important; margin-bottom: 4px !important;}.my-xxl-2{margin-top: 8px !important; margin-bottom: 8px !important;}.my-xxl-3{margin-top: 16px !important; margin-bottom: 16px !important;}.my-xxl-4{margin-top: 24px !important; margin-bottom: 24px !important;}.my-xxl-5{margin-top: 48px !important; margin-bottom: 48px !important;}.my-xxl-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-xxl-0{margin-top: 0 !important;}.mt-xxl-1{margin-top: 4px !important;}.mt-xxl-2{margin-top: 8px !important;}.mt-xxl-3{margin-top: 16px !important;}.mt-xxl-4{margin-top: 24px !important;}.mt-xxl-5{margin-top: 48px !important;}.mt-xxl-auto{margin-top: auto !important;}.me-xxl-0{margin-right: 0 !important;}.me-xxl-1{margin-right: 4px !important;}.me-xxl-2{margin-right: 8px !important;}.me-xxl-3{margin-right: 16px !important;}.me-xxl-4{margin-right: 24px !important;}.me-xxl-5{margin-right: 48px !important;}.me-xxl-auto{margin-right: auto !important;}.mb-xxl-0{margin-bottom: 0 !important;}.mb-xxl-1{margin-bottom: 4px !important;}.mb-xxl-2{margin-bottom: 8px !important;}.mb-xxl-3{margin-bottom: 16px !important;}.mb-xxl-4{margin-bottom: 24px !important;}.mb-xxl-5{margin-bottom: 48px !important;}.mb-xxl-auto{margin-bottom: auto !important;}.ms-xxl-0{margin-left: 0 !important;}.ms-xxl-1{margin-left: 4px !important;}.ms-xxl-2{margin-left: 8px !important;}.ms-xxl-3{margin-left: 16px !important;}.ms-xxl-4{margin-left: 24px !important;}.ms-xxl-5{margin-left: 48px !important;}.ms-xxl-auto{margin-left: auto !important;}.m-xxl-n1{margin: -4px !important;}.m-xxl-n2{margin: -8px !important;}.m-xxl-n3{margin: -16px !important;}.m-xxl-n4{margin: -24px !important;}.m-xxl-n5{margin: -48px !important;}.mx-xxl-n1{margin-right: -4px !important; margin-left: -4px !important;}.mx-xxl-n2{margin-right: -8px !important; margin-left: -8px !important;}.mx-xxl-n3{margin-right: -16px !important; margin-left: -16px !important;}.mx-xxl-n4{margin-right: -24px !important; margin-left: -24px !important;}.mx-xxl-n5{margin-right: -48px !important; margin-left: -48px !important;}.my-xxl-n1{margin-top: -4px !important; margin-bottom: -4px !important;}.my-xxl-n2{margin-top: -8px !important; margin-bottom: -8px !important;}.my-xxl-n3{margin-top: -16px !important; margin-bottom: -16px !important;}.my-xxl-n4{margin-top: -24px !important; margin-bottom: -24px !important;}.my-xxl-n5{margin-top: -48px !important; margin-bottom: -48px !important;}.mt-xxl-n1{margin-top: -4px !important;}.mt-xxl-n2{margin-top: -8px !important;}.mt-xxl-n3{margin-top: -16px !important;}.mt-xxl-n4{margin-top: -24px !important;}.mt-xxl-n5{margin-top: -48px !important;}.me-xxl-n1{margin-right: -4px !important;}.me-xxl-n2{margin-right: -8px !important;}.me-xxl-n3{margin-right: -16px !important;}.me-xxl-n4{margin-right: -24px !important;}.me-xxl-n5{margin-right: -48px !important;}.mb-xxl-n1{margin-bottom: -4px !important;}.mb-xxl-n2{margin-bottom: -8px !important;}.mb-xxl-n3{margin-bottom: -16px !important;}.mb-xxl-n4{margin-bottom: -24px !important;}.mb-xxl-n5{margin-bottom: -48px !important;}.ms-xxl-n1{margin-left: -4px !important;}.ms-xxl-n2{margin-left: -8px !important;}.ms-xxl-n3{margin-left: -16px !important;}.ms-xxl-n4{margin-left: -24px !important;}.ms-xxl-n5{margin-left: -48px !important;}.p-xxl-0{padding: 0 !important;}.p-xxl-1{padding: 4px !important;}.p-xxl-2{padding: 8px !important;}.p-xxl-3{padding: 16px !important;}.p-xxl-4{padding: 24px !important;}.p-xxl-5{padding: 48px !important;}.px-xxl-0{padding-right: 0 !important; padding-left: 0 !important;}.px-xxl-1{padding-right: 4px !important; padding-left: 4px !important;}.px-xxl-2{padding-right: 8px !important; padding-left: 8px !important;}.px-xxl-3{padding-right: 16px !important; padding-left: 16px !important;}.px-xxl-4{padding-right: 24px !important; padding-left: 24px !important;}.px-xxl-5{padding-right: 48px !important; padding-left: 48px !important;}.py-xxl-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-xxl-1{padding-top: 4px !important; padding-bottom: 4px !important;}.py-xxl-2{padding-top: 8px !important; padding-bottom: 8px !important;}.py-xxl-3{padding-top: 16px !important; padding-bottom: 16px !important;}.py-xxl-4{padding-top: 24px !important; padding-bottom: 24px !important;}.py-xxl-5{padding-top: 48px !important; padding-bottom: 48px !important;}.pt-xxl-0{padding-top: 0 !important;}.pt-xxl-1{padding-top: 4px !important;}.pt-xxl-2{padding-top: 8px !important;}.pt-xxl-3{padding-top: 16px !important;}.pt-xxl-4{padding-top: 24px !important;}.pt-xxl-5{padding-top: 48px !important;}.pe-xxl-0{padding-right: 0 !important;}.pe-xxl-1{padding-right: 4px !important;}.pe-xxl-2{padding-right: 8px !important;}.pe-xxl-3{padding-right: 16px !important;}.pe-xxl-4{padding-right: 24px !important;}.pe-xxl-5{padding-right: 48px !important;}.pb-xxl-0{padding-bottom: 0 !important;}.pb-xxl-1{padding-bottom: 4px !important;}.pb-xxl-2{padding-bottom: 8px !important;}.pb-xxl-3{padding-bottom: 16px !important;}.pb-xxl-4{padding-bottom: 24px !important;}.pb-xxl-5{padding-bottom: 48px !important;}.ps-xxl-0{padding-left: 0 !important;}.ps-xxl-1{padding-left: 4px !important;}.ps-xxl-2{padding-left: 8px !important;}.ps-xxl-3{padding-left: 16px !important;}.ps-xxl-4{padding-left: 24px !important;}.ps-xxl-5{padding-left: 48px !important;}.gap-xxl-0{gap: 0 !important;}.gap-xxl-1{gap: 4px !important;}.gap-xxl-2{gap: 8px !important;}.gap-xxl-3{gap: 16px !important;}.gap-xxl-4{gap: 24px !important;}.gap-xxl-5{gap: 48px !important;}.row-gap-xxl-0{row-gap: 0 !important;}.row-gap-xxl-1{row-gap: 4px !important;}.row-gap-xxl-2{row-gap: 8px !important;}.row-gap-xxl-3{row-gap: 16px !important;}.row-gap-xxl-4{row-gap: 24px !important;}.row-gap-xxl-5{row-gap: 48px !important;}.column-gap-xxl-0{column-gap: 0 !important;}.column-gap-xxl-1{column-gap: 4px !important;}.column-gap-xxl-2{column-gap: 8px !important;}.column-gap-xxl-3{column-gap: 16px !important;}.column-gap-xxl-4{column-gap: 24px !important;}.column-gap-xxl-5{column-gap: 48px !important;}.text-xxl-start{text-align: left !important;}.text-xxl-end{text-align: right !important;}.text-xxl-center{text-align: center !important;}.flex-basis-xxl-0{flex-basis: 0 !important;}.flex-basis-xxl-25{flex-basis: 25% !important;}.flex-basis-xxl-50{flex-basis: 50% !important;}.flex-basis-xxl-75{flex-basis: 75% !important;}.flex-basis-xxl-100{flex-basis: 100% !important;}.flex-basis-xxl-auto{flex-basis: auto !important;}}@media (min-width: 1200px){.fs-1{font-size: 1.75rem !important;}.fs-2{font-size: 1.3125rem !important;}}@media print{.d-print-inline{display: inline !important;}.d-print-inline-block{display: inline-block !important;}.d-print-block{display: block !important;}.d-print-grid{display: grid !important;}.d-print-inline-grid{display: inline-grid !important;}.d-print-table{display: table !important;}.d-print-table-row{display: table-row !important;}.d-print-table-cell{display: table-cell !important;}.d-print-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-print-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-print-none{display: none !important;}.d-print-contents{display: contents !important;}.w-print-0{width: 0 !important;}.w-print-25{width: 25% !important;}.w-print-50{width: 50% !important;}.w-print-75{width: 75% !important;}.w-print-100{width: 100% !important;}.w-print-auto{width: auto !important;}.p-print-0{padding: 0 !important;}.p-print-1{padding: 4px !important;}.p-print-2{padding: 8px !important;}.p-print-3{padding: 16px !important;}.p-print-4{padding: 24px !important;}.p-print-5{padding: 48px !important;}.px-print-0{padding-right: 0 !important; padding-left: 0 !important;}.px-print-1{padding-right: 4px !important; padding-left: 4px !important;}.px-print-2{padding-right: 8px !important; padding-left: 8px !important;}.px-print-3{padding-right: 16px !important; padding-left: 16px !important;}.px-print-4{padding-right: 24px !important; padding-left: 24px !important;}.px-print-5{padding-right: 48px !important; padding-left: 48px !important;}.py-print-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-print-1{padding-top: 4px !important; padding-bottom: 4px !important;}.py-print-2{padding-top: 8px !important; padding-bottom: 8px !important;}.py-print-3{padding-top: 16px !important; padding-bottom: 16px !important;}.py-print-4{padding-top: 24px !important; padding-bottom: 24px !important;}.py-print-5{padding-top: 48px !important; padding-bottom: 48px !important;}.pt-print-0{padding-top: 0 !important;}.pt-print-1{padding-top: 4px !important;}.pt-print-2{padding-top: 8px !important;}.pt-print-3{padding-top: 16px !important;}.pt-print-4{padding-top: 24px !important;}.pt-print-5{padding-top: 48px !important;}.pe-print-0{padding-right: 0 !important;}.pe-print-1{padding-right: 4px !important;}.pe-print-2{padding-right: 8px !important;}.pe-print-3{padding-right: 16px !important;}.pe-print-4{padding-right: 24px !important;}.pe-print-5{padding-right: 48px !important;}.pb-print-0{padding-bottom: 0 !important;}.pb-print-1{padding-bottom: 4px !important;}.pb-print-2{padding-bottom: 8px !important;}.pb-print-3{padding-bottom: 16px !important;}.pb-print-4{padding-bottom: 24px !important;}.pb-print-5{padding-bottom: 48px !important;}.ps-print-0{padding-left: 0 !important;}.ps-print-1{padding-left: 4px !important;}.ps-print-2{padding-left: 8px !important;}.ps-print-3{padding-left: 16px !important;}.ps-print-4{padding-left: 24px !important;}.ps-print-5{padding-left: 48px !important;}}

/* /web/static/src/scss/bootstrap_review.scss */
 .alert{clear: both;}.accordion .collapsing > .card-body:first-child, .accordion .collapse.show > .card-body:first-child{margin-top: var(--border-width);}.toast-header{background-clip: border-box;}@media (min-width: 576px){:not(.s_popup) > .modal .modal-dialog{height: 100%; padding: 1.75rem 0; margin: 0 auto;}:not(.s_popup) > .modal .modal-content{max-height: 100%;}:not(.s_popup) > .modal .modal-header, :not(.s_popup) > .modal .modal-footer{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;}:not(.s_popup) > .modal .modal-body{overflow: auto; min-height: 0;}}.modal-backdrop{display: none;}.modal:not([data-bs-backdrop="false"]){background-color: rgba(0, 0, 0, 0.5);}.form-check .form-check-input:not(:disabled), .form-check .form-check-input:not(:disabled) + label{cursor: pointer;}.form-check:hover, .form-check:hover .form-check-input:not(:disabled){border-color: #71639e;}.form-select:where(:not(:disabled)):hover{border-color: #71639e;}.dropdown-menu[x-placement^="top"], .dropdown-menu[x-placement^="right"], .dropdown-menu[x-placement^="bottom"], .dropdown-menu[x-placement^="left"]{right: auto;}.popover{right: auto;}.carousel-indicators{list-style: none;}@keyframes progress-bar-stripes{0%{background-position-x: var(--progress-height, 1rem);}}

/* /web/static/src/scss/bootstrap_review_backend.scss */
 .btn{--btn-disabled-border-color: transparent;}.btn-primary{--btn-color: #FFFFFF; --btn-bg: #71639e; --btn-border-color: #71639e; --btn-hover-color: #FFFFFF; --btn-hover-bg: #5a4f7f; --btn-hover-border-color: #443b60; --btn-focus-shadow-rgb: 134, 122, 173; --btn-active-color: #FFFFFF; --btn-active-bg: #443b60; --btn-active-border-color: #443b60; --btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); --btn-disabled-color: #FFFFFF; --btn-disabled-bg: #71639e; --btn-disabled-border-color: #71639e;}.btn-secondary{--btn-color: #343a40; --btn-bg: #dee2e6; --btn-border-color: #dee2e6; --btn-hover-color: #212529; --btn-hover-bg: #ced4da; --btn-hover-border-color: #ced4da; --btn-focus-shadow-rgb: 197, 201, 205; --btn-active-color: #212529; --btn-active-bg: #dddbe8; --btn-active-border-color: #71639e; --btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); --btn-disabled-color: #000000; --btn-disabled-bg: #dee2e6; --btn-disabled-border-color: #dee2e6;}.btn-light{--btn-color: #495057; --btn-bg: #FFFFFF; --btn-border-color: #FFFFFF; --btn-hover-color: #212529; --btn-hover-bg: #e9ecef; --btn-hover-border-color: #e9ecef; --btn-focus-shadow-rgb: 228, 229, 230; --btn-active-color: #212529; --btn-active-bg: #dddbe8; --btn-active-border-color: #71639e; --btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); --btn-disabled-color: #000000; --btn-disabled-bg: #FFFFFF; --btn-disabled-border-color: #FFFFFF;}.btn-outline-secondary{--btn-color: #495057; --btn-bg: transparent; --btn-border-color: #dee2e6; --btn-hover-color: #343a40; --btn-hover-bg: #e9ecef; --btn-hover-border-color: #dee2e6; --btn-focus-shadow-rgb: 200, 204, 209; --btn-active-color: #212529; --btn-active-bg: #dddbe8; --btn-active-border-color: #71639e; --btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); --btn-disabled-color: #000000; --btn-disabled-bg: transparent; --btn-disabled-border-color: #dee2e6;}.btn-fill-primary{--btn-color: #FFFFFF; --btn-bg: #71639e; --btn-border-color: #71639e; --btn-hover-color: #FFFFFF; --btn-hover-bg: #5a4f7f; --btn-hover-border-color: #443b60; --btn-focus-shadow-rgb: 134, 122, 173; --btn-active-color: #FFFFFF; --btn-active-bg: #443b60; --btn-active-border-color: #443b60; --btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); --btn-disabled-color: #FFFFFF; --btn-disabled-bg: #71639e; --btn-disabled-border-color: #71639e;}.btn-fill-secondary{--btn-color: #343a40; --btn-bg: #dee2e6; --btn-border-color: #dee2e6; --btn-hover-color: #212529; --btn-hover-bg: #ced4da; --btn-hover-border-color: #ced4da; --btn-focus-shadow-rgb: 197, 201, 205; --btn-active-color: #212529; --btn-active-bg: #dddbe8; --btn-active-border-color: #71639e; --btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); --btn-disabled-color: #000000; --btn-disabled-bg: #dee2e6; --btn-disabled-border-color: #dee2e6;}.btn-fill-light{--btn-color: #495057; --btn-bg: #FFFFFF; --btn-border-color: #FFFFFF; --btn-hover-color: #212529; --btn-hover-bg: #e9ecef; --btn-hover-border-color: #e9ecef; --btn-focus-shadow-rgb: 228, 229, 230; --btn-active-color: #212529; --btn-active-bg: #dddbe8; --btn-active-border-color: #71639e; --btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); --btn-disabled-color: #000000; --btn-disabled-bg: #FFFFFF; --btn-disabled-border-color: #FFFFFF;}.btn-outline-primary{--btn-color: #71639e; --btn-border-color: #71639e; --btn-hover-color: #FFFFFF; --btn-hover-bg: #71639e; --btn-hover-border-color: #71639e; --btn-focus-shadow-rgb: 113, 99, 158; --btn-active-color: #FFFFFF; --btn-active-bg: #71639e; --btn-active-border-color: #71639e; --btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); --btn-disabled-color: #71639e; --btn-disabled-bg: transparent; --btn-disabled-border-color: #71639e; --gradient: none;}.btn-success{--btn-color: #FFFFFF; --btn-bg: #28a745; --btn-border-color: #28a745; --btn-hover-color: #FFFFFF; --btn-hover-bg: #228e3b; --btn-hover-border-color: #208637; --btn-focus-shadow-rgb: 72, 180, 97; --btn-active-color: #FFFFFF; --btn-active-bg: #208637; --btn-active-border-color: #1e7d34; --btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); --btn-disabled-color: #FFFFFF; --btn-disabled-bg: #28a745; --btn-disabled-border-color: #28a745;}.btn-outline-success{--btn-color: #28a745; --btn-border-color: #28a745; --btn-hover-color: #FFFFFF; --btn-hover-bg: #28a745; --btn-hover-border-color: #28a745; --btn-focus-shadow-rgb: 40, 167, 69; --btn-active-color: #FFFFFF; --btn-active-bg: #28a745; --btn-active-border-color: #28a745; --btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); --btn-disabled-color: #28a745; --btn-disabled-bg: transparent; --btn-disabled-border-color: #28a745; --gradient: none;}.btn-info{--btn-color: #FFFFFF; --btn-bg: #17a2b8; --btn-border-color: #17a2b8; --btn-hover-color: #FFFFFF; --btn-hover-bg: #148a9c; --btn-hover-border-color: #128293; --btn-focus-shadow-rgb: 58, 176, 195; --btn-active-color: #FFFFFF; --btn-active-bg: #128293; --btn-active-border-color: #117a8a; --btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); --btn-disabled-color: #FFFFFF; --btn-disabled-bg: #17a2b8; --btn-disabled-border-color: #17a2b8;}.btn-outline-info{--btn-color: #17a2b8; --btn-border-color: #17a2b8; --btn-hover-color: #FFFFFF; --btn-hover-bg: #17a2b8; --btn-hover-border-color: #17a2b8; --btn-focus-shadow-rgb: 23, 162, 184; --btn-active-color: #FFFFFF; --btn-active-bg: #17a2b8; --btn-active-border-color: #17a2b8; --btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); --btn-disabled-color: #17a2b8; --btn-disabled-bg: transparent; --btn-disabled-border-color: #17a2b8; --gradient: none;}.btn-warning{--btn-color: #000000; --btn-bg: #ffac00; --btn-border-color: #ffac00; --btn-hover-color: #000000; --btn-hover-bg: #ffb826; --btn-hover-border-color: #ffb41a; --btn-focus-shadow-rgb: 217, 146, 0; --btn-active-color: #000000; --btn-active-bg: #ffbd33; --btn-active-border-color: #ffb41a; --btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); --btn-disabled-color: #000000; --btn-disabled-bg: #ffac00; --btn-disabled-border-color: #ffac00;}.btn-outline-warning{--btn-color: #ffac00; --btn-border-color: #ffac00; --btn-hover-color: #000000; --btn-hover-bg: #ffac00; --btn-hover-border-color: #ffac00; --btn-focus-shadow-rgb: 255, 172, 0; --btn-active-color: #000000; --btn-active-bg: #ffac00; --btn-active-border-color: #ffac00; --btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); --btn-disabled-color: #ffac00; --btn-disabled-bg: transparent; --btn-disabled-border-color: #ffac00; --gradient: none;}.btn-danger{--btn-color: #FFFFFF; --btn-bg: #dc3545; --btn-border-color: #dc3545; --btn-hover-color: #FFFFFF; --btn-hover-bg: #bb2d3b; --btn-hover-border-color: #b02a37; --btn-focus-shadow-rgb: 225, 83, 97; --btn-active-color: #FFFFFF; --btn-active-bg: #b02a37; --btn-active-border-color: #a52834; --btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); --btn-disabled-color: #FFFFFF; --btn-disabled-bg: #dc3545; --btn-disabled-border-color: #dc3545;}.btn-outline-danger{--btn-color: #dc3545; --btn-border-color: #dc3545; --btn-hover-color: #FFFFFF; --btn-hover-bg: #dc3545; --btn-hover-border-color: #dc3545; --btn-focus-shadow-rgb: 220, 53, 69; --btn-active-color: #FFFFFF; --btn-active-bg: #dc3545; --btn-active-border-color: #dc3545; --btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); --btn-disabled-color: #dc3545; --btn-disabled-bg: transparent; --btn-disabled-border-color: #dc3545; --gradient: none;}.btn-outline-light{--btn-color: #f8f9fa; --btn-border-color: #f8f9fa; --btn-hover-color: #000000; --btn-hover-bg: #f8f9fa; --btn-hover-border-color: #f8f9fa; --btn-focus-shadow-rgb: 248, 249, 250; --btn-active-color: #000000; --btn-active-bg: #f8f9fa; --btn-active-border-color: #f8f9fa; --btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); --btn-disabled-color: #f8f9fa; --btn-disabled-bg: transparent; --btn-disabled-border-color: #f8f9fa; --gradient: none;}.btn-dark{--btn-color: #FFFFFF; --btn-bg: #212529; --btn-border-color: #212529; --btn-hover-color: #FFFFFF; --btn-hover-bg: #1c1f23; --btn-hover-border-color: #1a1e21; --btn-focus-shadow-rgb: 66, 70, 73; --btn-active-color: #FFFFFF; --btn-active-bg: #1a1e21; --btn-active-border-color: #191c1f; --btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); --btn-disabled-color: #FFFFFF; --btn-disabled-bg: #212529; --btn-disabled-border-color: #212529;}.btn-outline-dark{--btn-color: #212529; --btn-border-color: #212529; --btn-hover-color: #FFFFFF; --btn-hover-bg: #212529; --btn-hover-border-color: #212529; --btn-focus-shadow-rgb: 33, 37, 41; --btn-active-color: #FFFFFF; --btn-active-bg: #212529; --btn-active-border-color: #212529; --btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); --btn-disabled-color: #212529; --btn-disabled-bg: transparent; --btn-disabled-border-color: #212529; --gradient: none;}.btn-favourite{--btn-color: #000000; --btn-bg: #f3cc00; --btn-border-color: #f3cc00; --btn-hover-color: #000000; --btn-hover-bg: #f5d426; --btn-hover-border-color: #f4d11a; --btn-focus-shadow-rgb: 207, 173, 0; --btn-active-color: #000000; --btn-active-bg: #f5d633; --btn-active-border-color: #f4d11a; --btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); --btn-disabled-color: #000000; --btn-disabled-bg: #f3cc00; --btn-disabled-border-color: #f3cc00;}.btn-group > :not(.btn-check:first-child) + .btn, .btn-group > .btn-group:not(:first-child){margin-left: var(--btn-group-gap, var(--border-width));}.text-primary{--color: RGBA(113, 99, 158, var(--text-opacity, 1)); color: var(--color) !important;}a.text-primary:hover, a.text-primary:focus, a.text-primary:active, a.text-primary.active, button.text-primary:hover, button.text-primary:focus, button.text-primary:active, button.text-primary.active{--color: RGBA(79, 69, 111, var(--text-opacity, 1)); color: var(--color) !important;}.text-secondary{--color: RGBA(222, 226, 230, var(--text-opacity, 1)); color: var(--color) !important;}a.text-secondary:hover, a.text-secondary:focus, a.text-secondary:active, a.text-secondary.active, button.text-secondary:hover, button.text-secondary:focus, button.text-secondary:active, button.text-secondary.active{--color: RGBA(155, 158, 161, var(--text-opacity, 1)); color: var(--color) !important;}.text-success{--color: RGBA(0, 136, 24, var(--text-opacity, 1)); color: var(--color) !important;}a.text-success:hover, a.text-success:focus, a.text-success:active, a.text-success.active, button.text-success:hover, button.text-success:focus, button.text-success:active, button.text-success.active{--color: RGBA(0, 95, 17, var(--text-opacity, 1)); color: var(--color) !important;}.text-info{--color: RGBA(1, 128, 165, var(--text-opacity, 1)); color: var(--color) !important;}a.text-info:hover, a.text-info:focus, a.text-info:active, a.text-info.active, button.text-info:hover, button.text-info:focus, button.text-info:active, button.text-info.active{--color: RGBA(1, 90, 116, var(--text-opacity, 1)); color: var(--color) !important;}.text-warning{--color: RGBA(154, 107, 1, var(--text-opacity, 1)); color: var(--color) !important;}a.text-warning:hover, a.text-warning:focus, a.text-warning:active, a.text-warning.active, button.text-warning:hover, button.text-warning:focus, button.text-warning:active, button.text-warning.active{--color: RGBA(108, 75, 1, var(--text-opacity, 1)); color: var(--color) !important;}.text-danger{--color: RGBA(210, 63, 58, var(--text-opacity, 1)); color: var(--color) !important;}a.text-danger:hover, a.text-danger:focus, a.text-danger:active, a.text-danger.active, button.text-danger:hover, button.text-danger:focus, button.text-danger:active, button.text-danger.active{--color: RGBA(147, 44, 41, var(--text-opacity, 1)); color: var(--color) !important;}.text-light{--color: RGBA(248, 249, 250, var(--text-opacity, 1)); color: var(--color) !important;}a.text-light:hover, a.text-light:focus, a.text-light:active, a.text-light.active, button.text-light:hover, button.text-light:focus, button.text-light:active, button.text-light.active{--color: RGBA(174, 174, 175, var(--text-opacity, 1)); color: var(--color) !important;}.text-dark{--color: RGBA(33, 37, 41, var(--text-opacity, 1)); color: var(--color) !important;}a.text-dark:hover, a.text-dark:focus, a.text-dark:active, a.text-dark.active, button.text-dark:hover, button.text-dark:focus, button.text-dark:active, button.text-dark.active{--color: RGBA(23, 26, 29, var(--text-opacity, 1)); color: var(--color) !important;}.btn-link{font-weight: 500;}.btn-link.btn-primary, .btn-link.text-primary{text-transform: none; color: #6c757d !important;}.btn-link.btn-primary:hover, .btn-link.btn-primary:focus, .btn-link.btn-primary.focus, .btn-link.text-primary:hover, .btn-link.text-primary:focus, .btn-link.text-primary.focus{color: #71639e !important;}.btn-link.btn-primary, .btn-link.btn-primary:hover, .btn-link.btn-primary:focus, .btn-link.btn-primary:active, .btn-link.btn-primary.active, .btn-link.text-primary, .btn-link.text-primary:hover, .btn-link.text-primary:focus, .btn-link.text-primary:active, .btn-link.text-primary.active{border-color: transparent !important; background-color: transparent !important;}.btn-link.btn-primary:hover:active:focus, .btn-link.text-primary:hover:active:focus{box-shadow: none; outline: none;}.btn-link.btn-primary.text-muted, .btn-link.btn-primary .text-muted, .btn-link.text-primary.text-muted, .btn-link.text-primary .text-muted{color: rgba(73, 80, 87, 0.76);}.btn-link.btn-primary.text-muted:hover, .btn-link.btn-primary.text-muted:focus, .btn-link.btn-primary.text-muted.focus, .btn-link.btn-primary .text-muted:hover, .btn-link.btn-primary .text-muted:focus, .btn-link.btn-primary .text-muted.focus, .btn-link.text-primary.text-muted:hover, .btn-link.text-primary.text-muted:focus, .btn-link.text-primary.text-muted.focus, .btn-link.text-primary .text-muted:hover, .btn-link.text-primary .text-muted:focus, .btn-link.text-primary .text-muted.focus{color: #71639e !important;}.btn-link.btn-secondary, .btn-link.text-secondary{text-transform: none; color: #6c757d !important;}.btn-link.btn-secondary:hover, .btn-link.btn-secondary:focus, .btn-link.btn-secondary.focus, .btn-link.text-secondary:hover, .btn-link.text-secondary:focus, .btn-link.text-secondary.focus{color: #dee2e6 !important;}.btn-link.btn-secondary, .btn-link.btn-secondary:hover, .btn-link.btn-secondary:focus, .btn-link.btn-secondary:active, .btn-link.btn-secondary.active, .btn-link.text-secondary, .btn-link.text-secondary:hover, .btn-link.text-secondary:focus, .btn-link.text-secondary:active, .btn-link.text-secondary.active{border-color: transparent !important; background-color: transparent !important;}.btn-link.btn-secondary:hover:active:focus, .btn-link.text-secondary:hover:active:focus{box-shadow: none; outline: none;}.btn-link.btn-secondary.text-muted, .btn-link.btn-secondary .text-muted, .btn-link.text-secondary.text-muted, .btn-link.text-secondary .text-muted{color: rgba(73, 80, 87, 0.76);}.btn-link.btn-secondary.text-muted:hover, .btn-link.btn-secondary.text-muted:focus, .btn-link.btn-secondary.text-muted.focus, .btn-link.btn-secondary .text-muted:hover, .btn-link.btn-secondary .text-muted:focus, .btn-link.btn-secondary .text-muted.focus, .btn-link.text-secondary.text-muted:hover, .btn-link.text-secondary.text-muted:focus, .btn-link.text-secondary.text-muted.focus, .btn-link.text-secondary .text-muted:hover, .btn-link.text-secondary .text-muted:focus, .btn-link.text-secondary .text-muted.focus{color: #dee2e6 !important;}.btn-link.btn-success, .btn-link.text-success{text-transform: none; color: #6c757d !important;}.btn-link.btn-success:hover, .btn-link.btn-success:focus, .btn-link.btn-success.focus, .btn-link.text-success:hover, .btn-link.text-success:focus, .btn-link.text-success.focus{color: #008818 !important;}.btn-link.btn-success, .btn-link.btn-success:hover, .btn-link.btn-success:focus, .btn-link.btn-success:active, .btn-link.btn-success.active, .btn-link.text-success, .btn-link.text-success:hover, .btn-link.text-success:focus, .btn-link.text-success:active, .btn-link.text-success.active{border-color: transparent !important; background-color: transparent !important;}.btn-link.btn-success:hover:active:focus, .btn-link.text-success:hover:active:focus{box-shadow: none; outline: none;}.btn-link.btn-success.text-muted, .btn-link.btn-success .text-muted, .btn-link.text-success.text-muted, .btn-link.text-success .text-muted{color: rgba(73, 80, 87, 0.76);}.btn-link.btn-success.text-muted:hover, .btn-link.btn-success.text-muted:focus, .btn-link.btn-success.text-muted.focus, .btn-link.btn-success .text-muted:hover, .btn-link.btn-success .text-muted:focus, .btn-link.btn-success .text-muted.focus, .btn-link.text-success.text-muted:hover, .btn-link.text-success.text-muted:focus, .btn-link.text-success.text-muted.focus, .btn-link.text-success .text-muted:hover, .btn-link.text-success .text-muted:focus, .btn-link.text-success .text-muted.focus{color: #008818 !important;}.btn-link.btn-info, .btn-link.text-info{text-transform: none; color: #6c757d !important;}.btn-link.btn-info:hover, .btn-link.btn-info:focus, .btn-link.btn-info.focus, .btn-link.text-info:hover, .btn-link.text-info:focus, .btn-link.text-info.focus{color: #0180a5 !important;}.btn-link.btn-info, .btn-link.btn-info:hover, .btn-link.btn-info:focus, .btn-link.btn-info:active, .btn-link.btn-info.active, .btn-link.text-info, .btn-link.text-info:hover, .btn-link.text-info:focus, .btn-link.text-info:active, .btn-link.text-info.active{border-color: transparent !important; background-color: transparent !important;}.btn-link.btn-info:hover:active:focus, .btn-link.text-info:hover:active:focus{box-shadow: none; outline: none;}.btn-link.btn-info.text-muted, .btn-link.btn-info .text-muted, .btn-link.text-info.text-muted, .btn-link.text-info .text-muted{color: rgba(73, 80, 87, 0.76);}.btn-link.btn-info.text-muted:hover, .btn-link.btn-info.text-muted:focus, .btn-link.btn-info.text-muted.focus, .btn-link.btn-info .text-muted:hover, .btn-link.btn-info .text-muted:focus, .btn-link.btn-info .text-muted.focus, .btn-link.text-info.text-muted:hover, .btn-link.text-info.text-muted:focus, .btn-link.text-info.text-muted.focus, .btn-link.text-info .text-muted:hover, .btn-link.text-info .text-muted:focus, .btn-link.text-info .text-muted.focus{color: #0180a5 !important;}.btn-link.btn-warning, .btn-link.text-warning{text-transform: none; color: #6c757d !important;}.btn-link.btn-warning:hover, .btn-link.btn-warning:focus, .btn-link.btn-warning.focus, .btn-link.text-warning:hover, .btn-link.text-warning:focus, .btn-link.text-warning.focus{color: #9a6b01 !important;}.btn-link.btn-warning, .btn-link.btn-warning:hover, .btn-link.btn-warning:focus, .btn-link.btn-warning:active, .btn-link.btn-warning.active, .btn-link.text-warning, .btn-link.text-warning:hover, .btn-link.text-warning:focus, .btn-link.text-warning:active, .btn-link.text-warning.active{border-color: transparent !important; background-color: transparent !important;}.btn-link.btn-warning:hover:active:focus, .btn-link.text-warning:hover:active:focus{box-shadow: none; outline: none;}.btn-link.btn-warning.text-muted, .btn-link.btn-warning .text-muted, .btn-link.text-warning.text-muted, .btn-link.text-warning .text-muted{color: rgba(73, 80, 87, 0.76);}.btn-link.btn-warning.text-muted:hover, .btn-link.btn-warning.text-muted:focus, .btn-link.btn-warning.text-muted.focus, .btn-link.btn-warning .text-muted:hover, .btn-link.btn-warning .text-muted:focus, .btn-link.btn-warning .text-muted.focus, .btn-link.text-warning.text-muted:hover, .btn-link.text-warning.text-muted:focus, .btn-link.text-warning.text-muted.focus, .btn-link.text-warning .text-muted:hover, .btn-link.text-warning .text-muted:focus, .btn-link.text-warning .text-muted.focus{color: #9a6b01 !important;}.btn-link.btn-danger, .btn-link.text-danger{text-transform: none; color: #6c757d !important;}.btn-link.btn-danger:hover, .btn-link.btn-danger:focus, .btn-link.btn-danger.focus, .btn-link.text-danger:hover, .btn-link.text-danger:focus, .btn-link.text-danger.focus{color: #d23f3a !important;}.btn-link.btn-danger, .btn-link.btn-danger:hover, .btn-link.btn-danger:focus, .btn-link.btn-danger:active, .btn-link.btn-danger.active, .btn-link.text-danger, .btn-link.text-danger:hover, .btn-link.text-danger:focus, .btn-link.text-danger:active, .btn-link.text-danger.active{border-color: transparent !important; background-color: transparent !important;}.btn-link.btn-danger:hover:active:focus, .btn-link.text-danger:hover:active:focus{box-shadow: none; outline: none;}.btn-link.btn-danger.text-muted, .btn-link.btn-danger .text-muted, .btn-link.text-danger.text-muted, .btn-link.text-danger .text-muted{color: rgba(73, 80, 87, 0.76);}.btn-link.btn-danger.text-muted:hover, .btn-link.btn-danger.text-muted:focus, .btn-link.btn-danger.text-muted.focus, .btn-link.btn-danger .text-muted:hover, .btn-link.btn-danger .text-muted:focus, .btn-link.btn-danger .text-muted.focus, .btn-link.text-danger.text-muted:hover, .btn-link.text-danger.text-muted:focus, .btn-link.text-danger.text-muted.focus, .btn-link.text-danger .text-muted:hover, .btn-link.text-danger .text-muted:focus, .btn-link.text-danger .text-muted.focus{color: #d23f3a !important;}.btn-link.btn-light, .btn-link.text-light{text-transform: none; color: #6c757d !important;}.btn-link.btn-light:hover, .btn-link.btn-light:focus, .btn-link.btn-light.focus, .btn-link.text-light:hover, .btn-link.text-light:focus, .btn-link.text-light.focus{color: #f8f9fa !important;}.btn-link.btn-light, .btn-link.btn-light:hover, .btn-link.btn-light:focus, .btn-link.btn-light:active, .btn-link.btn-light.active, .btn-link.text-light, .btn-link.text-light:hover, .btn-link.text-light:focus, .btn-link.text-light:active, .btn-link.text-light.active{border-color: transparent !important; background-color: transparent !important;}.btn-link.btn-light:hover:active:focus, .btn-link.text-light:hover:active:focus{box-shadow: none; outline: none;}.btn-link.btn-light.text-muted, .btn-link.btn-light .text-muted, .btn-link.text-light.text-muted, .btn-link.text-light .text-muted{color: rgba(73, 80, 87, 0.76);}.btn-link.btn-light.text-muted:hover, .btn-link.btn-light.text-muted:focus, .btn-link.btn-light.text-muted.focus, .btn-link.btn-light .text-muted:hover, .btn-link.btn-light .text-muted:focus, .btn-link.btn-light .text-muted.focus, .btn-link.text-light.text-muted:hover, .btn-link.text-light.text-muted:focus, .btn-link.text-light.text-muted.focus, .btn-link.text-light .text-muted:hover, .btn-link.text-light .text-muted:focus, .btn-link.text-light .text-muted.focus{color: #f8f9fa !important;}.btn-link.btn-dark, .btn-link.text-dark{text-transform: none; color: #6c757d !important;}.btn-link.btn-dark:hover, .btn-link.btn-dark:focus, .btn-link.btn-dark.focus, .btn-link.text-dark:hover, .btn-link.text-dark:focus, .btn-link.text-dark.focus{color: #212529 !important;}.btn-link.btn-dark, .btn-link.btn-dark:hover, .btn-link.btn-dark:focus, .btn-link.btn-dark:active, .btn-link.btn-dark.active, .btn-link.text-dark, .btn-link.text-dark:hover, .btn-link.text-dark:focus, .btn-link.text-dark:active, .btn-link.text-dark.active{border-color: transparent !important; background-color: transparent !important;}.btn-link.btn-dark:hover:active:focus, .btn-link.text-dark:hover:active:focus{box-shadow: none; outline: none;}.btn-link.btn-dark.text-muted, .btn-link.btn-dark .text-muted, .btn-link.text-dark.text-muted, .btn-link.text-dark .text-muted{color: rgba(73, 80, 87, 0.76);}.btn-link.btn-dark.text-muted:hover, .btn-link.btn-dark.text-muted:focus, .btn-link.btn-dark.text-muted.focus, .btn-link.btn-dark .text-muted:hover, .btn-link.btn-dark .text-muted:focus, .btn-link.btn-dark .text-muted.focus, .btn-link.text-dark.text-muted:hover, .btn-link.text-dark.text-muted:focus, .btn-link.text-dark.text-muted.focus, .btn-link.text-dark .text-muted:hover, .btn-link.text-dark .text-muted:focus, .btn-link.text-dark .text-muted.focus{color: #212529 !important;}.bg-primary{--background-color: RGBA(113, 99, 158, var(--bg-opacity, 1)); background-color: var(--background-color) !important;}.bg-secondary{--background-color: RGBA(222, 226, 230, var(--bg-opacity, 1)); background-color: var(--background-color) !important;}.bg-success{--background-color: RGBA(40, 167, 69, var(--bg-opacity, 1)); background-color: var(--background-color) !important;}.bg-info{--background-color: RGBA(23, 162, 184, var(--bg-opacity, 1)); background-color: var(--background-color) !important;}.bg-warning{--background-color: RGBA(255, 172, 0, var(--bg-opacity, 1)); background-color: var(--background-color) !important;}.bg-danger{--background-color: RGBA(220, 53, 69, var(--bg-opacity, 1)); background-color: var(--background-color) !important;}.bg-light{--background-color: RGBA(248, 249, 250, var(--bg-opacity, 1)); background-color: var(--background-color) !important;}.bg-dark{--background-color: RGBA(33, 37, 41, var(--bg-opacity, 1)); background-color: var(--background-color) !important;}.text-white-muted, .text-white-disabled, .text-white-75, .text-white-50, .text-white-25, .text-white{--color: RGBA(255, 255, 255, var(--text-opacity, 1)); color: var(--color) !important;}.text-black-muted, .text-black-disabled, .text-black-75, .text-black-50, .text-black-25, .text-black{--color: RGBA(0, 0, 0, var(--text-opacity, 1)); color: var(--color) !important;}.bg-white-muted, .bg-white-disabled, .bg-white-75, .bg-white-50, .bg-white-25, .bg-white{--background-color: RGBA(255, 255, 255, var(--bg-opacity, 1)); background-color: var(--background-color) !important;}.bg-black-muted, .bg-black-disabled, .bg-black-75, .bg-black-50, .bg-black-25, .bg-transparent, .bg-black{--background-color: RGBA(0, 0, 0, var(--bg-opacity, 1)); background-color: var(--background-color) !important;}.bg-transparent{--bg-opacity: 0;}.bg-white-25{--bg-opacity: 0.25;}.bg-black-25{--bg-opacity: 0.25;}.bg-white-50{--bg-opacity: 0.5;}.bg-black-50{--bg-opacity: 0.5;}.bg-white-75{--bg-opacity: 0.75;}.bg-black-75{--bg-opacity: 0.75;}.bg-white-disabled{--bg-opacity: 0.5;}.bg-black-disabled{--bg-opacity: 0.5;}.bg-white-muted{--bg-opacity: 0.76;}.bg-black-muted{--bg-opacity: 0.76;}.text-white-25{--text-opacity: 0.25;}.text-black-25{--text-opacity: 0.25;}.text-white-50{--text-opacity: 0.5;}.text-black-50{--text-opacity: 0.5;}.text-white-75{--text-opacity: 0.75;}.text-black-75{--text-opacity: 0.75;}.text-white-disabled{--text-opacity: 0.5;}.text-black-disabled{--text-opacity: 0.5;}.text-white-muted{--text-opacity: 0.76;}.text-black-muted{--text-opacity: 0.76;}.text-action{--color: RGBA(113, 99, 158, var(--text-opacity, 1)); color: var(--color) !important;}a.text-action:hover, a.text-action:focus, a.text-action:active, a.text-action.active, button.text-action:hover, button.text-action:focus, button.text-action:active, button.text-action.active{--color: RGBA(79, 69, 111, var(--text-opacity, 1)); color: var(--color) !important;}.text-favourite{--color: RGBA(243, 204, 0, var(--text-opacity, 1)); color: var(--color) !important;}a.text-favourite:hover, a.text-favourite:focus, a.text-favourite:active, a.text-favourite.active, button.text-favourite:hover, button.text-favourite:focus, button.text-favourite:active, button.text-favourite.active{--color: RGBA(170, 143, 0, var(--text-opacity, 1)); color: var(--color) !important;}.text-100{--color: RGBA(248, 249, 250, var(--text-opacity, 1)); color: var(--color) !important;}a.text-100:hover, a.text-100:focus, a.text-100:active, a.text-100.active, button.text-100:hover, button.text-100:focus, button.text-100:active, button.text-100.active{--color: RGBA(174, 174, 175, var(--text-opacity, 1)); color: var(--color) !important;}.text-200{--color: RGBA(233, 236, 239, var(--text-opacity, 1)); color: var(--color) !important;}a.text-200:hover, a.text-200:focus, a.text-200:active, a.text-200.active, button.text-200:hover, button.text-200:focus, button.text-200:active, button.text-200.active{--color: RGBA(163, 165, 167, var(--text-opacity, 1)); color: var(--color) !important;}.text-300{--color: RGBA(222, 226, 230, var(--text-opacity, 1)); color: var(--color) !important;}a.text-300:hover, a.text-300:focus, a.text-300:active, a.text-300.active, button.text-300:hover, button.text-300:focus, button.text-300:active, button.text-300.active{--color: RGBA(155, 158, 161, var(--text-opacity, 1)); color: var(--color) !important;}.text-400{--color: RGBA(206, 212, 218, var(--text-opacity, 1)); color: var(--color) !important;}a.text-400:hover, a.text-400:focus, a.text-400:active, a.text-400.active, button.text-400:hover, button.text-400:focus, button.text-400:active, button.text-400.active{--color: RGBA(144, 148, 153, var(--text-opacity, 1)); color: var(--color) !important;}.text-500{--color: RGBA(173, 181, 189, var(--text-opacity, 1)); color: var(--color) !important;}a.text-500:hover, a.text-500:focus, a.text-500:active, a.text-500.active, button.text-500:hover, button.text-500:focus, button.text-500:active, button.text-500.active{--color: RGBA(121, 127, 132, var(--text-opacity, 1)); color: var(--color) !important;}.text-600{--color: RGBA(108, 117, 125, var(--text-opacity, 1)); color: var(--color) !important;}a.text-600:hover, a.text-600:focus, a.text-600:active, a.text-600.active, button.text-600:hover, button.text-600:focus, button.text-600:active, button.text-600.active{--color: RGBA(76, 82, 88, var(--text-opacity, 1)); color: var(--color) !important;}.text-700{--color: RGBA(73, 80, 87, var(--text-opacity, 1)); color: var(--color) !important;}a.text-700:hover, a.text-700:focus, a.text-700:active, a.text-700.active, button.text-700:hover, button.text-700:focus, button.text-700:active, button.text-700.active{--color: RGBA(51, 56, 61, var(--text-opacity, 1)); color: var(--color) !important;}.text-800{--color: RGBA(52, 58, 64, var(--text-opacity, 1)); color: var(--color) !important;}a.text-800:hover, a.text-800:focus, a.text-800:active, a.text-800.active, button.text-800:hover, button.text-800:focus, button.text-800:active, button.text-800.active{--color: RGBA(36, 41, 45, var(--text-opacity, 1)); color: var(--color) !important;}.text-900{--color: RGBA(33, 37, 41, var(--text-opacity, 1)); color: var(--color) !important;}a.text-900:hover, a.text-900:focus, a.text-900:active, a.text-900.active, button.text-900:hover, button.text-900:focus, button.text-900:active, button.text-900.active{--color: RGBA(23, 26, 29, var(--text-opacity, 1)); color: var(--color) !important;}.bg-action, .text-bg-action{--background-color: RGBA(113, 99, 158, var(--bg-opacity, 1)); background-color: var(--background-color) !important;}.text-bg-action{--color: RGBA(255, 255, 255, var(--text-opacity, 1)); color: var(--color) !important;}.bg-view, .text-bg-view{--background-color: RGBA(255, 255, 255, var(--bg-opacity, 1)); background-color: var(--background-color) !important;}.text-bg-view{--color: RGBA(0, 0, 0, var(--text-opacity, 1)); color: var(--color) !important;}.bg-favourite, .text-bg-favourite{--background-color: RGBA(243, 204, 0, var(--bg-opacity, 1)); background-color: var(--background-color) !important;}.text-bg-favourite{--color: RGBA(0, 0, 0, var(--text-opacity, 1)); color: var(--color) !important;}.bg-100, .text-bg-100{--background-color: RGBA(248, 249, 250, var(--bg-opacity, 1)); background-color: var(--background-color) !important;}.text-bg-100{--color: RGBA(0, 0, 0, var(--text-opacity, 1)); color: var(--color) !important;}.bg-200, .text-bg-200{--background-color: RGBA(233, 236, 239, var(--bg-opacity, 1)); background-color: var(--background-color) !important;}.text-bg-200{--color: RGBA(0, 0, 0, var(--text-opacity, 1)); color: var(--color) !important;}.bg-300, .text-bg-300{--background-color: RGBA(222, 226, 230, var(--bg-opacity, 1)); background-color: var(--background-color) !important;}.text-bg-300{--color: RGBA(0, 0, 0, var(--text-opacity, 1)); color: var(--color) !important;}.bg-400, .text-bg-400{--background-color: RGBA(206, 212, 218, var(--bg-opacity, 1)); background-color: var(--background-color) !important;}.text-bg-400{--color: RGBA(0, 0, 0, var(--text-opacity, 1)); color: var(--color) !important;}.bg-500, .text-bg-500{--background-color: RGBA(173, 181, 189, var(--bg-opacity, 1)); background-color: var(--background-color) !important;}.text-bg-500{--color: RGBA(0, 0, 0, var(--text-opacity, 1)); color: var(--color) !important;}.bg-600, .text-bg-600{--background-color: RGBA(108, 117, 125, var(--bg-opacity, 1)); background-color: var(--background-color) !important;}.text-bg-600{--color: RGBA(255, 255, 255, var(--text-opacity, 1)); color: var(--color) !important;}.bg-700, .text-bg-700{--background-color: RGBA(73, 80, 87, var(--bg-opacity, 1)); background-color: var(--background-color) !important;}.text-bg-700{--color: RGBA(255, 255, 255, var(--text-opacity, 1)); color: var(--color) !important;}.bg-800, .text-bg-800{--background-color: RGBA(52, 58, 64, var(--bg-opacity, 1)); background-color: var(--background-color) !important;}.text-bg-800{--color: RGBA(255, 255, 255, var(--text-opacity, 1)); color: var(--color) !important;}.bg-900, .text-bg-900{--background-color: RGBA(33, 37, 41, var(--bg-opacity, 1)); background-color: var(--background-color) !important;}.text-bg-900{--color: RGBA(255, 255, 255, var(--text-opacity, 1)); color: var(--color) !important;}.btn-link.btn-action, .btn-link.text-action{text-transform: none; color: #6c757d !important;}.btn-link.btn-action:hover, .btn-link.btn-action:focus, .btn-link.btn-action.focus, .btn-link.text-action:hover, .btn-link.text-action:focus, .btn-link.text-action.focus{color: #71639e !important;}.btn-link.btn-action, .btn-link.btn-action:hover, .btn-link.btn-action:focus, .btn-link.btn-action:active, .btn-link.btn-action.active, .btn-link.text-action, .btn-link.text-action:hover, .btn-link.text-action:focus, .btn-link.text-action:active, .btn-link.text-action.active{border-color: transparent !important; background-color: transparent !important;}.btn-link.btn-action:hover:active:focus, .btn-link.text-action:hover:active:focus{box-shadow: none; outline: none;}.btn-link.btn-action.text-muted, .btn-link.btn-action .text-muted, .btn-link.text-action.text-muted, .btn-link.text-action .text-muted{color: rgba(73, 80, 87, 0.76);}.btn-link.btn-action.text-muted:hover, .btn-link.btn-action.text-muted:focus, .btn-link.btn-action.text-muted.focus, .btn-link.btn-action .text-muted:hover, .btn-link.btn-action .text-muted:focus, .btn-link.btn-action .text-muted.focus, .btn-link.text-action.text-muted:hover, .btn-link.text-action.text-muted:focus, .btn-link.text-action.text-muted.focus, .btn-link.text-action .text-muted:hover, .btn-link.text-action .text-muted:focus, .btn-link.text-action .text-muted.focus{color: #71639e !important;}.btn-link.btn-favourite, .btn-link.text-favourite{text-transform: none; color: #6c757d !important;}.btn-link.btn-favourite:hover, .btn-link.btn-favourite:focus, .btn-link.btn-favourite.focus, .btn-link.text-favourite:hover, .btn-link.text-favourite:focus, .btn-link.text-favourite.focus{color: #f3cc00 !important;}.btn-link.btn-favourite, .btn-link.btn-favourite:hover, .btn-link.btn-favourite:focus, .btn-link.btn-favourite:active, .btn-link.btn-favourite.active, .btn-link.text-favourite, .btn-link.text-favourite:hover, .btn-link.text-favourite:focus, .btn-link.text-favourite:active, .btn-link.text-favourite.active{border-color: transparent !important; background-color: transparent !important;}.btn-link.btn-favourite:hover:active:focus, .btn-link.text-favourite:hover:active:focus{box-shadow: none; outline: none;}.btn-link.btn-favourite.text-muted, .btn-link.btn-favourite .text-muted, .btn-link.text-favourite.text-muted, .btn-link.text-favourite .text-muted{color: rgba(73, 80, 87, 0.76);}.btn-link.btn-favourite.text-muted:hover, .btn-link.btn-favourite.text-muted:focus, .btn-link.btn-favourite.text-muted.focus, .btn-link.btn-favourite .text-muted:hover, .btn-link.btn-favourite .text-muted:focus, .btn-link.btn-favourite .text-muted.focus, .btn-link.text-favourite.text-muted:hover, .btn-link.text-favourite.text-muted:focus, .btn-link.text-favourite.text-muted.focus, .btn-link.text-favourite .text-muted:hover, .btn-link.text-favourite .text-muted:focus, .btn-link.text-favourite .text-muted.focus{color: #f3cc00 !important;}.bg-inherit{background-color: inherit;}.alert{padding-top: var(--alert-padding-y, 16px); padding-bottom: var(--alert-padding-y, 16px); margin-bottom: var(--alert-margin-bottom, 1rem);}.form-switch .form-check-input:checked{background-color: #28a745; border-color: #28a745;}.form-switch .form-check-input:focus{border-color: #28a745;}.form-switch:hover .form-check-input:not(:disabled){border-color: #28a745;}.form-switch.o_switch_toggle .form-check-input, .form-switch.o_switch_toggle .form-check-input:checked{border-color: #71639e; background-color: white;}.form-switch.o_switch_toggle .form-check-input, .form-switch.o_switch_toggle .form-check-input:focus, .form-switch.o_switch_toggle .form-check-input:checked, .form-switch.o_switch_toggle .form-check-input:checked, .form-switch.o_switch_toggle .form-check-input:checked:focus, .form-switch.o_switch_toggle .form-check-input:checked:checked{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%2371639e'/%3e%3c/svg%3e");}.form-switch.o_switch_toggle:hover .form-check-input:not(:disabled){border-color: #71639e;}.form-switch:disabled, .form-switch.disabled, .form-switch[disabled]{opacity: 0.5;}.table > :not(caption) > * > *{color: unset;}

/* /web/static/src/scss/ui.scss */
 :root .o_hidden{display: none !important;}.o_disabled{pointer-events: none; opacity: 0.5;}.o_btn_loading{opacity: 0.5;}.o_btn_loading .fa:not(.fa-spin){display: none;}.o_text_overflow{display: inline-block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top;}.dropdown-menu{max-height: 70vh; overflow: auto; background-clip: border-box;}.dropdown-toggle{white-space: nowrap;}.dropdown-toggle.o-no-caret::before, .dropdown-toggle.o-no-caret::after{content: normal;}.o_catch_attention{position: relative; z-index: 1; animation: catchAttention 200ms ease 0s infinite normal;}.o_treeEntry{padding-left: var(--treeEntry-padding-h, 24px); position: relative;}.o_treeEntry:before, .o_treeEntry:after{position: absolute; left: var(--treeEntry--beforeAfter-left, calc(var(--treeEntry-padding-h, 24px) * .5)); background: var(--treeEntry--beforeAfter-color, #dee2e6); content: '';}.o_treeEntry:before{top: var(--treeEntry--before-top, 0); width: 1px; height: 100%;}.o_treeEntry:after{display: var(--treeEntry--after-display, initial); top: calc(.5em + var(--treeEntry-padding-v, 8px)); width: calc(var(--treeEntry-padding-h, 24px) * .5); height: 1px;}.o_treeEntry:last-of-type:before{height: calc(.5em + var(--treeEntry-padding-v, 8px));}@keyframes catchAttention{0%, 20%, 40%, 60%, 80%, 100%{transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);}0%{transform: translateY(-30%);}20%{transform: translateY(-25%);}40%{transform: translateY(-20%);}60%{transform: translateY(-15%);}80%{transform: translateY(-10%);}100%{transform: translateY(-5%);}}span.o_force_ltr{display: inline;}.o_force_ltr{unicode-bidi: embed; direction: ltr;}.o_object_fit_cover{object-fit: cover;}.o_object_fit_contain{object-fit: contain;}.o_image_24_cover{width: 24px; height: 24px; object-fit: cover;}.o_image_40_cover{width: 40px; height: 40px; object-fit: cover;}.o_image_64_cover{width: 64px; height: 64px; object-fit: cover;}.o_image_64_contain{width: 64px; height: 64px; object-fit: contain;}.o_image_64_max{max-width: 64px; max-height: 64px;}.modal .o_cp_action_menus .o-dropdown:has(.fa-cog){display: none;}.o_ui_app_icon{width: var(--oi-font-size, 1em);}:not(:hover) > .o_ui_app_icon{--oi-color: currentColor;}

/* /web/static/src/libs/fontawesome/css/font-awesome.css */
 @font-face{font-family: 'FontAwesome'; src: url('/web/static/src/libs/fontawesome/css/../fonts/fontawesome-webfont.woff2?v=4.7.0') format('woff2'), url('/web/static/src/libs/fontawesome/css/../fonts/fontawesome-webfont.woff?v=4.7.0') format('woff'); font-weight: normal; font-style: normal; font-display: block;}.fa{display: inline-block; font: normal normal normal 14px/1 FontAwesome; font-size: inherit; text-rendering: auto; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;}.fa-lg{font-size: 1.315em; vertical-align: -6%;}.fa-2x{font-size: 2em;}.fa-3x{font-size: 3em;}.fa-4x{font-size: 4em;}.fa-5x{font-size: 5em;}.fa-fw{width: 1.28571429em; text-align: center;}.fa-ul{padding-left: 0; margin-left: 2.14285714em; list-style-type: none;}.fa-ul > li{position: relative;}.fa-li{position: absolute; left: -2.14285714em; width: 2.14285714em; top: 0.14285714em; text-align: center;}.fa-li.fa-lg{left: -1.85714286em;}.fa-border{padding: .2em .25em .15em; border: solid 0.08em #eeeeee; border-radius: .1em;}.fa-pull-left{float: left;}.fa-pull-right{float: right;}.fa.fa-pull-left{margin-right: .3em;}.fa.fa-pull-right{margin-left: .3em;}.fa-spin{animation: fa-spin 2s infinite linear;}.fa-pulse{animation: fa-spin 1s infinite steps(8);}@keyframes fa-spin{0%{transform: rotate(0deg);}100%{transform: rotate(359deg);}}.fa-rotate-90{transform: rotate(90deg);}.fa-rotate-180{transform: rotate(180deg);}.fa-rotate-270{transform: rotate(270deg);}.fa-flip-horizontal{transform: scale(-1, 1);}.fa-flip-vertical{transform: scale(1, -1);}:root .fa-rotate-90, :root .fa-rotate-180, :root .fa-rotate-270, :root .fa-flip-horizontal, :root .fa-flip-vertical{filter: none;}.fa-stack{position: relative; display: inline-block; width: 2em; height: 2em; line-height: 2em; vertical-align: middle;}.fa-stack-1x, .fa-stack-2x{position: absolute; left: 0; width: 100%; text-align: center;}.fa-stack-1x{line-height: inherit;}.fa-stack-2x{font-size: 2em;}.fa-inverse{color: #ffffff;}.fa-glass:before{content: "\f000";}.fa-music:before{content: "\f001";}.fa-search:before{content: "\f002";}.fa-envelope-o:before{content: "\f003";}.fa-heart:before{content: "\f004";}.fa-star:before{content: "\f005";}.fa-star-o:before{content: "\f006";}.fa-user:before{content: "\f007";}.fa-film:before{content: "\f008";}.fa-th-large:before{content: "\f009";}.fa-th:before{content: "\f00a";}.fa-th-list:before{content: "\f00b";}.fa-check:before{content: "\f00c";}.fa-remove:before, .fa-close:before, .fa-times:before{content: "\f00d";}.fa-search-plus:before{content: "\f00e";}.fa-search-minus:before{content: "\f010";}.fa-power-off:before{content: "\f011";}.fa-signal:before{content: "\f012";}.fa-gear:before, .fa-cog:before{content: "\f013";}.fa-trash-o:before{content: "\f014";}.fa-home:before{content: "\f015";}.fa-file-o:before{content: "\f016";}.fa-clock-o:before{content: "\f017";}.fa-road:before{content: "\f018";}.fa-download:before{content: "\f019";}.fa-arrow-circle-o-down:before{content: "\f01a";}.fa-arrow-circle-o-up:before{content: "\f01b";}.fa-inbox:before{content: "\f01c";}.fa-play-circle-o:before{content: "\f01d";}.fa-rotate-right:before, .fa-repeat:before{content: "\f01e";}.fa-refresh:before{content: "\f021";}.fa-list-alt:before{content: "\f022";}.fa-lock:before{content: "\f023";}.fa-flag:before{content: "\f024";}.fa-headphones:before{content: "\f025";}.fa-volume-off:before{content: "\f026";}.fa-volume-down:before{content: "\f027";}.fa-volume-up:before{content: "\f028";}.fa-qrcode:before{content: "\f029";}.fa-barcode:before{content: "\f02a";}.fa-tag:before{content: "\f02b";}.fa-tags:before{content: "\f02c";}.fa-book:before{content: "\f02d";}.fa-bookmark:before{content: "\f02e";}.fa-print:before{content: "\f02f";}.fa-camera:before{content: "\f030";}.fa-font:before{content: "\f031";}.fa-bold:before{content: "\f032";}.fa-italic:before{content: "\f033";}.fa-text-height:before{content: "\f034";}.fa-text-width:before{content: "\f035";}.fa-align-left:before{content: "\f036";}.fa-align-center:before{content: "\f037";}.fa-align-right:before{content: "\f038";}.fa-align-justify:before{content: "\f039";}.fa-list:before{content: "\f03a";}.fa-dedent:before, .fa-outdent:before{content: "\f03b";}.fa-indent:before{content: "\f03c";}.fa-video-camera:before{content: "\f03d";}.fa-photo:before, .fa-image:before, .fa-picture-o:before{content: "\f03e";}.fa-pencil:before{content: "\f040";}.fa-map-marker:before{content: "\f041";}.fa-adjust:before{content: "\f042";}.fa-tint:before{content: "\f043";}.fa-edit:before, .fa-pencil-square-o:before{content: "\f044";}.fa-share-square-o:before{content: "\f045";}.fa-check-square-o:before{content: "\f046";}.fa-arrows:before{content: "\f047";}.fa-step-backward:before{content: "\f048";}.fa-fast-backward:before{content: "\f049";}.fa-backward:before{content: "\f04a";}.fa-play:before{content: "\f04b";}.fa-pause:before{content: "\f04c";}.fa-stop:before{content: "\f04d";}.fa-forward:before{content: "\f04e";}.fa-fast-forward:before{content: "\f050";}.fa-step-forward:before{content: "\f051";}.fa-eject:before{content: "\f052";}.fa-chevron-left:before{content: "\f053";}.fa-chevron-right:before{content: "\f054";}.fa-plus-circle:before{content: "\f055";}.fa-minus-circle:before{content: "\f056";}.fa-times-circle:before{content: "\f057";}.fa-check-circle:before{content: "\f058";}.fa-question-circle:before{content: "\f059";}.fa-info-circle:before{content: "\f05a";}.fa-crosshairs:before{content: "\f05b";}.fa-times-circle-o:before{content: "\f05c";}.fa-check-circle-o:before{content: "\f05d";}.fa-ban:before{content: "\f05e";}.fa-arrow-left:before{content: "\f060";}.fa-arrow-right:before{content: "\f061";}.fa-arrow-up:before{content: "\f062";}.fa-arrow-down:before{content: "\f063";}.fa-mail-forward:before, .fa-share:before{content: "\f064";}.fa-expand:before{content: "\f065";}.fa-compress:before{content: "\f066";}.fa-plus:before{content: "\f067";}.fa-minus:before{content: "\f068";}.fa-asterisk:before{content: "\f069";}.fa-exclamation-circle:before{content: "\f06a";}.fa-gift:before{content: "\f06b";}.fa-leaf:before{content: "\f06c";}.fa-fire:before{content: "\f06d";}.fa-eye:before{content: "\f06e";}.fa-eye-slash:before{content: "\f070";}.fa-warning:before, .fa-exclamation-triangle:before{content: "\f071";}.fa-plane:before{content: "\f072";}.fa-calendar:before{content: "\f073";}.fa-random:before{content: "\f074";}.fa-comment:before{content: "\f075";}.fa-magnet:before{content: "\f076";}.fa-chevron-up:before{content: "\f077";}.fa-chevron-down:before{content: "\f078";}.fa-retweet:before{content: "\f079";}.fa-shopping-cart:before{content: "\f07a";}.fa-folder:before{content: "\f07b";}.fa-folder-open:before{content: "\f07c";}.fa-arrows-v:before{content: "\f07d";}.fa-arrows-h:before{content: "\f07e";}.fa-bar-chart-o:before, .fa-bar-chart:before{content: "\f080";}.fa-twitter-square:before{content: "\f081";}.fa-facebook-square:before{content: "\f082";}.fa-camera-retro:before{content: "\f083";}.fa-key:before{content: "\f084";}.fa-gears:before, .fa-cogs:before{content: "\f085";}.fa-comments:before{content: "\f086";}.fa-thumbs-o-up:before{content: "\f087";}.fa-thumbs-o-down:before{content: "\f088";}.fa-star-half:before{content: "\f089";}.fa-heart-o:before{content: "\f08a";}.fa-sign-out:before{content: "\f08b";}.fa-linkedin-square:before{content: "\f08c";}.fa-thumb-tack:before{content: "\f08d";}.fa-external-link:before{content: "\f08e";}.fa-sign-in:before{content: "\f090";}.fa-trophy:before{content: "\f091";}.fa-github-square:before{content: "\f092";}.fa-upload:before{content: "\f093";}.fa-lemon-o:before{content: "\f094";}.fa-phone:before{content: "\f095";}.fa-square-o:before{content: "\f096";}.fa-bookmark-o:before{content: "\f097";}.fa-phone-square:before{content: "\f098";}.fa-twitter:before{content: "\f099";}.fa-facebook-f:before, .fa-facebook:before{content: "\f09a";}.fa-github:before{content: "\f09b";}.fa-unlock:before{content: "\f09c";}.fa-credit-card:before{content: "\f09d";}.fa-feed:before, .fa-rss:before{content: "\f09e";}.fa-hdd-o:before{content: "\f0a0";}.fa-bullhorn:before{content: "\f0a1";}.fa-bell:before{content: "\f0f3";}.fa-certificate:before{content: "\f0a3";}.fa-hand-o-right:before{content: "\f0a4";}.fa-hand-o-left:before{content: "\f0a5";}.fa-hand-o-up:before{content: "\f0a6";}.fa-hand-o-down:before{content: "\f0a7";}.fa-arrow-circle-left:before{content: "\f0a8";}.fa-arrow-circle-right:before{content: "\f0a9";}.fa-arrow-circle-up:before{content: "\f0aa";}.fa-arrow-circle-down:before{content: "\f0ab";}.fa-globe:before{content: "\f0ac";}.fa-wrench:before{content: "\f0ad";}.fa-tasks:before{content: "\f0ae";}.fa-filter:before{content: "\f0b0";}.fa-briefcase:before{content: "\f0b1";}.fa-arrows-alt:before{content: "\f0b2";}.fa-group:before, .fa-users:before{content: "\f0c0";}.fa-chain:before, .fa-link:before{content: "\f0c1";}.fa-cloud:before{content: "\f0c2";}.fa-flask:before{content: "\f0c3";}.fa-cut:before, .fa-scissors:before{content: "\f0c4";}.fa-copy:before, .fa-files-o:before{content: "\f0c5";}.fa-paperclip:before{content: "\f0c6";}.fa-save:before, .fa-floppy-o:before{content: "\f0c7";}.fa-square:before{content: "\f0c8";}.fa-navicon:before, .fa-reorder:before, .fa-bars:before{content: "\f0c9";}.fa-list-ul:before{content: "\f0ca";}.fa-list-ol:before{content: "\f0cb";}.fa-strikethrough:before{content: "\f0cc";}.fa-underline:before{content: "\f0cd";}.fa-table:before{content: "\f0ce";}.fa-magic:before{content: "\f0d0";}.fa-truck:before{content: "\f0d1";}.fa-pinterest:before{content: "\f0d2";}.fa-pinterest-square:before{content: "\f0d3";}.fa-google-plus-square:before{content: "\f0d4";}.fa-google-plus:before{content: "\f0d5";}.fa-money:before{content: "\f0d6";}.fa-caret-down:before{content: "\f0d7";}.fa-caret-up:before{content: "\f0d8";}.fa-caret-left:before{content: "\f0d9";}.fa-caret-right:before{content: "\f0da";}.fa-columns:before{content: "\f0db";}.fa-unsorted:before, .fa-sort:before{content: "\f0dc";}.fa-sort-down:before, .fa-sort-desc:before{content: "\f0dd";}.fa-sort-up:before, .fa-sort-asc:before{content: "\f0de";}.fa-envelope:before{content: "\f0e0";}.fa-linkedin:before{content: "\f0e1";}.fa-rotate-left:before, .fa-undo:before{content: "\f0e2";}.fa-legal:before, .fa-gavel:before{content: "\f0e3";}.fa-dashboard:before, .fa-tachometer:before{content: "\f0e4";}.fa-comment-o:before{content: "\f0e5";}.fa-comments-o:before{content: "\f0e6";}.fa-flash:before, .fa-bolt:before{content: "\f0e7";}.fa-sitemap:before{content: "\f0e8";}.fa-umbrella:before{content: "\f0e9";}.fa-paste:before, .fa-clipboard:before{content: "\f0ea";}.fa-lightbulb-o:before{content: "\f0eb";}.fa-exchange:before{content: "\f0ec";}.fa-cloud-download:before{content: "\f0ed";}.fa-cloud-upload:before{content: "\f0ee";}.fa-user-md:before{content: "\f0f0";}.fa-stethoscope:before{content: "\f0f1";}.fa-suitcase:before{content: "\f0f2";}.fa-bell-o:before{content: "\f0a2";}.fa-coffee:before{content: "\f0f4";}.fa-cutlery:before{content: "\f0f5";}.fa-file-text-o:before{content: "\f0f6";}.fa-building-o:before{content: "\f0f7";}.fa-hospital-o:before{content: "\f0f8";}.fa-ambulance:before{content: "\f0f9";}.fa-medkit:before{content: "\f0fa";}.fa-fighter-jet:before{content: "\f0fb";}.fa-beer:before{content: "\f0fc";}.fa-h-square:before{content: "\f0fd";}.fa-plus-square:before{content: "\f0fe";}.fa-angle-double-left:before{content: "\f100";}.fa-angle-double-right:before{content: "\f101";}.fa-angle-double-up:before{content: "\f102";}.fa-angle-double-down:before{content: "\f103";}.fa-angle-left:before{content: "\f104";}.fa-angle-right:before{content: "\f105";}.fa-angle-up:before{content: "\f106";}.fa-angle-down:before{content: "\f107";}.fa-desktop:before{content: "\f108";}.fa-laptop:before{content: "\f109";}.fa-tablet:before{content: "\f10a";}.fa-mobile-phone:before, .fa-mobile:before{content: "\f10b";}.fa-circle-o:before{content: "\f10c";}.fa-quote-left:before{content: "\f10d";}.fa-quote-right:before{content: "\f10e";}.fa-spinner:before{content: "\f110";}.fa-circle:before{content: "\f111";}.fa-mail-reply:before, .fa-reply:before{content: "\f112";}.fa-github-alt:before{content: "\f113";}.fa-folder-o:before{content: "\f114";}.fa-folder-open-o:before{content: "\f115";}.fa-smile-o:before{content: "\f118";}.fa-frown-o:before{content: "\f119";}.fa-meh-o:before{content: "\f11a";}.fa-gamepad:before{content: "\f11b";}.fa-keyboard-o:before{content: "\f11c";}.fa-flag-o:before{content: "\f11d";}.fa-flag-checkered:before{content: "\f11e";}.fa-terminal:before{content: "\f120";}.fa-code:before{content: "\f121";}.fa-mail-reply-all:before, .fa-reply-all:before{content: "\f122";}.fa-star-half-empty:before, .fa-star-half-full:before, .fa-star-half-o:before{content: "\f123";}.fa-location-arrow:before{content: "\f124";}.fa-crop:before{content: "\f125";}.fa-code-fork:before{content: "\f126";}.fa-unlink:before, .fa-chain-broken:before{content: "\f127";}.fa-question:before{content: "\f128";}.fa-info:before{content: "\f129";}.fa-exclamation:before{content: "\f12a";}.fa-superscript:before{content: "\f12b";}.fa-subscript:before{content: "\f12c";}.fa-eraser:before{content: "\f12d";}.fa-puzzle-piece:before{content: "\f12e";}.fa-microphone:before{content: "\f130";}.fa-microphone-slash:before{content: "\f131";}.fa-shield:before{content: "\f132";}.fa-calendar-o:before{content: "\f133";}.fa-fire-extinguisher:before{content: "\f134";}.fa-rocket:before{content: "\f135";}.fa-maxcdn:before{content: "\f136";}.fa-chevron-circle-left:before{content: "\f137";}.fa-chevron-circle-right:before{content: "\f138";}.fa-chevron-circle-up:before{content: "\f139";}.fa-chevron-circle-down:before{content: "\f13a";}.fa-html5:before{content: "\f13b";}.fa-css3:before{content: "\f13c";}.fa-anchor:before{content: "\f13d";}.fa-unlock-alt:before{content: "\f13e";}.fa-bullseye:before{content: "\f140";}.fa-ellipsis-h:before{content: "\f141";}.fa-ellipsis-v:before{content: "\f142";}.fa-rss-square:before{content: "\f143";}.fa-play-circle:before{content: "\f144";}.fa-ticket:before{content: "\f145";}.fa-minus-square:before{content: "\f146";}.fa-minus-square-o:before{content: "\f147";}.fa-level-up:before{content: "\f148";}.fa-level-down:before{content: "\f149";}.fa-check-square:before{content: "\f14a";}.fa-pencil-square:before{content: "\f14b";}.fa-external-link-square:before{content: "\f14c";}.fa-share-square:before{content: "\f14d";}.fa-compass:before{content: "\f14e";}.fa-toggle-down:before, .fa-caret-square-o-down:before{content: "\f150";}.fa-toggle-up:before, .fa-caret-square-o-up:before{content: "\f151";}.fa-toggle-right:before, .fa-caret-square-o-right:before{content: "\f152";}.fa-euro:before, .fa-eur:before{content: "\f153";}.fa-gbp:before{content: "\f154";}.fa-dollar:before, .fa-usd:before{content: "\f155";}.fa-rupee:before, .fa-inr:before{content: "\f156";}.fa-cny:before, .fa-rmb:before, .fa-yen:before, .fa-jpy:before{content: "\f157";}.fa-ruble:before, .fa-rouble:before, .fa-rub:before{content: "\f158";}.fa-won:before, .fa-krw:before{content: "\f159";}.fa-bitcoin:before, .fa-btc:before{content: "\f15a";}.fa-file:before{content: "\f15b";}.fa-file-text:before{content: "\f15c";}.fa-sort-alpha-asc:before{content: "\f15d";}.fa-sort-alpha-desc:before{content: "\f15e";}.fa-sort-amount-asc:before{content: "\f160";}.fa-sort-amount-desc:before{content: "\f161";}.fa-sort-numeric-asc:before{content: "\f162";}.fa-sort-numeric-desc:before{content: "\f163";}.fa-thumbs-up:before{content: "\f164";}.fa-thumbs-down:before{content: "\f165";}.fa-youtube-square:before{content: "\f166";}.fa-youtube:before{content: "\f167";}.fa-xing:before{content: "\f168";}.fa-xing-square:before{content: "\f169";}.fa-youtube-play:before{content: "\f16a";}.fa-dropbox:before{content: "\f16b";}.fa-stack-overflow:before{content: "\f16c";}.fa-instagram:before{content: "\f16d";}.fa-flickr:before{content: "\f16e";}.fa-adn:before{content: "\f170";}.fa-bitbucket:before{content: "\f171";}.fa-bitbucket-square:before{content: "\f172";}.fa-tumblr:before{content: "\f173";}.fa-tumblr-square:before{content: "\f174";}.fa-long-arrow-down:before{content: "\f175";}.fa-long-arrow-up:before{content: "\f176";}.fa-long-arrow-left:before{content: "\f177";}.fa-long-arrow-right:before{content: "\f178";}.fa-apple:before{content: "\f179";}.fa-windows:before{content: "\f17a";}.fa-android:before{content: "\f17b";}.fa-linux:before{content: "\f17c";}.fa-dribbble:before{content: "\f17d";}.fa-skype:before{content: "\f17e";}.fa-foursquare:before{content: "\f180";}.fa-trello:before{content: "\f181";}.fa-female:before{content: "\f182";}.fa-male:before{content: "\f183";}.fa-gittip:before, .fa-gratipay:before{content: "\f184";}.fa-sun-o:before{content: "\f185";}.fa-moon-o:before{content: "\f186";}.fa-archive:before{content: "\f187";}.fa-bug:before{content: "\f188";}.fa-vk:before{content: "\f189";}.fa-weibo:before{content: "\f18a";}.fa-renren:before{content: "\f18b";}.fa-pagelines:before{content: "\f18c";}.fa-stack-exchange:before{content: "\f18d";}.fa-arrow-circle-o-right:before{content: "\f18e";}.fa-arrow-circle-o-left:before{content: "\f190";}.fa-toggle-left:before, .fa-caret-square-o-left:before{content: "\f191";}.fa-dot-circle-o:before{content: "\f192";}.fa-wheelchair:before{content: "\f193";}.fa-vimeo-square:before{content: "\f194";}.fa-turkish-lira:before, .fa-try:before{content: "\f195";}.fa-plus-square-o:before{content: "\f196";}.fa-space-shuttle:before{content: "\f197";}.fa-slack:before{content: "\f198";}.fa-envelope-square:before{content: "\f199";}.fa-wordpress:before{content: "\f19a";}.fa-openid:before{content: "\f19b";}.fa-institution:before, .fa-bank:before, .fa-university:before{content: "\f19c";}.fa-mortar-board:before, .fa-graduation-cap:before{content: "\f19d";}.fa-yahoo:before{content: "\f19e";}.fa-google:before{content: "\f1a0";}.fa-reddit:before{content: "\f1a1";}.fa-reddit-square:before{content: "\f1a2";}.fa-stumbleupon-circle:before{content: "\f1a3";}.fa-stumbleupon:before{content: "\f1a4";}.fa-delicious:before{content: "\f1a5";}.fa-digg:before{content: "\f1a6";}.fa-pied-piper-pp:before{content: "\f1a7";}.fa-pied-piper-alt:before{content: "\f1a8";}.fa-drupal:before{content: "\f1a9";}.fa-joomla:before{content: "\f1aa";}.fa-language:before{content: "\f1ab";}.fa-fax:before{content: "\f1ac";}.fa-building:before{content: "\f1ad";}.fa-child:before{content: "\f1ae";}.fa-paw:before{content: "\f1b0";}.fa-spoon:before{content: "\f1b1";}.fa-cube:before{content: "\f1b2";}.fa-cubes:before{content: "\f1b3";}.fa-behance:before{content: "\f1b4";}.fa-behance-square:before{content: "\f1b5";}.fa-steam:before{content: "\f1b6";}.fa-steam-square:before{content: "\f1b7";}.fa-recycle:before{content: "\f1b8";}.fa-automobile:before, .fa-car:before{content: "\f1b9";}.fa-cab:before, .fa-taxi:before{content: "\f1ba";}.fa-tree:before{content: "\f1bb";}.fa-spotify:before{content: "\f1bc";}.fa-deviantart:before{content: "\f1bd";}.fa-soundcloud:before{content: "\f1be";}.fa-database:before{content: "\f1c0";}.fa-file-pdf-o:before{content: "\f1c1";}.fa-file-word-o:before{content: "\f1c2";}.fa-file-excel-o:before{content: "\f1c3";}.fa-file-powerpoint-o:before{content: "\f1c4";}.fa-file-photo-o:before, .fa-file-picture-o:before, .fa-file-image-o:before{content: "\f1c5";}.fa-file-zip-o:before, .fa-file-archive-o:before{content: "\f1c6";}.fa-file-sound-o:before, .fa-file-audio-o:before{content: "\f1c7";}.fa-file-movie-o:before, .fa-file-video-o:before{content: "\f1c8";}.fa-file-code-o:before{content: "\f1c9";}.fa-vine:before{content: "\f1ca";}.fa-codepen:before{content: "\f1cb";}.fa-jsfiddle:before{content: "\f1cc";}.fa-life-bouy:before, .fa-life-buoy:before, .fa-life-saver:before, .fa-support:before, .fa-life-ring:before{content: "\f1cd";}.fa-circle-o-notch:before{content: "\f1ce";}.fa-ra:before, .fa-resistance:before, .fa-rebel:before{content: "\f1d0";}.fa-ge:before, .fa-empire:before{content: "\f1d1";}.fa-git-square:before{content: "\f1d2";}.fa-git:before{content: "\f1d3";}.fa-y-combinator-square:before, .fa-yc-square:before, .fa-hacker-news:before{content: "\f1d4";}.fa-tencent-weibo:before{content: "\f1d5";}.fa-qq:before{content: "\f1d6";}.fa-wechat:before, .fa-weixin:before{content: "\f1d7";}.fa-send:before, .fa-paper-plane:before{content: "\f1d8";}.fa-send-o:before, .fa-paper-plane-o:before{content: "\f1d9";}.fa-history:before{content: "\f1da";}.fa-circle-thin:before{content: "\f1db";}.fa-header:before{content: "\f1dc";}.fa-paragraph:before{content: "\f1dd";}.fa-sliders:before{content: "\f1de";}.fa-share-alt:before{content: "\f1e0";}.fa-share-alt-square:before{content: "\f1e1";}.fa-bomb:before{content: "\f1e2";}.fa-soccer-ball-o:before, .fa-futbol-o:before{content: "\f1e3";}.fa-tty:before{content: "\f1e4";}.fa-binoculars:before{content: "\f1e5";}.fa-plug:before{content: "\f1e6";}.fa-slideshare:before{content: "\f1e7";}.fa-twitch:before{content: "\f1e8";}.fa-yelp:before{content: "\f1e9";}.fa-newspaper-o:before{content: "\f1ea";}.fa-wifi:before{content: "\f1eb";}.fa-calculator:before{content: "\f1ec";}.fa-paypal:before{content: "\f1ed";}.fa-google-wallet:before{content: "\f1ee";}.fa-cc-visa:before{content: "\f1f0";}.fa-cc-mastercard:before{content: "\f1f1";}.fa-cc-discover:before{content: "\f1f2";}.fa-cc-amex:before{content: "\f1f3";}.fa-cc-paypal:before{content: "\f1f4";}.fa-cc-stripe:before{content: "\f1f5";}.fa-bell-slash:before{content: "\f1f6";}.fa-bell-slash-o:before{content: "\f1f7";}.fa-trash:before{content: "\f1f8";}.fa-copyright:before{content: "\f1f9";}.fa-at:before{content: "\f1fa";}.fa-eyedropper:before{content: "\f1fb";}.fa-paint-brush:before{content: "\f1fc";}.fa-birthday-cake:before{content: "\f1fd";}.fa-area-chart:before{content: "\f1fe";}.fa-pie-chart:before{content: "\f200";}.fa-line-chart:before{content: "\f201";}.fa-lastfm:before{content: "\f202";}.fa-lastfm-square:before{content: "\f203";}.fa-toggle-off:before{content: "\f204";}.fa-toggle-on:before{content: "\f205";}.fa-bicycle:before{content: "\f206";}.fa-bus:before{content: "\f207";}.fa-ioxhost:before{content: "\f208";}.fa-angellist:before{content: "\f209";}.fa-cc:before{content: "\f20a";}.fa-shekel:before, .fa-sheqel:before, .fa-ils:before{content: "\f20b";}.fa-meanpath:before{content: "\f20c";}.fa-buysellads:before{content: "\f20d";}.fa-connectdevelop:before{content: "\f20e";}.fa-dashcube:before{content: "\f210";}.fa-forumbee:before{content: "\f211";}.fa-leanpub:before{content: "\f212";}.fa-sellsy:before{content: "\f213";}.fa-shirtsinbulk:before{content: "\f214";}.fa-simplybuilt:before{content: "\f215";}.fa-skyatlas:before{content: "\f216";}.fa-cart-plus:before{content: "\f217";}.fa-cart-arrow-down:before{content: "\f218";}.fa-diamond:before{content: "\f219";}.fa-ship:before{content: "\f21a";}.fa-user-secret:before{content: "\f21b";}.fa-motorcycle:before{content: "\f21c";}.fa-street-view:before{content: "\f21d";}.fa-heartbeat:before{content: "\f21e";}.fa-venus:before{content: "\f221";}.fa-mars:before{content: "\f222";}.fa-mercury:before{content: "\f223";}.fa-intersex:before, .fa-transgender:before{content: "\f224";}.fa-transgender-alt:before{content: "\f225";}.fa-venus-double:before{content: "\f226";}.fa-mars-double:before{content: "\f227";}.fa-venus-mars:before{content: "\f228";}.fa-mars-stroke:before{content: "\f229";}.fa-mars-stroke-v:before{content: "\f22a";}.fa-mars-stroke-h:before{content: "\f22b";}.fa-neuter:before{content: "\f22c";}.fa-genderless:before{content: "\f22d";}.fa-facebook-official:before{content: "\f230";}.fa-pinterest-p:before{content: "\f231";}.fa-whatsapp:before{content: "\f232";}.fa-server:before{content: "\f233";}.fa-user-plus:before{content: "\f234";}.fa-user-times:before{content: "\f235";}.fa-hotel:before, .fa-bed:before{content: "\f236";}.fa-viacoin:before{content: "\f237";}.fa-train:before{content: "\f238";}.fa-subway:before{content: "\f239";}.fa-medium:before{content: "\f23a";}.fa-yc:before, .fa-y-combinator:before{content: "\f23b";}.fa-optin-monster:before{content: "\f23c";}.fa-opencart:before{content: "\f23d";}.fa-expeditedssl:before{content: "\f23e";}.fa-battery-4:before, .fa-battery:before, .fa-battery-full:before{content: "\f240";}.fa-battery-3:before, .fa-battery-three-quarters:before{content: "\f241";}.fa-battery-2:before, .fa-battery-half:before{content: "\f242";}.fa-battery-1:before, .fa-battery-quarter:before{content: "\f243";}.fa-battery-0:before, .fa-battery-empty:before{content: "\f244";}.fa-mouse-pointer:before{content: "\f245";}.fa-i-cursor:before{content: "\f246";}.fa-object-group:before{content: "\f247";}.fa-object-ungroup:before{content: "\f248";}.fa-sticky-note:before{content: "\f249";}.fa-sticky-note-o:before{content: "\f24a";}.fa-cc-jcb:before{content: "\f24b";}.fa-cc-diners-club:before{content: "\f24c";}.fa-clone:before{content: "\f24d";}.fa-balance-scale:before{content: "\f24e";}.fa-hourglass-o:before{content: "\f250";}.fa-hourglass-1:before, .fa-hourglass-start:before{content: "\f251";}.fa-hourglass-2:before, .fa-hourglass-half:before{content: "\f252";}.fa-hourglass-3:before, .fa-hourglass-end:before{content: "\f253";}.fa-hourglass:before{content: "\f254";}.fa-hand-grab-o:before, .fa-hand-rock-o:before{content: "\f255";}.fa-hand-stop-o:before, .fa-hand-paper-o:before{content: "\f256";}.fa-hand-scissors-o:before{content: "\f257";}.fa-hand-lizard-o:before{content: "\f258";}.fa-hand-spock-o:before{content: "\f259";}.fa-hand-pointer-o:before{content: "\f25a";}.fa-hand-peace-o:before{content: "\f25b";}.fa-trademark:before{content: "\f25c";}.fa-registered:before{content: "\f25d";}.fa-creative-commons:before{content: "\f25e";}.fa-gg:before{content: "\f260";}.fa-gg-circle:before{content: "\f261";}.fa-tripadvisor:before{content: "\f262";}.fa-odnoklassniki:before{content: "\f263";}.fa-odnoklassniki-square:before{content: "\f264";}.fa-get-pocket:before{content: "\f265";}.fa-wikipedia-w:before{content: "\f266";}.fa-safari:before{content: "\f267";}.fa-chrome:before{content: "\f268";}.fa-firefox:before{content: "\f269";}.fa-opera:before{content: "\f26a";}.fa-internet-explorer:before{content: "\f26b";}.fa-tv:before, .fa-television:before{content: "\f26c";}.fa-contao:before{content: "\f26d";}.fa-500px:before{content: "\f26e";}.fa-amazon:before{content: "\f270";}.fa-calendar-plus-o:before{content: "\f271";}.fa-calendar-minus-o:before{content: "\f272";}.fa-calendar-times-o:before{content: "\f273";}.fa-calendar-check-o:before{content: "\f274";}.fa-industry:before{content: "\f275";}.fa-map-pin:before{content: "\f276";}.fa-map-signs:before{content: "\f277";}.fa-map-o:before{content: "\f278";}.fa-map:before{content: "\f279";}.fa-commenting:before{content: "\f27a";}.fa-commenting-o:before{content: "\f27b";}.fa-houzz:before{content: "\f27c";}.fa-vimeo:before{content: "\f27d";}.fa-black-tie:before{content: "\f27e";}.fa-fonticons:before{content: "\f280";}.fa-reddit-alien:before{content: "\f281";}.fa-edge:before{content: "\f282";}.fa-credit-card-alt:before{content: "\f283";}.fa-codiepie:before{content: "\f284";}.fa-modx:before{content: "\f285";}.fa-fort-awesome:before{content: "\f286";}.fa-usb:before{content: "\f287";}.fa-product-hunt:before{content: "\f288";}.fa-mixcloud:before{content: "\f289";}.fa-scribd:before{content: "\f28a";}.fa-pause-circle:before{content: "\f28b";}.fa-pause-circle-o:before{content: "\f28c";}.fa-stop-circle:before{content: "\f28d";}.fa-stop-circle-o:before{content: "\f28e";}.fa-shopping-bag:before{content: "\f290";}.fa-shopping-basket:before{content: "\f291";}.fa-hashtag:before{content: "\f292";}.fa-bluetooth:before{content: "\f293";}.fa-bluetooth-b:before{content: "\f294";}.fa-percent:before{content: "\f295";}.fa-gitlab:before{content: "\f296";}.fa-wpbeginner:before{content: "\f297";}.fa-wpforms:before{content: "\f298";}.fa-envira:before{content: "\f299";}.fa-universal-access:before{content: "\f29a";}.fa-wheelchair-alt:before{content: "\f29b";}.fa-question-circle-o:before{content: "\f29c";}.fa-blind:before{content: "\f29d";}.fa-audio-description:before{content: "\f29e";}.fa-volume-control-phone:before{content: "\f2a0";}.fa-braille:before{content: "\f2a1";}.fa-assistive-listening-systems:before{content: "\f2a2";}.fa-asl-interpreting:before, .fa-american-sign-language-interpreting:before{content: "\f2a3";}.fa-deafness:before, .fa-hard-of-hearing:before, .fa-deaf:before{content: "\f2a4";}.fa-glide:before{content: "\f2a5";}.fa-glide-g:before{content: "\f2a6";}.fa-signing:before, .fa-sign-language:before{content: "\f2a7";}.fa-low-vision:before{content: "\f2a8";}.fa-viadeo:before{content: "\f2a9";}.fa-viadeo-square:before{content: "\f2aa";}.fa-snapchat:before{content: "\f2ab";}.fa-snapchat-ghost:before{content: "\f2ac";}.fa-snapchat-square:before{content: "\f2ad";}.fa-pied-piper:before{content: "\f2ae";}.fa-first-order:before{content: "\f2b0";}.fa-yoast:before{content: "\f2b1";}.fa-themeisle:before{content: "\f2b2";}.fa-google-plus-circle:before, .fa-google-plus-official:before{content: "\f2b3";}.fa-fa:before, .fa-font-awesome:before{content: "\f2b4";}.fa-handshake-o:before{content: "\f2b5";}.fa-envelope-open:before{content: "\f2b6";}.fa-envelope-open-o:before{content: "\f2b7";}.fa-linode:before{content: "\f2b8";}.fa-address-book:before{content: "\f2b9";}.fa-address-book-o:before{content: "\f2ba";}.fa-vcard:before, .fa-address-card:before{content: "\f2bb";}.fa-vcard-o:before, .fa-address-card-o:before{content: "\f2bc";}.fa-user-circle:before{content: "\f2bd";}.fa-user-circle-o:before{content: "\f2be";}.fa-user-o:before{content: "\f2c0";}.fa-id-badge:before{content: "\f2c1";}.fa-drivers-license:before, .fa-id-card:before{content: "\f2c2";}.fa-drivers-license-o:before, .fa-id-card-o:before{content: "\f2c3";}.fa-quora:before{content: "\f2c4";}.fa-free-code-camp:before{content: "\f2c5";}.fa-telegram:before{content: "\f2c6";}.fa-thermometer-4:before, .fa-thermometer:before, .fa-thermometer-full:before{content: "\f2c7";}.fa-thermometer-3:before, .fa-thermometer-three-quarters:before{content: "\f2c8";}.fa-thermometer-2:before, .fa-thermometer-half:before{content: "\f2c9";}.fa-thermometer-1:before, .fa-thermometer-quarter:before{content: "\f2ca";}.fa-thermometer-0:before, .fa-thermometer-empty:before{content: "\f2cb";}.fa-shower:before{content: "\f2cc";}.fa-bathtub:before, .fa-s15:before, .fa-bath:before{content: "\f2cd";}.fa-podcast:before{content: "\f2ce";}.fa-window-maximize:before{content: "\f2d0";}.fa-window-minimize:before{content: "\f2d1";}.fa-window-restore:before{content: "\f2d2";}.fa-times-rectangle:before, .fa-window-close:before{content: "\f2d3";}.fa-times-rectangle-o:before, .fa-window-close-o:before{content: "\f2d4";}.fa-bandcamp:before{content: "\f2d5";}.fa-grav:before{content: "\f2d6";}.fa-etsy:before{content: "\f2d7";}.fa-imdb:before{content: "\f2d8";}.fa-ravelry:before{content: "\f2d9";}.fa-eercast:before{content: "\f2da";}.fa-microchip:before{content: "\f2db";}.fa-snowflake-o:before{content: "\f2dc";}.fa-superpowers:before{content: "\f2dd";}.fa-wpexplorer:before{content: "\f2de";}.fa-meetup:before{content: "\f2e0";}.visually-hidden{position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); border: 0;}.visually-hidden-focusable:active, .visually-hidden-focusable:focus{position: static; width: auto; height: auto; margin: 0; overflow: visible; clip: auto;}

/* /web/static/lib/odoo_ui_icons/style.css */
@font-face{font-family: 'odoo_ui_icons'; src: url('/web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff2') format('woff2'), url('/web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff') format('woff'); font-weight: normal; font-style: normal; font-display: block;}.oi{display: inline-block; font-family: 'odoo_ui_icons'; speak: never; font-style: normal; font-weight: normal; font-variant: normal; text-transform: none; line-height: 1; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;}.oi-view-pivot:before{content: '\e800';}.oi-text-break:before{content: '\e801';}.oi-text-inline:before{content: '\e802';}.oi-voip:before{content: '\e803';}.oi-odoo:before{content: '\e806';}.oi-search:before{content: '\e808';}.oi-group:before{content: '\e80a';}.oi-settings-adjust:before{content: '\e80c';}.oi-apps:before{content: '\e80d';}.oi-panel-right:before{content: '\e810';}.oi-launch:before{content: '\e812';}.oi-studio:before{content: '\e813';}.oi-view-kanban:before{content: '\e814';}.oi-text-wrap:before{content: '\e815';}.oi-view-cohort:before{content: '\e816';}.oi-view-list:before{content: '\e817';}.oi-gif-picker:before{content: '\e82e';}.oi-chevron-down:before{content: '\e839';}.oi-chevron-left:before{content: '\e83a';}.oi-chevron-right:before{content: '\e83b';}.oi-chevron-up:before{content: '\e83c';}.oi-arrows-h:before{content: '\e83d';}.oi-arrows-v:before{content: '\e83e';}.oi-arrow-down-left:before{content: '\e83f';}.oi-arrow-down-right:before{content: '\e840';}.oi-arrow-down:before{content: '\e841';}.oi-arrow-left:before{content: '\e842';}.oi-arrow-right:before{content: '\e843';}.oi-arrow-up-left:before{content: '\e844';}.oi-arrow-up-right:before{content: '\e845';}.oi-arrow-up:before{content: '\e846';}.oi-draggable:before{content: '\e847';}.oi-view:before{content: '\e861';}.oi-archive:before{content: '\e862';}.oi-unarchive:before{content: '\e863';}.oi-text-effect:before{content: '\e827';}.oi-smile-add:before{content: '\e84e';}.oi-close:before{content: '\e852';}.oi-food-delivery:before{content: '\e82a';}.o_rtl .oi-chevron-left, .o_rtl .oi-chevron-right, .o_rtl .oi-arrow-down-left, .o_rtl .oi-arrow-down-right, .o_rtl .oi-arrow-left, .o_rtl .oi-arrow-right, .o_rtl .oi-arrow-up-left, .o_rtl .oi-arrow-up-right{transform: rotate(180deg);}

/* /web/static/src/webclient/webclient.scss */
 :root{--o-webclient-color-scheme:bright; font-size: 1rem;}html, body{position: relative; width: 100%; height: 100%;}tfoot tr, tfoot td, tfoot th{border-style: none;}.o_web_client{direction: ltr; position: relative; background-color: #f8f9fa; color-scheme: bright;}.o_icon_button{background-color: transparent; border: 0; padding: 0; outline: none;}kbd{border: 1px solid #e9ecef; box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.2), inset 0px -1px 1px 1px rgba(233, 236, 239, 0.8), inset 0px 2px 0px 0px rgba(255, 255, 255, 0.8);}.bg-primary-light{--bg-light: rgba(113, 99, 158, 0.5); --color-light: #000000; background-color: rgba(113, 99, 158, 0.5) !important; color: #000000;}.bg-primary-light .text-muted, .o_colored_level .bg-primary-light .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-primary-light:hover, a.bg-primary-light:focus, button.bg-primary-light:hover, button.bg-primary-light:focus{--bg-light: rgba(90, 79, 127, 0.5); --color-light: #000000; background-color: rgba(90, 79, 127, 0.5) !important; color: #000000;}.bg-secondary-light{--bg-light: rgba(222, 226, 230, 0.5); --color-light: #000000; background-color: rgba(222, 226, 230, 0.5) !important; color: #000000;}.bg-secondary-light .text-muted, .o_colored_level .bg-secondary-light .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-secondary-light:hover, a.bg-secondary-light:focus, button.bg-secondary-light:hover, button.bg-secondary-light:focus{--bg-light: rgba(193, 201, 208, 0.5); --color-light: #000000; background-color: rgba(193, 201, 208, 0.5) !important; color: #000000;}.bg-success-light{--bg-light: rgba(40, 167, 69, 0.5); --color-light: #000000; background-color: rgba(40, 167, 69, 0.5) !important; color: #000000;}.bg-success-light .text-muted, .o_colored_level .bg-success-light .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-success-light:hover, a.bg-success-light:focus, button.bg-success-light:hover, button.bg-success-light:focus{--bg-light: rgba(30, 126, 52, 0.5); --color-light: #000000; background-color: rgba(30, 126, 52, 0.5) !important; color: #000000;}.bg-info-light{--bg-light: rgba(23, 162, 184, 0.5); --color-light: #000000; background-color: rgba(23, 162, 184, 0.5) !important; color: #000000;}.bg-info-light .text-muted, .o_colored_level .bg-info-light .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-info-light:hover, a.bg-info-light:focus, button.bg-info-light:hover, button.bg-info-light:focus{--bg-light: rgba(17, 122, 139, 0.5); --color-light: #000000; background-color: rgba(17, 122, 139, 0.5) !important; color: #000000;}.bg-warning-light{--bg-light: rgba(255, 172, 0, 0.5); --color-light: #000000; background-color: rgba(255, 172, 0, 0.5) !important; color: #000000;}.bg-warning-light .text-muted, .o_colored_level .bg-warning-light .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-warning-light:hover, a.bg-warning-light:focus, button.bg-warning-light:hover, button.bg-warning-light:focus{--bg-light: rgba(204, 138, 0, 0.5); --color-light: #000000; background-color: rgba(204, 138, 0, 0.5) !important; color: #000000;}.bg-danger-light{--bg-light: rgba(220, 53, 69, 0.5); --color-light: #000000; background-color: rgba(220, 53, 69, 0.5) !important; color: #000000;}.bg-danger-light .text-muted, .o_colored_level .bg-danger-light .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-danger-light:hover, a.bg-danger-light:focus, button.bg-danger-light:hover, button.bg-danger-light:focus{--bg-light: rgba(189, 33, 48, 0.5); --color-light: #000000; background-color: rgba(189, 33, 48, 0.5) !important; color: #000000;}.bg-light-light{--bg-light: rgba(248, 249, 250, 0.5); --color-light: #000000; background-color: rgba(248, 249, 250, 0.5) !important; color: #000000;}.bg-light-light .text-muted, .o_colored_level .bg-light-light .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-light-light:hover, a.bg-light-light:focus, button.bg-light-light:hover, button.bg-light-light:focus{--bg-light: rgba(218, 224, 229, 0.5); --color-light: #000000; background-color: rgba(218, 224, 229, 0.5) !important; color: #000000;}.bg-dark-light{--bg-light: rgba(33, 37, 41, 0.5); --color-light: #FFFFFF; background-color: rgba(33, 37, 41, 0.5) !important; color: #FFFFFF;}.bg-dark-light .text-muted, .o_colored_level .bg-dark-light .text-muted{color: rgba(255, 255, 255, 0.7) !important;}a.bg-dark-light:hover, a.bg-dark-light:focus, button.bg-dark-light:hover, button.bg-dark-light:focus{--bg-light: rgba(10, 12, 13, 0.5); --color-light: #FFFFFF; background-color: rgba(10, 12, 13, 0.5) !important; color: #FFFFFF;}.badge{min-width: 3ch; line-height: 1.1; border: 0; font-size: 0.85em; user-select: none; font-weight: 500; display: inline-block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; transition: none; color: #000000;}:not(.dropstart) > .dropdown-item.active, :not(.dropstart) > .dropdown-item.selected{position: relative; font-weight: 500;}:not(.dropstart) > .dropdown-item.active:focus, :not(.dropstart) > .dropdown-item.active:hover, :not(.dropstart) > .dropdown-item.selected:focus, :not(.dropstart) > .dropdown-item.selected:hover{background-color: rgba(0, 0, 0, 0.08);}:not(.dropstart) > .dropdown-item.active:not(.dropdown-item_active_noarrow):before, :not(.dropstart) > .dropdown-item.selected:not(.dropdown-item_active_noarrow):before{position: absolute; top: 0; left: auto; bottom: auto; right: auto; transform: translate(-1.5em, 90%); font: .7em/1em FontAwesome; color: #66598f; content: "\f00c";}:not(.dropstart) > .dropdown-item.active:not(.dropdown-item_active_noarrow).disabled:before, :not(.dropstart) > .dropdown-item.selected:not(.dropdown-item_active_noarrow).disabled:before{color: rgba(73, 80, 87, 0.76);}.o-dropdown.dropstart > .dropdown-item.dropdown-toggle:not(.dropdown-item_active_noarrow).active::after, .o-dropdown.dropstart > .dropdown-item.dropdown-toggle:not(.dropdown-item_active_noarrow).selected::after{position: absolute; top: 0; left: 90%; bottom: auto; right: auto; transform: translate(0, 90%); font: .7em/1em FontAwesome; color: #66598f; display: inline-block; content: "\f00c"; border: 0;}.o-dropdown.dropstart > .dropdown-item.dropdown-toggle:not(.dropdown-item_active_noarrow).active.disabled:after, .o-dropdown.dropstart > .dropdown-item.dropdown-toggle:not(.dropdown-item_active_noarrow).selected.disabled:after{color: rgba(73, 80, 87, 0.76);}.dropdown-header{font-weight: 500; padding-bottom: .1em;}.dropdown-header:not(:first-child){margin-top: .3em;}.dropdown-divider:first-child{display: none;}@media print{.table-responsive{overflow-x: initial;}}[type="action"], [type="toggle"]{cursor: pointer !important;}.o_web_client.o_touch_device .btn, .o_web_client.o_touch_device .btn .btn-sm{font-size: 0.875rem; padding: 7px 14px;}.o_web_client.o_touch_device .btn:has(.fa-fw:only-child, .oi-fw:only-child){padding: 7px 10px;}.o_web_client.o_touch_device .btn.fa{font-size: 1.3em; padding: 2px 10px;}[type="text"], [type="password"], [type="number"], [type="email"], [type="tel"], textarea, select{width: 100%; display: block; outline: none;}select{cursor: pointer; min-width: 50px; -webkit-appearance: none; -moz-appearance: none; appearance: none; background: transparent url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='7' height='4' viewBox='0 0 7 4'><polygon fill='%23495057' points='3.5 4 7 0 0 0'/></svg>") no-repeat right center; border-radius: 0; color: #495057;}select > option{background: #f8f9fa;}select:-moz-focusring{color: transparent; text-shadow: 0 0 0 #495057;}select:-moz-focusring > option{color: #495057;}::-webkit-input-placeholder{color: #bec5cc;}::-moz-placeholder{color: #bec5cc;}:-ms-input-placeholder{color: #bec5cc;}@media (max-width: 767.98px){h1, .h1{font-size: 1.3125rem;}h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6{font-size: 1rem;}}.alert.alert-info, .alert.alert-success, .alert.alert-warning, .alert.alert-danger{border-width: 0 0 0 3px;}.alert a{font-weight: 500;}.badge.text-bg-default, .badge.bg-light, .badge.text-bg-light, .badge.bg-default, .badge.text-primary{outline: 1px solid #71639e; outline-offset: -1px;}.btn:focus:hover{box-shadow: none;}.navbar .navbar-toggle{border-color: transparent;}.label{border-radius: 0; font-size: 1em;}

/* /web/static/src/core/utils/transitions.scss */
 

/* /web/static/src/core/action_swiper/action_swiper.scss */
 .o_actionswiper{position: relative; touch-action: pan-y;}.o_actionswiper_target_container{transition: transform 0.4s;}.o_actionswiper_swiping{transition: none;}.o_actionswiper_right_swipe_area{transform: translateX(-100%); inset: 0 auto auto 0;}.o_actionswiper_left_swipe_area{transform: translateX(100%); inset: 0 0 auto auto;}

/* /web/static/src/core/autocomplete/autocomplete.scss */
 .o-autocomplete .o-autocomplete--input{width: 100%;}

/* /web/static/src/core/avatar/avatar.scss */
 .o_avatar img, .o_avatar .o_avatar_empty, img.o_avatar{height: var(--Avatar-size, 1.7145em); aspect-ratio: 1; object-fit: cover;}.o_avatar_empty{background: #000000; opacity: .1;}

/* /web/static/src/core/barcode/barcode_dialog.scss */
 .modal .o-barcode-modal .modal-body{overflow: hidden;}@media (max-width: 767.98px){.modal .o-barcode-modal .modal-body{padding: 0;}}.modal .o-barcode-modal .modal-body video{object-fit: cover;}

/* /web/static/src/core/barcode/crop_overlay.scss */
 .o_crop_container{position: relative;}.o_crop_container > *{grid-row: 1 / -1; grid-column: 1 / -1;}.o_crop_container .o_crop_overlay{background-color: RGB(0 0 0/0.75); mix-blend-mode: darken;}.o_crop_container .o_crop_overlay::after{content: ''; display: block; height: 100%; width: 100%; clip-path: inset(var(--o-crop-y, 0px) var(--o-crop-x, 0px)); background-color: white;}.o_crop_container .o_crop_icon{--o-crop-icon-width: 20px; --o-crop-icon-height: 20px; position: absolute; width: var(--o-crop-icon-width); height: var(--o-crop-icon-height); left: calc(var(--o-crop-icon-x, 0px) - (var(--o-crop-icon-width) / 2)); top: calc(var(--o-crop-icon-y, 0px) - (var(--o-crop-icon-height) / 2));}

/* /web/static/src/core/checkbox/checkbox.scss */
 .o-checkbox{width: fit-content;}

/* /web/static/src/core/colorlist/colorlist.scss */
 .o_colorlist button{border: 1px solid var(--body-bg); box-shadow: 0 0 0 1px #adb5bd; width: 22px; height: 17px;}.o_colorlist .o_colorlist_selected{box-shadow: 0 0 0 2px #71639e !important;}.o_colorlist_item_color_1{--background-color: RGBA(255, 155.5, 155.5, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(67.15870044, 11.84129956, 11.84129956, var(--text-opacity, 1)); color: var(--color) !important;}.o_colorlist_item_color_2{--background-color: RGBA(247.0375, 198.06116071, 152.4625, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(52.9, 33.325, 15.1, var(--text-opacity, 1)); color: var(--color) !important;}.o_colorlist_item_color_3{--background-color: RGBA(252.88960843, 226.89175248, 135.61039157, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(47.45993976, 39.05405514, 9.54006024, var(--text-opacity, 1)); color: var(--color) !important;}.o_colorlist_item_color_4{--background-color: RGBA(187.45210396, 215.03675558, 248.04789604, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(25.3049505, 49.60939855, 78.6950495, var(--text-opacity, 1)); color: var(--color) !important;}.o_colorlist_item_color_5{--background-color: RGBA(216.79194664, 167.70805336, 203.91748283, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(28.91432806, 24.08567194, 27.64779531, var(--text-opacity, 1)); color: var(--color) !important;}.o_colorlist_item_color_6{--background-color: RGBA(247.84539474, 213.9484835, 199.65460526, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(85.32105263, 46.88635147, 30.67894737, var(--text-opacity, 1)); color: var(--color) !important;}.o_colorlist_item_color_7{--background-color: RGBA(136.6125, 224.8875, 218.94591346, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(10.58333333, 19.41666667, 18.82211538, var(--text-opacity, 1)); color: var(--color) !important;}.o_colorlist_item_color_8{--background-color: RGBA(150.60535714, 165.68382711, 248.89464286, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(13.95714286, 20.10665584, 54.04285714, var(--text-opacity, 1)); color: var(--color) !important;}.o_colorlist_item_color_9{--background-color: RGBA(254.94583333, 157.55416667, 203.95543194, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(68.805, 12.195, 39.16625654, var(--text-opacity, 1)); color: var(--color) !important;}.o_colorlist_item_color_10{--background-color: RGBA(182.62075688, 236.87924312, 189.81831118, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(30.82018349, 57.17981651, 34.3168695, var(--text-opacity, 1)); color: var(--color) !important;}.o_colorlist_item_color_11{--background-color: RGBA(230.11575613, 219.41069277, 252.08930723, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(56.75321978, 31.58433735, 108.41566265, var(--text-opacity, 1)); color: var(--color) !important;}.o_colorlist_item_color_0{background: linear-gradient(45deg, rgba(var(--body-bg), 0) 0%, rgba(var(--body-bg), 0) 48%, #dc3545 48%, #dc3545 52%, rgba(var(--body-bg), 0) 52%, rgba(var(--body-bg), 0) 100%);}

/* /web/static/src/core/colorpicker/colorpicker.scss */
 .o_colorpicker_widget .o_color_pick_area{height: 125px; background-image: linear-gradient(to bottom, white 0%, rgba(255, 255, 255, 0) 50%, rgba(0, 0, 0, 0) 50%, black 100%), linear-gradient(to right, gray 0%, rgba(128, 128, 128, 0) 100%); cursor: crosshair;}.o_colorpicker_widget .o_color_slider{background: linear-gradient(#F00 0%, #FF0 16.66%, #0F0 33.33%, #0FF 50%, #00F 66.66%, #F0F 83.33%, #F00 100%);}.o_colorpicker_widget .o_color_slider, .o_colorpicker_widget .o_opacity_slider{width: 4%; margin-right: 2%; cursor: pointer;}.o_colorpicker_widget .o_slider_pointer, .o_colorpicker_widget .o_opacity_pointer{position: absolute; top: auto; left: -50%; bottom: auto; right: auto; width: 200%; height: 8px; margin-top: -2px;}.o_colorpicker_widget .o_slider_pointer, .o_colorpicker_widget .o_opacity_pointer, .o_colorpicker_widget .o_picker_pointer, .o_colorpicker_widget .o_color_preview{box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.9); border: 1px solid black;}.o_colorpicker_widget .o_color_picker_inputs{font-size: 10px;}.o_colorpicker_widget .o_color_picker_inputs input{font-family: monospace !important; height: 18px; font-size: 11px;}.o_colorpicker_widget .o_color_picker_inputs .o_hex_div input{width: 7ch;}.o_colorpicker_widget .o_color_picker_inputs .o_rgba_div input{margin-right: 3px; width: 3ch;}

/* /web/static/src/core/commands/command_palette.scss */
 .o_command_palette{top: 120px; position: absolute;}.o_command_palette > .modal-body{padding: 0;}.o_command_palette_listbox{max-height: 50vh;}.o_command_palette_listbox .o_command.focused{background: rgba(221, 219, 232, 0.65);}.o_command_palette_listbox .o_command_hotkey{align-items: center; justify-content: space-between; background-color: inherit; padding: 0.5rem 1.3em; display: -webkit-box; display: -webkit-flex; display: flex;}.o_command_palette_listbox .o_command_hotkey > icon{position: relative; top: 0.4em;}.o_command_palette_listbox .o_command a{text-decoration: none; color: inherit;}.o_command_palette .o_favorite{color: #f3cc00;}.o_command_palette .o_app_icon{height: 1.8rem; width: 1.8rem;}.o_command_palette .o_command{cursor: pointer;}.o_command_palette .o_command .text-ellipsis{text-overflow: ellipsis; white-space: nowrap; overflow: hidden;}.o_command_palette .o_command .o_command_focus{white-space: nowrap; opacity: 0.9;}

/* /web/static/src/core/datetime/datetime_picker.scss */
 .o_datetime_picker{--DateTimePicker__Cell-size-md: 3rem; --DateTimePicker__Cell-size-lg: 5rem; --DateTimePicker__Template-rows: 3; --DateTimePicker__Template-columns: 4; --DateTimePicker__Day-template-rows: 6;}.o_datetime_picker .o_datetime_picker_header .o_header_part{text-transform: none;}.o_datetime_picker .o_date_item_cell{position: relative; border-radius: 0;}.o_datetime_picker .o_current, .o_datetime_picker .o_selected{color: #212529;}.o_datetime_picker .o_selected:not(.o_select_start):not(.o_select_end){background: #dddbe8;}.o_datetime_picker .o_current:before, .o_datetime_picker .o_highlighted:before, .o_datetime_picker .o_select_start:before, .o_datetime_picker .o_select_end:before{content: ""; position: absolute; box-shadow: inset 0 0 0 1px #71639e; width: 100%; aspect-ratio: 1; border-radius: 100%; z-index: 1;}.o_datetime_picker .o_select_start:before, .o_datetime_picker .o_select_end:before{background: #d1cee0;}.o_datetime_picker .o_select_start:after, .o_datetime_picker .o_select_end:after{content: ""; position: absolute; background: transparent; width: 50%; aspect-ratio: 1/2;}.o_datetime_picker .o_select_start:not(.o_select_end):after, .o_datetime_picker .o_select_end:not(.o_select_end):after{right: 0; background: #dddbe8;}.o_datetime_picker .o_select_start:not(.o_select_start):after, .o_datetime_picker .o_select_end:not(.o_select_start):after{right: 50%; background: #dddbe8;}.o_datetime_picker .o_today span{position: relative;}.o_datetime_picker .o_today span::after{content: ""; position: absolute; left: 50%; bottom: -4px; transform: translateX(-50%); width: 0.95em; height: 0.2em; border-radius: 50rem; background: #dc3545;}.o_datetime_picker .o_time_picker_select{background: none;}.o_datetime_picker .o_time_picker_select:focus, .o_datetime_picker .o_time_picker_select:hover{border-color: var(--primary);}.o_datetime_picker .o_date_picker{grid-template-rows: repeat(var(--DateTimePicker__Day-template-rows), 1fr); grid-template-columns: repeat(var(--DateTimePicker__Day-template-columns), 1fr);}.o_datetime_picker .o_date_item_picker{grid-template-rows: repeat(var(--DateTimePicker__Template-rows), 1fr); grid-template-columns: repeat(var(--DateTimePicker__Template-columns), 1fr);}.o_datetime_picker .o_date_item_picker .o_datetime_button.o_selected:not(.o_select_start):not(.o_select_end), .o_datetime_picker .o_date_item_picker .o_datetime_button:hover:not(.o_select_start):not(.o_select_end), .o_datetime_picker .o_date_item_picker .o_datetime_button.o_today:not(.o_selected):hover:not(.o_select_start):not(.o_select_end){background: #dddbe8; color: #212529;}.o_datetime_picker .o_center{display: grid; place-items: center;}.o_datetime_picker .o_zoom_out{gap: calc( var(--border-width) * 4 + 0.875rem * 2 + 0.625rem * 4);}.o_datetime_picker .o_cell_md{aspect-ratio: 1;}@media (min-width: 768px){.o_datetime_picker .o_cell_md{padding: 0.4rem; width: var(--DateTimePicker__Cell-size-md); height: var(--DateTimePicker__Cell-size-md);}}.o_datetime_picker .o_cell_lg{width: var(--DateTimePicker__Cell-size-lg); height: var(--DateTimePicker__Cell-size-lg);}.o_datetime_picker .o_text_sm{font-size: 0.875rem;}.o_datetime_picker .o_time_picker{direction: ltr;}

/* /web/static/src/core/debug/debug_menu.scss */
 .o_dialog .o_debug_manager .dropdown-toggle{padding: 0 4px; margin: 2px 10px 2px 0;}

/* /web/static/src/core/dialog/dialog.scss */
 .modal.o_technical_modal .modal-content .modal-header .modal-title{overflow: hidden; white-space: nowrap; text-overflow: ellipsis;}.modal.o_technical_modal .modal-content .modal-header .o_expand_button{opacity: 75%;}.modal.o_technical_modal .modal-content .modal-header .o_expand_button:hover{opacity: 100%;}.modal.o_technical_modal .modal-footer{text-align: left;}.modal.o_technical_modal .modal-footer footer, .modal.o_technical_modal .modal-footer .o_form_buttons_edit, .modal.o_technical_modal .modal-footer .o_form_buttons_view{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; justify-content: space-around; gap: 4px;}@media (min-width: 768px){.modal.o_technical_modal .modal-footer footer, .modal.o_technical_modal .modal-footer .o_form_buttons_edit, .modal.o_technical_modal .modal-footer .o_form_buttons_view{-webkit-box-pack: start; justify-content: flex-start;}}.modal.o_technical_modal .modal-footer button{margin: 0;}@media (max-width: 767.98px){.modal.o_technical_modal .modal-footer .btn{width: 45%; text-overflow: ellipsis; white-space: inherit;}}@media (max-width: 575.98px){.modal.o_technical_modal.o_modal_full .modal-dialog{margin: 0px; height: 100%;}.modal.o_technical_modal.o_modal_full .modal-dialog .modal-content{height: 100%; border: none;}.modal.o_technical_modal.o_modal_full .modal-dialog .modal-content .modal-header{background: #71639e;}.modal.o_technical_modal.o_modal_full .modal-dialog .modal-content .modal-header .modal-title, .modal.o_technical_modal.o_modal_full .modal-dialog .modal-content .modal-header .btn{color: rgba(255, 255, 255, 0.9);}.modal.o_technical_modal.o_modal_full .modal-dialog .modal-content .modal-body{height: 100%; overflow-y: auto;}}.modal.o_inactive_modal{z-index: 1049;}.o_dialog > .modal{display: block;}@media (min-width: 576px){.modal-fs{width: calc(100% - 3.5rem); max-width: none;}}@media (max-width: 767.98px){.modal.o_modal_full .modal-content .modal-header{align-items: center; height: 46px; padding: 0 1rem;}.modal.o_modal_full .modal-content .modal-footer{padding-top: 1rem; padding-right: 16px; padding-bottom: 0.5rem; padding-left: 16px; box-shadow: 0 1rem 2rem black; z-index: 0;}}

/* /web/static/src/core/dropdown/accordion_item.scss */
 .o_accordion_toggle::after{display: inline-block; margin-left: 3.4px; vertical-align: 3.4px; content: ""; border-top: 4px solid; border-right: 4px solid transparent; border-bottom: 0; border-left: 4px solid transparent;}.o_accordion_toggle:empty::after{margin-left: 0;}.o_accordion_toggle.open::after{display: inline-block; margin-left: 3.4px; vertical-align: 3.4px; content: ""; border-top: 0; border-right: 4px solid transparent; border-bottom: 4px solid; border-left: 4px solid transparent;}.o_accordion_toggle.open:empty::after{margin-left: 0;}.o_accordion_toggle::after{position: absolute; top: 0; left: auto; bottom: auto; right: 0; transform: translate(-0.6em, 0.8em);}

/* /web/static/src/core/dropdown/dropdown.scss */
 .o-dropdown{}.o-dropdown.dropdown-toggle::after{content: none; border: none;}.o-dropdown.btn-secondary, .o-dropdown.btn-outline-secondary{transition: background-color .2s ease-in-out, border-color .2s ease-in-out;}.o-dropdown.btn-secondary.show, .o-dropdown.btn-outline-secondary.show{color: #212529; background-color: #dddbe8; border-color: #71639e;}.o-dropdown.dropup > .o-dropdown--menu, .o-dropdown.dropdown > .o-dropdown--menu, .o-dropdown.dropstart > .o-dropdown--menu, .o-dropdown.dropend > .o-dropdown--menu{left: auto; right: auto; margin-left: 0; margin-right: 0;}.o-dropdown.o-dropdown-caret:not(.o-dropdown-no-caret), .o-dropdopwn-item.o-dropdown-caret:not(.o-dropdown-no-caret), .o_menu_item.o-dropdown-caret:not(.o-dropdown-no-caret){display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: nowrap; flex-wrap: nowrap; align-items: center; gap: 5px;}.o-dropdown.o-dropdown-caret:not(.o-dropdown-no-caret):after, .o-dropdopwn-item.o-dropdown-caret:not(.o-dropdown-no-caret):after, .o_menu_item.o-dropdown-caret:not(.o-dropdown-no-caret):after{content: "\f0d7"; font-family: FontAwesome; display: inline-block; margin-left: auto; transform: rotate(0deg); transition: .25s ease-in-out;}.o-dropdown.o-dropdown-caret:not(.o-dropdown-no-caret).show:after, .o-dropdopwn-item.o-dropdown-caret:not(.o-dropdown-no-caret).show:after, .o_menu_item.o-dropdown-caret:not(.o-dropdown-no-caret).show:after{transform: rotate(180deg);}.o-dropdown.o-dropdown-caret:not(.o-dropdown-no-caret).o-dropdown--has-parent, .o-dropdopwn-item.o-dropdown-caret:not(.o-dropdown-no-caret).o-dropdown--has-parent, .o_menu_item.o-dropdown-caret:not(.o-dropdown-no-caret).o-dropdown--has-parent{padding-right: 8px;}.o-dropdown.o-dropdown-caret:not(.o-dropdown-no-caret).o-dropdown--has-parent:after, .o-dropdopwn-item.o-dropdown-caret:not(.o-dropdown-no-caret).o-dropdown--has-parent:after, .o_menu_item.o-dropdown-caret:not(.o-dropdown-no-caret).o-dropdown--has-parent:after{transform: rotate(-90deg);}.o-dropdown.o-dropdown-caret:not(.o-dropdown-no-caret).o-dropdown--has-parent.o-dropdown--open:after, .o-dropdopwn-item.o-dropdown-caret:not(.o-dropdown-no-caret).o-dropdown--has-parent.o-dropdown--open:after, .o_menu_item.o-dropdown-caret:not(.o-dropdown-no-caret).o-dropdown--has-parent.o-dropdown--open:after{transform: rotate(90deg);}.o-dropdown--menu{font-size: 0.875rem; margin-top: var(--Dropdown_menu-margin-y, 4px); margin-bottom: var(--Dropdown_menu-margin-y, 4px);}.o-dropdown--menu .dropdown-toggle:focus, .o-dropdown--menu .dropdown-item:focus{background-color: transparent; outline: none;}.o-dropdown--menu .dropdown-toggle.focus, .o-dropdown--menu .dropdown-item.focus{background-color: rgba(0, 0, 0, 0.08);}.o-dropdown--menu.o-dropdown--menu-submenu{--o-dropdown--submenu-margin: calc(-.5rem - 1px); margin-top: var(--o-dropdown--submenu-margin); margin-bottom: var(--o-dropdown--submenu-margin);}.o-dropdown--menu .dropdown-item:not(.disabled):not(:disabled), .o-dropdown--menu .dropdown-item:not(.disabled):not(:disabled) label{cursor: pointer;}.o-dropdown-item-unstyled-button button, .o-dropdown-item-unstyled-button button:hover, .o-dropdown-item-unstyled-button button:disabled, .o_web_client.o_touch_device .o-dropdown-item-unstyled-button button, .o_web_client.o_touch_device .o-dropdown-item-unstyled-button button:hover, .o_web_client.o_touch_device .o-dropdown-item-unstyled-button button:disabled{all: unset; display: inline-block; width: 100%;}

/* /web/static/src/core/dropzone/dropzone.scss */
 .o-Dropzone{border: 2px dashed; z-index: 1000;}.o-Dropzone.o-dragging-inside{border-width: 5px;}

/* /web/static/src/core/effects/rainbow_man.scss */
 .o_reward{will-change: transform; z-index: 1056; animation: reward-fading 0.7s ease-in-out forwards;}.o_reward .o_reward_box{transform-box: fill-box;}.o_reward.o_reward_fading{animation: reward-fading-reverse 0.56s ease-in-out forwards;}.o_reward.o_reward_fading .o_reward_face_group{animation: reward-jump-reverse 0.56s ease-in-out forwards;}.o_reward.o_reward_fading .o_reward_rainbow_line{animation: reward-rainbow-reverse 0.7s ease-out forwards;}.o_reward .o_reward_rainbow_man{max-width: 400px;}.o_reward .o_reward_rainbow_line{animation: reward-rainbow 1.12s ease-out 1 forwards;}.o_reward .o_reward_face_group{animation: reward-jump 1.12s ease-in-out 1;}.o_reward .o_reward_face_wrap{animation: reward-rotate 1.12s cubic-bezier(0.51, 0.92, 0.24, 1.15) 1;}.o_reward .o_reward_face{animation: reward-float 1.4s ease-in-out 1.4s infinite alternate;}.o_reward .o_reward_star_01, .o_reward .o_reward_star_03{animation: reward-stars 1.4s ease-in-out infinite alternate-reverse;}.o_reward .o_reward_star_02, .o_reward .o_reward_star_04{animation: reward-stars 1.68s ease-in-out infinite alternate;}.o_reward .o_reward_thumbup{animation: reward-scale 0.7s ease-in-out 0s infinite alternate;}.o_reward .o_reward_shadow_container{animation: reward-float 1.4s ease-in-out infinite alternate;}.o_reward .o_reward_shadow{animation: reward-scale 1.4s ease-in-out infinite alternate;}.o_reward .o_reward_msg_container{aspect-ratio: 1 / 1; animation: reward-float-reverse 1.4s ease-in-out infinite alternate-reverse;}@keyframes reward-fading{0%{opacity: 0;}}@keyframes reward-fading-reverse{100%{opacity: 0;}}@keyframes reward-jump{0%{transform: scale(0.5);}50%{transform: scale(1.05);}}@keyframes reward-jump-reverse{50%{transform: scale(1.05);}to{transform: scale(0.5);}}@keyframes reward-rainbow{to{stroke-dashoffset: 0;}}@keyframes reward-rainbow-reverse{from{stroke-dashoffset: 0;}}@keyframes reward-float{to{transform: translateY(5px);}}@keyframes reward-float-reverse{from{transform: translateY(5px);}}@keyframes reward-stars{from{transform: scale(0.3) rotate(0deg);}50%{transform: scale(1) rotate(20deg);}to{transform: scale(0.3) rotate(80deg);}}@keyframes reward-scale{from{transform: scale(0.8);}}@keyframes reward-rotate{from{transform: scale(0.5) rotate(-30deg);}}

/* /web/static/src/core/emoji_picker/emoji_picker.dark.scss */
 .o-EmojiPicker{--EmojiPicker-placeholderOpacity: 75%; --EmojiPicker-active: #dadfe3;}

/* /web/static/src/core/emoji_picker/emoji_picker.scss */
 .popover .o-EmojiPicker{width: 300px; height: 365px;}.o-EmojiPicker-content{padding-left: 6px; padding-right: 6px;}.o-EmojiPicker{--EmojiPicker-active: rgba(113, 99, 158, 0.15);}.o-EmojiPicker .o-Emoji{padding-left: 4px; padding-right: 4px; font-size: 0.8rem; aspect-ratio: 1;}.o-EmojiPicker .o-Emoji:hover{background-color: var(--EmojiPicker-active) !important;}.o-EmojiPicker .o-Emoji.o-active{background-color: var(--EmojiPicker-active) !important;}.o-EmojiPicker .o-EmojiPicker-navbar{--border-opacity: .5; padding-top: 2px; padding-bottom: 2px;}.o-EmojiPicker .o-EmojiPicker-navbar .o-Emoji > span{filter: grayscale(1);}.o-EmojiPicker .o-EmojiPicker-navbar .o-Emoji:not(.o-active) > span{opacity: 50%;}.o-EmojiPicker .o-EmojiPicker-sectionIcon{filter: grayscale(1);}.o-EmojiPicker .o-EmojiPicker-empty{font-size: 5rem !important; filter: grayscale(0.25);}.o-EmojiPicker-category:before{content: "\200b";}.o-EmojiPicker-search input::placeholder{opacity: var(--EmojiPicker-placeholderOpacity, 50%);}.o-EmojiPicker-search input:not(:focus) + .oi-search{color: rgba(73, 80, 87, 0.76);}

/* /web/static/src/core/errors/error_dialog.scss */
 @media (min-width: 768px){.o_error_dialog{padding: 1rem;}.o_error_dialog .o_error_detail > div pre{max-height: 40vh;}}.o_error_dialog .o_error_detail > div{overflow: auto;}.o_error_dialog .o_error_detail > div pre{border: none; overflow: unset;}.o_error_dialog .o_error_detail button{top: 10px; right: 15px; opacity: 0.66;}.o_error_dialog .o_error_detail button:hover{opacity: 1;}.o_error_dialog .modal-header{border: none; padding-top: 0.5rem; padding-bottom: 0.5rem;}.o_error_dialog .modal-header .modal-title{font-size: 1.625rem;}.o_error_dialog .modal-body{padding-top: 0;}.o_error_dialog .modal-footer{border: none; padding-top: 0;}

/* /web/static/src/core/file_upload/file_upload_progress_bar.scss */
 .o-file-upload-progress-bar-value{transition: width 0.1s; border-right: 1px solid #605487; background-color: #71639e; opacity: 0.5;}.o-file-upload-progress-bar-abort{padding: 4px; color: #963535; font-size: 16px;}.o-file-upload-progress-bar-abort:active{opacity: 0.7;}div:not(:hover) .o-file-upload-progress-bar-abort{display: none;}

/* /web/static/src/core/file_upload/file_upload_progress_record.scss */
 .o_kanban_record .o_kanban_progress_card{min-height: 80px;}.o_kanban_record .o_kanban_progress_card .o_kanban_record_bottom{color: #212529;}.o_kanban_record .o_kanban_progress_card .o_kanban_image_wrapper{opacity: 0.7;}.o_data_row.o_list_progress_card{height: 25px; border: 1px solid #dfdfdf;}.o_data_row.o_list_progress_card .o_file_upload_upload_title{color: #212529; font-size: 13px; font-weight: 500;}

/* /web/static/src/core/file_viewer/file_viewer.dark.scss */
 .o-FileViewer-header{--FileViewer-toolbarBgColor: #e9ecef;}.o-FileViewer-toolbarButton{--FileViewer-toolbarBgColor: #e9ecef;}

/* /web/static/src/core/file_viewer/file_viewer.scss */
 .o-FileViewer{z-index: -1; outline: none;}.o-FileViewer-navigation{width: 40px; height: 40px;}.o-FileViewer-header{color: #fff; background-color: var(--FileViewer-toolbarBgColor, #343a40); height: 46px;}.o-FileViewer-main{z-index: -1; padding: 51.75px 0;}.o-FileViewer-zoomer{padding: 51.75px 0;}.o-FileViewer-headerButton:hover{background-color: rgba(255, 255, 255, 0.1); color: #fafafb;}.o-FileViewer-toolbarButton{background-color: var(--FileViewer-toolbarBgColor, #343a40); color: #fff;}.o-FileViewer-toolbarButton:hover{filter: brightness(1.3);}.o-FileViewer-view{background-color: #000000; box-shadow: 0 0 40px #000000; outline: none;}.o-FileViewer-view.o-isText{background: #FFFFFF;}

/* /web/static/src/core/model_field_selector/model_field_selector.scss */
 .o_model_field_selector{position: relative;}.o_model_field_selector.o_edit_mode{cursor: pointer;}.o_model_field_selector > .o_model_field_selector_value{min-height: 20px; max-width: 100%; word-wrap: break-word;}.o_model_field_selector > .o_model_field_selector_value:active, .o_model_field_selector > .o_model_field_selector_value:focus, .o_model_field_selector > .o_model_field_selector_value:active:focus{outline: none;}.o_model_field_selector > .o_model_field_selector_value > .o_model_field_selector_chain_part{cursor: inherit; border: 1px solid #dae0e5; background: #f8f9fa;}.o_model_field_selector > .o_model_field_selector_value > i{font-size: 10px;}

/* /web/static/src/core/model_field_selector/model_field_selector_popover.scss */
 .o_model_field_selector_popover{width: 17rem;}.o_model_field_selector_popover .o_model_field_selector_popover_body .o_model_field_selector_popover_page{height: 20rem;}.o_model_field_selector_popover .o_model_field_selector_popover_body .o_model_field_selector_popover_page > .o_model_field_selector_popover_item{background: var(--body-bg);}.o_model_field_selector_popover .o_model_field_selector_popover_body .o_model_field_selector_popover_page > .o_model_field_selector_popover_item.active button{background: rgba(0, 0, 0, 0.08);}

/* /web/static/src/core/model_selector/model_selector.scss */
 .o_model_selector .o-autocomplete--dropdown-menu{width: 25ch; max-height: 350px !important;}.o_model_selector .o-autocomplete--dropdown-menu .o-autocomplete--dropdown-item a{text-overflow: ellipsis; width: inherit;}

/* /web/static/src/core/notebook/notebook.scss */
 .o_notebook{--notebook-margin-x: 0; --notebook-padding-x: 0; --notebook-link-border-color: transparent; --notebook-link-border-color-active: #dee2e6; --notebook-link-border-color-hover: #e9ecef; --notebook-link-border-color-active-accent: #dee2e6;}.o_notebook .o_notebook_headers{margin: 0 var(--notebook-margin-x, 0); overflow-x: auto;}@media (max-width: 767.98px){.o_notebook .o_notebook_headers::-webkit-scrollbar{display: none;}}.o_notebook .nav{padding: 0 var(--notebook-padding-x, 0); background-color: white;}.o_notebook .nav-item{white-space: nowrap; margin: 0 -1px 0 0;}.o_notebook .nav-item.disabled .nav-link{cursor: not-allowed; opacity: .3;}.o_notebook .nav-link{border-color: var(--notebook-link-border-color, transparent);}.o_notebook .nav-link.active, .o_notebook .nav-link.active:hover, .o_notebook .nav-link.active:focus, .o_notebook .nav-link.active:active{border-color: var(--notebook-link-border-color-active); border-top-color: var(--notebook-link-border-color-active-accent, var(--notebook-link-border-color-active)); border-bottom-color: white;}.o_notebook .nav-link:hover, .o_notebook .nav-link:focus, .o_notebook .nav-link:active{outline: none;}.o_notebook .nav-link:hover{border-color: var(--notebook-link-border-color-hover);}.o_notebook .tab-pane:not(.show){transition: none;}.o_notebook.vertical .o_notebook_headers{overflow-x: visible;}.o_notebook.vertical .nav{width: max-content; border-bottom-color: transparent;}.o_notebook.vertical .nav-item{margin: 0 0 -1px 0;}.o_notebook.vertical .nav-item:first-child .nav-link{border-top-width: 0;}.o_notebook.vertical .nav-link{margin-bottom: 0;}.o_notebook.vertical .nav-link.active, .o_notebook.vertical .nav-link.active:hover, .o_notebook.vertical .nav-link.active:focus, .o_notebook.vertical .nav-link.active:active{border-color: var(--notebook-link-border-color-active); border-left-color: var(--notebook-link-border-color-active-accent, var(--notebook-link-border-color-active)); border-right-color: white;}@media (max-width: 991.98px){.o_notebook .o_notebook_content .oe-toolbar{position: -webkit-sticky; position: sticky; top: 0px; left: auto; bottom: auto; right: auto; margin-left: var(--notebook-margin-x, 16px); margin-right: var(--notebook-margin-x, 16px); width: auto;}}

/* /web/static/src/core/notifications/notification.scss */
 .o_notification_manager{position: fixed; inset: 52.9px 1rem auto 1rem; z-index: 1055;}@media (min-width: 576px){.o_notification_manager{left: auto; width: 400px;}}.o_notification_manager .o_notification{background-color: var(--Notification__background-color, white);}.o_notification_manager .o_notification_bar{width: 0.5rem;}.o_notification_fade{transition: all 0.5s;}.o_notification_fade-enter{opacity: 0;}

/* /web/static/src/core/overlay/overlay_container.scss */
 .o-overlay-item{position: fixed; z-index: 1055;}

/* /web/static/src/core/pager/pager_indicator.scss */
 .o_pager_indicator{z-index: 1056; transition: opacity 0.4s;}.o_pager_indicator > .o_pager_indicator_inner{min-width: 13ch; padding: 7px 14px; border: var(--border-width) solid #e9ecef; border-radius: var(--border-radius); background-color: #e9ecef;}.o_pager_indicator.o-fade-leave, .o_pager_indicator.o-fade-enter{opacity: 0;}

/* /web/static/src/core/popover/popover.scss */
 .o_popover.o-popover--with-arrow{margin: 8px;}.o_popover .popover-arrow{position: absolute;}.o_popover.o-popover--ts .popover-arrow, .o_popover.o-popover--bs .popover-arrow{left: 0.25rem;}.o_popover.o-popover--te .popover-arrow, .o_popover.o-popover--be .popover-arrow{right: 0.25rem;}.o_popover.o-popover--ls .popover-arrow, .o_popover.o-popover--rs .popover-arrow{top: 0.25rem;}.o_popover.o-popover--le .popover-arrow, .o_popover.o-popover--re .popover-arrow{bottom: 0.25rem;}

/* /web/static/src/core/pwa/install_prompt.scss */
 .o_install_prompt{border-radius: 20px; height: unset !important; -webkit-backdrop-filter: blur(5px); backdrop-filter: blur(5px); background: rgba(255, 255, 255, 0.7); inset: 0 auto auto 0 !important; width: fit-content !important;}.o_install_prompt.o_touch_bounce{animation: none;}@media screen and (max-width: 768px){.o_install_prompt{inset: auto auto 0 0 !important; width: 90% !important; margin: 5% !important;}.o_install_prompt .modal-header{background: none !important; border: none !important;}.o_install_prompt .modal-header button{color: #000000 !important;}}

/* /web/static/src/core/record_selectors/record_selectors.scss */
 .o_record_selector:hover .o_dropdown_button:after, .o_record_selector:focus-within .o_dropdown_button:after, .o_multi_record_selector:hover .o_dropdown_button:after, .o_multi_record_selector:focus-within .o_dropdown_button:after{content: ""; display: inline-block; width: 0; height: 0; vertical-align: middle; -moz-transform: scale(0.9999); border-bottom: 0; border-left: 4px solid transparent; border-right: 4px solid transparent; border-top: 4px solid var(--o-caret-color, currentColor);}.o_record_selector .o_record_autocomplete_with_caret, .o_multi_record_selector .o_record_autocomplete_with_caret{display: -webkit-box; display: -webkit-flex; display: flex; min-width: 100%;}.o_record_selector .o_record_autocomplete_with_caret:hover::after, .o_record_selector .o_record_autocomplete_with_caret:focus-within::after, .o_multi_record_selector .o_record_autocomplete_with_caret:hover::after, .o_multi_record_selector .o_record_autocomplete_with_caret:focus-within::after{content: ""; display: inline-block; width: 0; height: 0; vertical-align: middle; -moz-transform: scale(0.9999); border-bottom: 0; border-left: 4px solid transparent; border-right: 4px solid transparent; border-top: 4px solid var(--o-caret-color, currentColor); align-self: center;}

/* /web/static/src/core/resizable_panel/resizable_panel.scss */
 .o_resizable_panel{max-width: 100vw; flex-grow: 0;}.o_resizable_panel_handle{cursor: col-resize; z-index: 10; width: 5px;}

/* /web/static/src/core/select_menu/select_menu.scss */
 .o_select_menu .o_select_menu_toggler{display: grid; grid-template-columns: auto 25px;}.o_select_menu .o_select_menu_toggler.o_can_deselect{grid-template-columns: auto 25px 25px;}.o_select_menu .o_select_menu_toggler_slot{flex-grow: 2;}.o_select_menu .o_select_menu_toggler_caret{grid-column: 2;}.o_select_menu .o_can_deselect .o_select_menu_toggler_caret{grid-column: 3;}.o_select_menu .o_select_menu_toggler_clear{grid-column: 2;}.o_select_menu .o_select_menu_toggler_clear:hover i{color: red;}.o_select_menu .o_tag{margin: 2px;}.o_select_menu--sticky{position: sticky;}.o_select_menu_menu{min-width: fit-content; max-height: 350px !important;}.o_select_menu_menu input{cursor: text !important;}.o_select_menu_menu .o_select_menu_sticky{background-color: var(--body-bg) !important;}.o_select_menu_menu .o_select_menu_sticky.o_select_menu_item.focus{background: #dee2e6 !important;}.o_select_menu_menu .o_select_menu_group{top: 40px !important;}.o_select_menu_menu .o_select_menu_group:not(.o_select_menu_searchable_group){top: -4px;}.o_select_menu_menu .o_select_active{color: white;}.o_select_menu_menu.o_select_menu_multi_select .o_select_active:hover{background: #dc3545 !important; transition: background .5s;}.dropup .o_select_menu_menu{box-shadow: 0 -7px 10px rgba(8, 8, 8, 0.319);}.dropdown .o_select_menu_menu{box-shadow: 0 7px 10px rgba(8, 8, 8, 0.319);}

/* /web/static/src/core/signature/name_and_signature.scss */
 .o_web_sign_name_and_signature{position: relative;}.o_web_sign_signature_container canvas{width: 100%; height: 100%;}.o_signature_stroke{position: absolute; border-top: #D1D0CE solid 2px; bottom: 20%; width: 72%; left: 14%;}

/* /web/static/src/core/tags_list/tags_list.scss */
 .o_tag{font-size: var(--Tag-font-size, 0.8125rem); max-width: var(--Tag-max-width, 100%);}.o_tag.o_tag_color_0, .o_tag.o_tag_color_0::after{--background-color: RGBA(230.1375, 221.3625, 221.3625, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(60, 60, 60, var(--text-opacity, 1)); color: var(--color) !important;}.o_tag.o_tag_color_1, .o_tag.o_tag_color_1::after{--background-color: RGBA(255, 155.5, 155.5, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(67.15870044, 11.84129956, 11.84129956, var(--text-opacity, 1)); color: var(--color) !important;}.o_tag.o_tag_color_2, .o_tag.o_tag_color_2::after{--background-color: RGBA(247.0375, 198.06116071, 152.4625, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(52.9, 33.325, 15.1, var(--text-opacity, 1)); color: var(--color) !important;}.o_tag.o_tag_color_3, .o_tag.o_tag_color_3::after{--background-color: RGBA(252.88960843, 226.89175248, 135.61039157, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(47.45993976, 39.05405514, 9.54006024, var(--text-opacity, 1)); color: var(--color) !important;}.o_tag.o_tag_color_4, .o_tag.o_tag_color_4::after{--background-color: RGBA(187.45210396, 215.03675558, 248.04789604, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(25.3049505, 49.60939855, 78.6950495, var(--text-opacity, 1)); color: var(--color) !important;}.o_tag.o_tag_color_5, .o_tag.o_tag_color_5::after{--background-color: RGBA(216.79194664, 167.70805336, 203.91748283, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(28.91432806, 24.08567194, 27.64779531, var(--text-opacity, 1)); color: var(--color) !important;}.o_tag.o_tag_color_6, .o_tag.o_tag_color_6::after{--background-color: RGBA(247.84539474, 213.9484835, 199.65460526, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(85.32105263, 46.88635147, 30.67894737, var(--text-opacity, 1)); color: var(--color) !important;}.o_tag.o_tag_color_7, .o_tag.o_tag_color_7::after{--background-color: RGBA(136.6125, 224.8875, 218.94591346, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(10.58333333, 19.41666667, 18.82211538, var(--text-opacity, 1)); color: var(--color) !important;}.o_tag.o_tag_color_8, .o_tag.o_tag_color_8::after{--background-color: RGBA(150.60535714, 165.68382711, 248.89464286, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(13.95714286, 20.10665584, 54.04285714, var(--text-opacity, 1)); color: var(--color) !important;}.o_tag.o_tag_color_9, .o_tag.o_tag_color_9::after{--background-color: RGBA(254.94583333, 157.55416667, 203.95543194, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(68.805, 12.195, 39.16625654, var(--text-opacity, 1)); color: var(--color) !important;}.o_tag.o_tag_color_10, .o_tag.o_tag_color_10::after{--background-color: RGBA(182.62075688, 236.87924312, 189.81831118, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(30.82018349, 57.17981651, 34.3168695, var(--text-opacity, 1)); color: var(--color) !important;}.o_tag.o_tag_color_11, .o_tag.o_tag_color_11::after{--background-color: RGBA(230.11575613, 219.41069277, 252.08930723, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(56.75321978, 31.58433735, 108.41566265, var(--text-opacity, 1)); color: var(--color) !important;}

/* /web/static/src/core/tooltip/tooltip.scss */
 .o-tooltip{--tooltip-max-width: 400px; --tooltip-padding-x: 8px; --tooltip-padding-y: 4px; --tooltip-color: #e9ecef; --tooltip-bg: var(--emphasis-color); --tooltip-border-radius: var(--border-radius); font-family: var(--font-sans-serif); font-style: normal; font-weight: 400; line-height: 1.5; text-align: left; text-align: start; text-decoration: none; text-shadow: none; text-transform: none; letter-spacing: normal; word-break: normal; white-space: normal; word-spacing: normal; line-break: auto; font-size: 0.8125rem;}.o-tooltip:has(.o-tooltip--technical){max-width: 800px;}.o-tooltip:has(.o-tooltip--technical) .o-tooltip--help{background-color: #17a2b8; color: #FFFFFF;}.o-tooltip:has(.o-tooltip--technical) .o-tooltip--string, .o-tooltip:has(.o-tooltip--technical) .o-tooltip--help{max-width: MIN(400px, 100%);}.o-tooltip .o-tooltip--string, .o-tooltip .o-tooltip--technical--title{padding: 4px 0; font-weight: 500;}.o-tooltip .o-tooltip--help, .o-tooltip .o-tooltip--technical{margin: 4px 8px 8px;}.o-tooltip .o-tooltip--help{white-space: pre-line; padding: 0 8px;}.o-tooltip .o-tooltip--technical{padding-left: 1.3em; font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; font-size: 0.75rem; list-style-type: disc;}.o-tooltip .o-tooltip--technical .o-tooltip--technical--title{margin-right: 4px;}.o-tooltip + .popover-arrow{--popover-bg: var(--emphasis-color);}

/* /web/static/src/core/tree_editor/tree_editor.scss */
 .o_tree_editor .o_tree_editor_node .o_tree_editor_node_control_panel > button{opacity: .2;}.o_tree_editor .o_tree_editor_node.o_hovered_button .o_tree_editor_node_control_panel > button{opacity: .5;}.o_tree_editor .o_tree_editor_node.o_hovered_button .o_tree_editor_node_control_panel > button:hover{opacity: 1;}

/* /web/static/src/core/ui/block_ui.scss */
 .o_blockUI{cursor: wait; z-index: 1070 !important;}.o_blockUI:not(.o_blockUI_invisible){-webkit-backdrop-filter: blur(2px); backdrop-filter: blur(2px); background: rgba(0, 0, 0, 0.5); color: #fff;}

/* /web/static/src/core/utils/draggable_hook_builder.scss */
 @keyframes o-draggable-bounce{0%{transform: scale(1);}60%{transform: scale(0.95);}100%{transform: scale(1);}}.o_draggable{-webkit-touch-callout: none;}.o_dragged{z-index: 1000; pointer-events: none;}.o_touch_bounce{animation: o-draggable-bounce .4s forwards; user-select: none;}

/* /web/static/src/core/utils/nested_sortable.scss */
 .o_nested_sortable_placeholder{background-clip: content-box; background-color: deepskyblue; height: 5px; padding-top: 0 !important; padding-bottom: 0 !important;}.o_nested_sortable_placeholder_realsize{outline: 1px dashed #6c757d; background-color: #f8f9fa;}

/* /web/static/src/scss/mimetypes.scss */
 .o_image{display: inline-block; width: 38px; height: 38px; background-image: url("/web/static/img/mimetypes/unknown.svg"); background-size: contain; background-repeat: no-repeat; background-position: center;}.o_image[data-mimetype^='image']{background-image: url("/web/static/img/mimetypes/image.svg");}.o_image[data-mimetype^='audio']{background-image: url("/web/static/img/mimetypes/audio.svg");}.o_image[data-mimetype^='text'], .o_image[data-mimetype$='rtf']{background-image: url("/web/static/img/mimetypes/text.svg");}.o_image[data-mimetype*='octet-stream'], .o_image[data-mimetype*='download'], .o_image[data-mimetype*='python']{background-image: url("/web/static/img/mimetypes/binary.svg");}.o_image[data-mimetype^='video'], .o_image[title$='.mp4'], .o_image[title$='.avi']{background-image: url("/web/static/img/mimetypes/video.svg");}.o_image[data-mimetype$='archive'], .o_image[data-mimetype$='compressed'], .o_image[data-mimetype*='zip'], .o_image[data-mimetype$='tar'], .o_image[data-mimetype*='package']{background-image: url("/web/static/img/mimetypes/archive.svg");}.o_image[data-mimetype^='application/pdf']{background-image: url("/web/static/img/mimetypes/pdf.svg");}.o_image[data-mimetype^='text-master'], .o_image[data-mimetype*='document'], .o_image[data-mimetype*='msword'], .o_image[data-mimetype*='wordprocessing']{background-image: url("/web/static/img/mimetypes/document.svg");}.o_image[data-mimetype*='application/xml'], .o_image[data-mimetype$='html']{background-image: url("/web/static/img/mimetypes/web_code.svg");}.o_image[data-mimetype$='css'], .o_image[data-mimetype$='less'], .o_image[data-ext$='less']{background-image: url("/web/static/img/mimetypes/web_style.svg");}.o_image[data-mimetype*='-image'], .o_image[data-mimetype*='diskimage'], .o_image[data-ext$='dmg']{background-image: url("/web/static/img/mimetypes/disk.svg");}.o_image[data-mimetype$='csv'], .o_image[data-mimetype*='vc'], .o_image[data-mimetype*='excel'], .o_image[data-mimetype$='numbers'], .o_image[data-mimetype$='calc'], .o_image[data-mimetype*='mods'], .o_image[data-mimetype*='spreadsheet']{background-image: url("/web/static/img/mimetypes/spreadsheet.svg");}.o_image[data-mimetype^='key']{background-image: url("/web/static/img/mimetypes/certificate.svg");}.o_image[data-mimetype*='presentation'], .o_image[data-mimetype*='keynote'], .o_image[data-mimetype*='teacher'], .o_image[data-mimetype*='slideshow'], .o_image[data-mimetype*='powerpoint']{background-image: url("/web/static/img/mimetypes/presentation.svg");}.o_image[data-mimetype*='cert'], .o_image[data-mimetype*='rules'], .o_image[data-mimetype*='pkcs'], .o_image[data-mimetype$='stl'], .o_image[data-mimetype$='crl']{background-image: url("/web/static/img/mimetypes/certificate.svg");}.o_image[data-mimetype*='-font'], .o_image[data-mimetype*='font-'], .o_image[data-ext$='ttf']{background-image: url("/web/static/img/mimetypes/font.svg");}.o_image[data-mimetype*='-dvi']{background-image: url("/web/static/img/mimetypes/print.svg");}.o_image[data-mimetype*='script'], .o_image[data-mimetype*='x-sh'], .o_image[data-ext*='bat'], .o_image[data-mimetype$='bat'], .o_image[data-mimetype$='cgi'], .o_image[data-mimetype$='-c'], .o_image[data-mimetype*='java'], .o_image[data-mimetype*='ruby']{background-image: url("/web/static/img/mimetypes/script.svg");}.o_image[data-mimetype*='javascript']{background-image: url("/web/static/img/mimetypes/javascript.svg");}.o_image[data-mimetype*='calendar'], .o_image[data-mimetype$='ldif']{background-image: url("/web/static/img/mimetypes/calendar.svg");}.o_image[data-mimetype$='postscript'], .o_image[data-mimetype$='cdr'], .o_image[data-mimetype$='xara'], .o_image[data-mimetype$='cgm'], .o_image[data-mimetype$='graphics'], .o_image[data-mimetype$='draw'], .o_image[data-mimetype*='svg']{background-image: url("/web/static/img/mimetypes/vector.svg");}

/* /mail/static/src/core/common/attachment_list.scss */
 @media (hover: hover){.o-mail-AttachmentCard-unlink.o-inComposer{transform: translateX(100%);}.o-mail-AttachmentCard-aside:hover .o-mail-AttachmentCard-unlink.o-inComposer{transform: translateX(0);}}.o-mail-AttachmentCard-aside:not(.o-hasMultipleActions){min-width: 50px;}.o-mail-AttachmentCard-aside.o-hasMultipleActions{min-width: 30px;}.o-mail-AttachmentList-in-composer{max-height: 300px;}.o-mail-AttachmentList-in-composer.o-inChatWindow{max-height: 100px;}.o-mail-AttachmentImage{min-width: 75px; min-height: 75px; background-color: #e9ecef;}.o-mail-AttachmentImage img{object-fit: contain;}.o-viewable{cursor: zoom-in;}

/* /mail/static/src/core/common/attachment_view.scss */
 @media (min-width: 1534px){.o_attachment_preview{display: block; flex: auto; overflow: hidden; width: 530px;}.o_attachment_preview > .o-mail-Attachment{position: relative; width: 100%; height: 100%;}.o_attachment_preview > .o-mail-Attachment .arrow{width: 5%; position: absolute; top: 50%; left: auto; bottom: auto; right: 0; background-color: rgba(0, 0, 0, 0.4); color: rgba(248, 249, 250, 0.7); transition: width 0.3s; padding-top: 30px; height: 75px;}.o_attachment_preview > .o-mail-Attachment .arrow:hover{background-color: rgba(0, 0, 0, 0.6); color: white;}.o_attachment_preview > .o-mail-Attachment .arrow.disabled{color: #6c757d; background: none;}.o_attachment_preview > .o-mail-Attachment .arrow:hover{width: 7%;}.o_attachment_preview > .o-mail-Attachment .arrow.o_move_previous{left: 2px; right: 0px;}.o_attachment_preview > .o-mail-Attachment > iframe{width: 100%; height: 100%;}.o_attachment_preview > .o-mail-Attachment > .o-mail-Attachment-imgContainer{position: absolute; overflow: auto; width: 100%; height: 100%;}.o_attachment_preview > .o-mail-Attachment > .o-mail-Attachment-imgContainer > img{margin: var(--o-Mail-Attachment-img-margin, auto); box-shadow: 0px 0px 5px rgba(41, 41, 41, 0.43);}.o_attachment_control{position: absolute; top: 8%; background-color: black; opacity: 0.3; margin-top: -15px; transition: all 0.3s; z-index: 1000;}.o_attachment_control:hover{opacity: 0.7;}.o_attachment_control.popout{right: 0px; border-radius: 30px 0 0 30px; padding: 15px 0 15px 15px;}.o_attachment_control.popout:hover{padding-right: 15px;}}.o-mail-PopoutAttachmentView{width: auto;}.o-mail-PopoutAttachmentView > .o-mail-Attachment{position: relative; width: 100%; height: 100%;}.o-mail-PopoutAttachmentView > .o-mail-Attachment .arrow{width: 5%; position: absolute; top: 50%; left: auto; bottom: auto; right: 0; background-color: rgba(0, 0, 0, 0.4); color: rgba(248, 249, 250, 0.7); transition: width 0.3s; padding-top: 30px; height: 75px;}.o-mail-PopoutAttachmentView > .o-mail-Attachment .arrow:hover{background-color: rgba(0, 0, 0, 0.6); color: white;}.o-mail-PopoutAttachmentView > .o-mail-Attachment .arrow.disabled{color: #6c757d; background: none;}.o-mail-PopoutAttachmentView > .o-mail-Attachment .arrow:hover{width: 7%;}.o-mail-PopoutAttachmentView > .o-mail-Attachment .arrow.o_move_previous{left: 2px; right: 0px;}.o-mail-PopoutAttachmentView > .o-mail-Attachment > iframe{width: 100%; height: 100%;}.o-mail-PopoutAttachmentView > .o-mail-Attachment > .o-mail-Attachment-imgContainer{position: absolute; overflow: auto; width: 100%; height: 100%;}.o-mail-PopoutAttachmentView > .o-mail-Attachment > .o-mail-Attachment-imgContainer > img{margin: var(--o-Mail-Attachment-img-margin, auto); box-shadow: 0px 0px 5px rgba(41, 41, 41, 0.43);}.o-mail-PopoutAttachmentView .o_attachment_control{display: none;}

/* /mail/static/src/core/common/autoresize_input.scss */
 .o-mail-AutoresizeInput{--o-input-border-color: transparent; border: var(--border-width) solid var(--o-input-border-color); background-color: var(--o-input-background-color, transparent);}.o-mail-AutoresizeInput::placeholder{opacity: 50%;}.o-mail-AutoresizeInput:not([disabled]):hover{--o-input-border-color: #dee2e6;}.o-mail-AutoresizeInput:not([disabled]):focus{--o-input-border-color: #000000;}

/* /mail/static/src/core/common/chat_bubble.scss */
 .o-mail-ChatBubble{height: auto !important; z-index: 1001; border: none !important; padding: 0 8px; background-color: transparent !important;}.o-mail-ChatBubble:hover .o-mail-ChatBubble-close{display: -webkit-inline-box; display: -webkit-inline-flex; display: inline-flex;}.o-mail-ChatBubble:hover .o-mail-ChatBubble-counter{display: none;}.o-mail-ChatBubble:hover, .o-mail-ChatBubble.o-active{filter: brightness(1.1);}.o-mail-ChatBubble.o-bouncing{animation: o-mail-ChatBubble-bouncing 0.25s; animation-direction: alternate; animation-timing-function: cubic-bezier(0.5, 0.95, 0, 0.5); animation-iteration-count: 16;}.o-mail-ChatBubble-avatar{width: 45px; height: 45px;}.o-mail-ChatBubble-avatar.o-big{width: 50px; height: 50px;}.o-mail-ChatBubble-close{right: 3px; top: -3px; z-index: 6; font-size: 11px; display: none; padding: 2px; border: 1px solid #d6dbe0;}.o-mail-ChatBubble-close:not(:hover){color: rgba(73, 80, 87, 0.76);}.o-mail-ChatBubble-close:hover{background-color: #dee2e6 !important; border-color: #adb5bd;}.o-mail-ChatBubble-country{width: 16px; left: 3px;}.o-mail-ChatBubble-counter{z-index: 7; top: -3px; right: 3px; display: -webkit-inline-box; display: -webkit-inline-flex; display: inline-flex;}.o-mail-ChatBubble-preview{max-width: 225px; right: 56px; z-index: 1000; border-color: #d6dbe0 !important;}.o-mail-ChatBubble-preview + .popover-arrow::before{border-left-color: #d6dbe0 !important; right: 1px !important;}.o-mail-ChatBubble-preview + .popover-arrow::after{border-left-color: #f8f9fa !important; right: 2px !important;}.o-mail-ChatBubble-status{z-index: 6; bottom: -2px; right: 2px; background-color: transparent;}.o-mail-ChatBubble-unreadIndicator{font-size: .5rem; bottom: 40%; right: -2px;}@keyframes o-mail-ChatBubble-bouncing{from{transform: translate3d(0, 0, 0);}to{transform: translate3d(0, -10px, 0);}}

/* /mail/static/src/core/common/chat_hub.scss */
 .o-mail-ChatHub-bubbles{width: 56px; margin-top: 10px; margin-right: 25px; margin-bottom: 10px; margin-left: 10px; z-index: 20;}.o-mail-ChatHub-bubbles.o-liftUp{transform: translateY(-25px);}.o-mail-ChatHub-bubbleBtn{padding: 0 !important; border: none !important; border-radius: 50%; box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);}.o-mail-ChatHub-hiddenAvatar{width: 28px; height: 28px;}.o-mail-ChatHub-hiddenBtnIcon{color: #FFFFFF !important; background-color: #71639e !important; width: 50px !important; height: 50px !important;}.o-mail-ChatHub-hiddenBtnCounter{padding: 4px 8px; z-index: 20; top: -4px; right: -4px;}.o-mail-ChatHub-hiddenClose{margin-left: auto; background-color: transparent; opacity: 25%; padding: 2px; border: 1px solid transparent;}.o-mail-ChatHub-hiddenClose:hover{border-color: #d6dbe0;}.o-mail-ChatHub-hiddenCounter{right: 20px;}.o-mail-ChatHub-hiddenItem{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; max-width: 225px;}.o-mail-ChatHub-hiddenItem:hover:not(:has(.o-mail-ChatHub-hiddenClose:hover)), .o-mail-ChatHub-hiddenItem.o-active{background-color: #dee2e6;}.o-mail-ChatHub-hiddenItem:hover, .o-mail-ChatHub-hiddenItem.o-active{cursor: pointer;}.o-mail-ChatHub-hiddenItem:hover .o-mail-ChatHub-hiddenClose, .o-mail-ChatHub-hiddenItem.o-active .o-mail-ChatHub-hiddenClose{opacity: 100%;}.o-mail-ChatHub-hiddenMenu{border-color: #d6dbe0 !important;}.o-mail-ChatHub-option:hover, .o-mail-ChatHub-option.o-active{background-color: #dee2e6; cursor: pointer;}.o-mail-ChatHub-optionsBtn{font-size: 15px; width: 30px !important; height: 30px !important; border: 1px solid #d6dbe0 !important;}.o-mail-ChatHub-optionsBtn:hover, .o-mail-ChatHub-optionsBtn.show{background-color: #dee2e6 !important; opacity: 100% !important;}

/* /mail/static/src/core/common/chat_window.scss */
 .o-mail-ChatWindow{width: 380px; z-index: 1001; outline: none;}.o-mail-ChatWindow:not(.o-mobile){--border-opacity: .15; aspect-ratio: 9 / 15;}.o-mail-ChatWindow-command{color: inherit !important;}.o-mail-ChatWindow-command.o-quick{opacity: 15%;}.o-mail-ChatWindow-command:not(.o-actionsMenu){padding: 2px;}.o-mail-ChatWindow-command:hover, .o-mail-ChatWindow-command.o-active, .o-mail-ChatWindow-command.o-hover{color: var(--mail-ChatWindow-commandHoverColor, black) !important;}.o-mail-ChatWindow-command:hover.o-quick, .o-mail-ChatWindow-command:hover.o-actionsMenu, .o-mail-ChatWindow-command.o-active.o-quick, .o-mail-ChatWindow-command.o-active.o-actionsMenu, .o-mail-ChatWindow-command.o-hover.o-quick, .o-mail-ChatWindow-command.o-hover.o-actionsMenu{backdrop-filter: invert(0.025);}.o-mail-ChatWindow-command:hover, .o-mail-ChatWindow-command.o-active, .o-mail-ChatWindow-command.o-hover{border-color: transparent !important;}.o-mail-ChatWindow-command:not(.o-active):not(.o-hover) .fa-caret-down{opacity: 25%;}.o-mail-ChatWindow-command.o-small{--border-opacity: 0.5 ;}.o-mail-ChatWindow-closePanel{z-index: 2;}.o-mail-ChatWindow-counter{margin-bottom: 2px;}.o-mail-ChatWindow-country{width: 24px;}.o-mail-ChatWindow-header .o-mail-ChatWindow-threadAvatar img{height: 28px; width: 28px;}.o-mail-ChatWindow-typing{font-size: 0.73125rem; z-index: 19;}

/* /mail/static/src/core/common/composer.scss */
 .o-mail-Composer{grid-template-areas: "sidebar-header core-header" "sidebar-main core-main" "sidebar-footer core-footer"; grid-template-columns: auto 1fr; grid-template-rows: auto 1fr auto;}.o-mail-Composer.o-hasSelfAvatar{grid-template-columns: 42px 1fr;}.o-mail-Composer .o-mail-Composer-sidebarMain{padding-top: 0.4375rem; width: 48px;}.o-mail-Composer .o-mail-Composer-coreHeader{grid-area: core-header;}.o-mail-Composer .o-mail-Composer-coreMain{grid-area: core-main;}.o-mail-Composer .o-mail-Composer-sidebarMain{grid-area: sidebar-main;}.o-mail-Composer .o-mail-Composer-footer{grid-area: core-footer;}.o-mail-Composer.o-chatWindow .o-mail-Composer-mainActions{margin-top: 2px;}.o-mail-Composer:not(.o-focused) .o-mail-Composer-input::placeholder{opacity: 50%;}.o-mail-Composer:not(.o-focused) .o-mail-Composer-actions button:not(:hover):not(.o-active):not(.show){opacity: 25%;}.o-mail-Composer-actions button{opacity: 35%;}@media (hover: hover){.o-mail-Composer-actions button:hover{opacity: 100%; color: var(--mail-Composer-actionHoverColor, black); backdrop-filter: invert(0.025);}.o-mail-Message.o-editing .o-mail-Composer-actions button:hover{background-color: rgba(0, 0, 0, 0.05);}}.o-mail-Composer-actions button:disabled{--btn-active-color: var(--btn-disabled-color); --btn-hover-color: var(--btn-disabled-color); opacity: 25%;}.o-mail-Composer-actions button.o-mail-Composer-send.btn-link:not(:disabled){background-color: rgba(113, 99, 158, 0.75); opacity: 100% !important;}.o-mail-Composer-actions button.o-mail-Composer-send.btn-link:not(:disabled):focus-visible{outline: #4f456f solid 1px;}.o-mail-Composer-actions button.o-mail-Composer-send.btn-link:not(:disabled) i.fa-paper-plane-o{color: #FFF; transform: translate(-1px); scale: .85;}.o-mail-Composer-actions button.rounded-circle{aspect-ratio: 1;}.o-mail-Composer-bg{background-color: var(--mail-Composer-bg, white); --border-opacity: .5;}.o-mail-Composer-inputStyle{padding-top: 10px; padding-bottom: 10px; padding-left: 12px; padding-right: 12px; line-height: 1.42857143 !important;}.o-mail-Composer.o-chatWindow .o-mail-Composer-inputStyle{padding-top: 7px; padding-bottom: 7px;}.o-mail-Composer.o-editing .o-mail-Composer-inputStyle{padding-left: 8px; padding-right: 8px;}.o-mail-Composer-input{font-family: "text-emoji", var(--font-sans-serif); max-height: Min(100px, 60vh); resize: none;}@media (min-height: 325px){.o-mail-Composer.o-chatWindow .o-mail-Composer-input{max-height: Min(550px, 70vh);}}@media (min-height: 425px){.o-mail-Composer.o-discussApp .o-mail-Composer-input{max-height: 50vh;}}.o-mail-Composer.o-extended .o-mail-Composer-input{max-height: Min(400px, 30vh);}.o-mail-Composer-input::placeholder{opacity: 75%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}.o-mail-Composer-avatar{--Avatar-size: 36px;}.o-mail-Composer-fake{height: 0; left: -10000px; top: -10000px; visibility: hidden;}.o-mail-Composer-bg.o-iosPwa:not(:focus-within){margin-bottom: 48px !important;}.o-mail-Composer-bg:has(textarea:focus){--border-opacity: 0.35; border-color: rgba(113, 99, 158, var(--border-opacity)) !important;}

/* /mail/static/src/core/common/core.scss */
 .o-mail-discussSidebarBgColor{background-color: #FFFFFF;}.o-mail-brighter{filter: brightness(1.2);}.o-bg-black{background-color: rgba(0, 0, 0, var(--bg-opacity, 1));}.o-discuss-badge{--o-discuss-badge-bg: #28a745; color: white !important; background-color: var(--o-discuss-badge-bg) !important; align-items: center; justify-content: center; padding: 3px 4px;}.o-discuss-badge.o-muted{--o-discuss-badge-bg: #ced4da;}.o-discuss-mobileContextMenu{top: auto !important; border-top: 1px solid #dee2e6 !important;}.o-discuss-separator{opacity: 0.08333333;}.o-discuss-badge, .o-discuss-badgeShape{display: -webkit-box; display: -webkit-flex; display: flex; transform: translate(0, 0) !important; font-size: 0.687505rem !important; user-select: none;}.o-min-height-0{min-height: 0;}.o-min-width-0{min-width: 0;}.o-opacity-35{opacity: 35%;}.o-gap-0_5{gap: 2px;}.o-py-0_5{padding-top: 2px; padding-bottom: 2px;}.o-hover-text-underline:hover{text-decoration: underline;}.o-text-white{color: #FFF;}.o-xsmaller{font-size: 0.65rem;}.o-xxsmaller{font-size: 0.5rem;}.o-yellow{color: #ffc107;}a.o_mail_redirect, a.o_channel_redirect, a.o-discuss-mention{border-radius: var(--border-radius-sm); padding: 0rem 0.1875rem; border: 1px solid; font-weight: 500; display: inline-block;}a.o_mail_redirect, a.o_channel_redirect{color: #5a4f7f; background-color: rgba(113, 99, 158, 0.15); border-color: rgba(113, 99, 158, 0.25);}a.o_mail_redirect:hover, a.o_channel_redirect:hover{color: #4f456f; background-color: rgba(113, 99, 158, 0.2); border-color: rgba(113, 99, 158, 0.5);}a.o_mail_redirect:focus, a.o_channel_redirect:focus{box-shadow: 100, 88, 141;}a.o_mail_redirect:active, a.o_channel_redirect:active{color: #000000; background-color: rgba(220, 217, 231, 0.32); border-color: rgba(175, 167, 200, 0.325);}a.o_mail_redirect:disabled, a.o_channel_redirect:disabled{color: #000000; background-color: rgba(113, 99, 158, 0.15); border-color: rgba(113, 99, 158, 0.25);}a.o-discuss-mention{color: #5a4f7f; background-color: rgba(113, 99, 158, 0.15); border-color: rgba(113, 99, 158, 0.25); cursor: default !important;}a.o-discuss-mention:hover{color: #5a4f7f; background-color: rgba(113, 99, 158, 0.15); border-color: rgba(113, 99, 158, 0.25);}a.o-discuss-mention:focus{box-shadow: 100, 88, 141;}a.o-discuss-mention:active{color: #000000; background-color: rgba(220, 217, 231, 0.32); border-color: rgba(175, 167, 200, 0.325);}a.o-discuss-mention:disabled{color: #000000; background-color: rgba(113, 99, 158, 0.15); border-color: rgba(113, 99, 158, 0.25);}.o-discuss-inCallIconColor{color: #71639e;}.o-mail-DiscussSystray{--border-color: #dee2e6 !important;}.o-mail-DiscussSystray-class{margin-top: 0px; margin-bottom: 0px; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center;}.o-mail-DiscussSystray-class:hover, .o-mail-DiscussSystray-class.show{background-color: rgba(0, 0, 0, 0.075);}.o-mail-systrayFullscreenDropdownMenu{top: 46px !important; height: calc(100% - 46px);}.o-pointer-events-none{pointer-events: none;}.o-visible-short-delay{animation: o-visible-short-delay-animation 0s ease-in 0.25s forwards; display: none;}@keyframes o-visible-short-delay-animation{to{display: -webkit-inline-box; display: -webkit-inline-flex; display: inline-flex;}}@media (hover: none){.o-opacity-hoverable{opacity: 100 !important;}}.o-mail-Discuss-threadActionPopover{width: Min(95vw, 400px); max-height: Min(50vh, 530px);}

/* /mail/static/src/core/common/country_flag.scss */
 .o-mail-CountryFlag{aspect-ratio: 3/2;}

/* /mail/static/src/core/common/im_status.scss */
 .o-mail-ImStatus{width: 1.09375rem; height: 1.09375rem;}.o-mail-ImStatus.o-md{width: 0.875rem; height: 0.875rem;}.o-mail-ImStatus.o-sm{width: 0.8125rem; height: 0.8125rem;}.o-mail-ImStatus .o-away{color: #ffc107;}

/* /mail/static/src/core/common/link_preview.scss */
 .o-mail-LinkPreviewCard{max-width: 320px;}.o-mail-LinkPreviewCard .row{min-height: 80px;}.o-mail-LinkPreviewCard-description{display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2;}.o-mail-LinkPreviewCard-imageLinkWrap{border-right: 1px solid var(--border-color-translucent);}.o-mail-LinkPreviewImage img{max-height: 240px; max-width: 320px;}.o-mail-ChatWindow .o-mail-LinkPreviewImage img{max-width: 100%;}.o-mail-LinkPreviewVideo{max-width: 320px;}.o-mail-LinkPreviewVideo .row{min-height: 80px;}.o-mail-LinkPreviewVideo-hasDescription{display: -webkit-box; -webkit-box-orient: vertical;}.o-mail-LinkPreviewVideo-hasDescription.o-mail-LinkPreviewVideo-description{-webkit-line-clamp: 2;}.o-mail-LinkPreviewVideo-hasDescription.o-mail-LinkPreviewVideo-title{-webkit-line-clamp: 3;}.o-mail-LinkPreviewVideo-videoWrap{border-right: 1px solid var(--border-color-translucent);}.o-mail-LinkPreviewVideo-play{background: #000000;}.o-mail-LinkPreviewVideo-play i{color: #ffffff;}@media (hover: hover){.o-mail-LinkPreview-aside{display: none;}.o-mail-LinkPreviewCard:hover .o-mail-LinkPreview-aside, .o-mail-LinkPreviewImage:hover .o-mail-LinkPreview-aside, .o-mail-LinkPreviewVideo:hover .o-mail-LinkPreview-aside{display: block;}}

/* /mail/static/src/core/common/message.scss */
 .o-mail-Message{transition: background-color .2s ease-out, opacity .5s ease-out, box-shadow .5s ease-out, transform .2s ease-out;}.o-mail-Message.o-card{outline: 1px solid rgba(222, 226, 230, 0.35); outline-offset: -1px; background-color: white; --border-opacity: .5;}.o-mail-Message.o-highlighted{transform: translateY(-16px);}.o-mail-Message.o-actionMenuMobileOpen{background-color: rgba(113, 99, 158, 0.1); outline: 1px solid rgba(113, 99, 158, 0.15); outline-offset: -1px;}.o-mail-Message-date{opacity: 50%;}.o-mail-Message-seenContainer{font-size: 0.65rem; right: 2px; bottom: -2px;}.o-mail-Message-sidebar{flex-basis: 42px; max-width: 42px;}.o-mail-Message-sidebar.o-inChatWindow{flex-basis: 34px; max-width: 34px;}.o-mail-Message-avatarContainer{width: 36px; height: 36px;}.o-mail-Message-avatarContainer.o-inChatWindow{width: 28px; height: 28px;}@font-face{font-family: "text-emoji"; src: local("Segoe UI"), local("Apple Color Emoji"), local("Android Emoji"), local("Noto Color Emoji"), local("Twitter Color Emoji"), local("Twitter Color"), local("EmojiOne Color"), local("EmojiOne"), local(EmojiSymbols), local(Symbola); unicode-range: U+231A-231B, U+23E9-23EC, U+23F0, U+23F3, U+25FD-25FE, U+2614-2615, U+2648-2653, U+267F, U+2693, U+26A1, U+26AA-26AB, U+26BD-26BE, U+26C4-26C5, U+26CE, U+26D4, U+26EA, U+26F2-26F3, U+26F5, U+26FA, U+26FD, U+2705, U+270A-270B, U+2728, U+274C, U+274E, U+2753-2755, U+2757, U+2795-2797, U+27B0, U+27BF, U+2B1B-2B1C, U+2B50, U+2B55, U+FE0F, U+1F004, U+1F0CF, U+1F18E, U+1F191-1F19A, U+1F1E6-1F1FF, U+1F201, U+1F21A, U+1F22F, U+1F232-1F236, U+1F238-1F23A, U+1F250-1F251, U+1F300-1F320, U+1F32D-1F335, U+1F337-1F393, U+1F3A0-1F3CA, U+1F3CF-1F3D3, U+1F3E0-1F3F0, U+1F3F4, U+1F3F8-1F43E, U+1F440, U+1F442-1F4FC, U+1F4FF-1F53D, U+1F54B-1F567, U+1F57A, U+1F595-1F596, U+1F5A4, U+1F5FB-1F64F, U+1F680-1F6CC, U+1F6D0-1F6D2, U+1F6D5-1F6D7, U+1F6DC-1F6DF, U+1F6EB-1F6EC, U+1F6F4-1F6FC, U+1F7E0-1F7EB, U+1F7F0, U+1F90C-1F93A, U+1F93C-1F945, U+1F947-1FA7C, U+1FA80-1FAC5, U+1FACE-1FADB, U+1FAE0-1FAE8, U+1FAF0-1FAF8; size-adjust: 121%;}.o-mail-Message-body{font-family: "text-emoji", var(--font-sans-serif);}.o-mail-Message-body:not(.o-note){padding-left: 12px; padding-right: 12px;}.o-mail-Message.o-editing .o-mail-Message-body:not(.o-note){padding-left: 4px; padding-right: 4px;}.o-mail-Message-body > p{margin-bottom: 0 !important;}.o-mail-Message-body table[align="left"][width="100%"]{float: none;}.o-mail-Message-bubble.o-blue{background-color: #e2f3f6 !important; border-color: #bbe3ea !important;}.o-mail-Message-bubble.o-blue.o-muted{background-color: #f1f9fb !important;}.o-mail-Message-bubble.o-green{background-color: #e4f4e8 !important; border-color: #c0e5c9 !important;}.o-mail-Message-bubble.o-green.o-muted{background-color: #f2faf4 !important;}.o-mail-Message-bubble.o-orange{background-color: #ffeabf !important; border-color: #ffd98c !important;}.o-mail-Message-bubble.o-orange.o-muted{background-color: #fffcf5 !important;}.o-mail-ChatWindow .o-mail-Message.o-selfAuthored{flex-direction: row-reverse;}.o-mail-ChatWindow .o-mail-Message.o-selfAuthored .o-mail-Message-core, .o-mail-ChatWindow .o-mail-Message.o-selfAuthored .o-mail-Message-textContent{flex-direction: row-reverse;}.o-mail-ChatWindow .o-mail-Message.o-selfAuthored .o-mail-Message-header{-webkit-box-pack: end; justify-content: flex-end;}.o-mail-ChatWindow .o-mail-Message.o-selfAuthored .o-mail-Message-author{display: none;}.o-mail-Message-actions{z-index: 18;}.o-mail-Message-actions.o-expanded{z-index: 19;}.o-mail-Message-actions button{opacity: 50%;}.o-mail-Message-actions button:hover, .o-mail-Message-actions button.focus, .o-mail-Message-actions button.show{opacity: 100%; color: var(--mail-Message-actionIconHoveredColor, black);}.o-mail-Message-moreMenu{z-index: 21;}.o-mail-Message-openActionMobile:active{opacity: 75% !important;}.o-mail-Message-pendingProgress{animation: o-mail-message-pendingProgress-animation 0s ease-in 0.5s forwards; visibility: hidden;}@keyframes o-mail-message-pendingProgress-animation{to{visibility: visible;}}.o-mail-Message-searchHighlight{background: rgba(255, 172, 0, 0.75);}.o-mail-Message-starred{color: #f3cc00;}.o-mail-Message-translated{color: #017e84;}

/* /mail/static/src/core/common/message_action_menu_mobile.scss */
 .o-mail-MessageActionMenuMobile button{border-color: #dee2e6;}.o-mail-MessageActionMenuMobile button:active{border: none !important;}

/* /mail/static/src/core/common/message_card_list.scss */
 .o-mail-MessageCardList .card-body{background-color: var(--mail-MessageCardList-cardBodyBg, white);}.o-mail-MessageCardList .card-body:hover .o-mail-MessageCard-jump{opacity: 100 !important;}.o-mail-MessageCard-jump{color: white;}.o_touch_device .o-mail-MessageCard-jump.btn{font-size: 0.9rem;}

/* /mail/static/src/core/common/message_in_reply.scss */
 .o-mail-MessageInReply-avatar{width: 18px; height: 18px;}.o-mail-MessageInReply-core{border-left: 3px solid transparent !important; border-top: none !important; border-right: none !important; border-bottom: none !important; background-color: rgba(255, 255, 255, 0.5) !important;}.o-mail-MessageInReply-core.o-blue{border-left-color: #8bd1dc !important;}.o-mail-MessageInReply-core.o-green{border-left-color: #94d3a2 !important;}.o-mail-MessageInReply-core.o-orange{border-left-color: #ffcd66 !important;}.o-mail-MessageInReply-content{display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2;}.o-mail-MessageInReply-message p, .o-mail-MessageInReply-message div{display: inline; margin: 0;}.o-mail-MessageInReply-message br{display: none;}

/* /mail/static/src/core/common/message_reaction_list.scss */
 .o-mail-MessageReaction{line-height: 1.25;}.o-mail-MessageReaction:not(.o-selfReacted):hover{--border-color: #71639e;}.o-mail-MessageReactionList-preview:hover{background-color: #f8f9fa !important;}.o-mail-MessageReactionList-previewText{max-width: 200px;}

/* /mail/static/src/core/common/message_reaction_menu.scss */
 .o-mail-MessageReactionMenu-avatar{width: 36px; height: 36px;}.o-mail-MessageReactionMenu > .modal-body{padding: 0;}.o-mail-MessageReactionMenu-persona:not(.o-isDeviceSmall):not(:hover) button.fa-trash{opacity: 0;}.o-mail-MessageReactionMenu-persona:not(.o-isDeviceSmall):hover button.fa-trash{opacity: 100;}.o-mail-MessageReactionMenu-persona:not(.o-isDeviceSmall):hover button.fa-trash:hover{background-color: #f8f9fa;}

/* /mail/static/src/core/common/message_reactions.scss */
 .o-mail-MessageReactions:not(.o-emojiPickerOpen):not(:hover) .o-mail-MessageReactions-add{visibility: hidden;}.o-mail-MessageReaction.o-selfReacted{--border-opacity: 0.75; background: #f1eff5;}

/* /mail/static/src/core/common/message_seen_indicator.scss */
 .o-mail-MessageSeenIndicator i{line-height: 1.5;}.o-mail-MessageSeenIndicator i.o-second{top: -3px;}

/* /mail/static/src/core/common/navigable_list.scss */
 .o-mail-NavigableList{z-index: 21;}.o-mail-NavigableList-active{background-color: var(--mail-NavigableList-activeBgColor, #f1eff5);}.o-mail-NavigableList-item:hover{background-color: var(--mail-NavigableList-hoverBgColor, #f8f7fa);}.o-mail-NavigableList-floatingLoading{right: 1px;}

/* /mail/static/src/core/common/picker.scss */
 .o-mail-Picker{height: 55vh;}.o-mail-Picker .o-EmojiPicker{width: 100% !important;}.o-mail-Picker .o-EmojiPicker .o-Emoji{width: 40px; font-size: 1.5rem !important;}.o-mail-Picker .o-EmojiPicker-navbar{gap: 0.5rem !important;}

/* /mail/static/src/core/common/picker_content.scss */
 .popover .o-mail-PickerContent{width: 300px; height: 365px;}.popover .o-mail-PickerContent .o-EmojiPicker{width: 100% !important; height: 100% !important;}

/* /mail/static/src/core/common/thread.scss */
 .o-mail-Thread:focus-visible{outline: 0;}.o-mail-Thread-jumpPresent{z-index: 20; --border-opacity: .5;}.o-mail-Thread-newMessage{transition: opacity 0.5s;}.o-mail-Thread-newMessage span{font-size: 0.6rem; clip-path: polygon(0% 50%, 15% 0%, 100% 0%, 100% 100%, 15% 100%);}.o_mail_notification{display: inline;}.o_mail_notification a:hover{text-decoration: underline;}.o-mail-NotificationMessage p{margin-bottom: 0;}.o-mail-NotificationMessage:has(.o_hide_author) .o-mail-NotificationMessage-author{display: none !important;}.o-mail-Thread-banner{z-index: 20; --border-opacity: 0.25;}.o-mail-Thread-bannerHover:hover{filter: brightness(98%);}

/* /mail/static/src/discuss/core/common/action_panel.scss */
 .o-mail-ActionPanel .form-check-input{--form-check-bg: #FFFFFF;}@media (min-width: 750px){.o-mail-ActionPanel:not(.o-mail-ActionPanel-chatter){max-width: 30vw;}}@media (min-width: 1000px){.o-mail-ActionPanel:not(.o-mail-ActionPanel-chatter){max-width: 50vw;}}@media (min-width: 1200px){.o-mail-ActionPanel:not(.o-mail-ActionPanel-chatter){max-width: 55vw;}}@media (min-width: 1600px){.o-mail-ActionPanel:not(.o-mail-ActionPanel-chatter){max-width: 65vw;}}@media (min-width: 2300px){.o-mail-ActionPanel:not(.o-mail-ActionPanel-chatter){max-width: 75vw;}}.popover .o-mail-ActionPanel:not(.o-mail-ActionPanel-chatter){width: 475px; max-width: unset;}.o-mail-ActionPanel-header{z-index: 20;}

/* /mail/static/src/discuss/core/common/channel_invitation.scss */
 .o-discuss-ChannelInvitation{min-height: 0;}.o-discuss-ChannelInvitation-selectable.o-odd{background-color: #f8f9fa !important;}.o-discuss-ChannelInvitation-selectable:hover{background-color: #f1f3f5 !important;}.o-discuss-ChannelInvitation-selectable.o-selected{background-color: #eae8f0 !important;}.o-discuss-ChannelInvitation-selectedList{max-height: 100px;}.o-discuss-ChannelInvitation-selectedList button{background-color: #eae8f0;}.o-discuss-ChannelInvitation-selectedList button:not(:hover) .oi-close{border-color: transparent !important;}.o-discuss-ChannelInvitation-selectedList button:hover .oi-close{color: #dc3545; border-color: #dc3545 !important;}.o-discuss-ChannelInvitation-avatar{width: 32px; aspect-ratio: 1;}.o-discuss-ChannelInvitation-invitationBox{position: sticky; bottom: -8px;}

/* /mail/static/src/discuss/core/common/channel_member_list.scss */
 .o-discuss-ChannelMember.o-offline{opacity: 33%;}.o-discuss-ChannelMember.o-offline.cursor-pointer:hover{opacity: 75%;}.o-discuss-ChannelMember.cursor-pointer:hover{background-color: var(--discuss-ChannelMember-hoverBg, #f1f3f5);}.o-discuss-ChannelMember-avatar{width: 32px; aspect-ratio: 1;}

/* /mail/static/src/discuss/core/common/discuss_notification_settings.scss */
 .o-mail-DiscussNotificationSettings button.o-selected{background-color: var(--mail-DiscussNotificationSettings-btnSelectedBg, #e9ecef);}

/* /mail/static/src/discuss/core/common/notification_settings.scss */
 .o-discuss-NotificationSettings .o-mail-NotificationSettings-submenu{max-width: 250px !important;}.o-discuss-NotificationSettings .o-discuss-NotificationSettings-separator{min-width: 20px;}.o-discuss-NotificationSettings button:hover{background-color: var(--mail-NotificationSettings-btnHoverBg, #e9ecef);}.o-discuss-NotificationSettings-defaultValue{color: #71639e;}

/* /mail/static/src/discuss/call/common/call.scss */
 .o-discuss-Call{height: 50%; min-height: 50%; background: var(--o-discuss-Call-bgColor, #212529);}.o-discuss-Call.o-minimized{height: 20%; min-height: max(20%, 130px);}.o-discuss-Call.o-minimized.o-compact{height: 10%; min-height: max(10%, 100px);}.o-discuss-Call-main{background-color: rgba(0, 0, 0, 0.5);}.o-discuss-Call-sidebar{width: 120px; min-width: 120px; overflow-y: auto; overflow-x: hidden;}.o-discuss-Call-sidebar::-webkit-scrollbar{width: 0.3vw;}.o-discuss-Call-sidebarCard{aspect-ratio: 16/9;}.o-discuss-Call-mainCardStyle{width: var(--width); height: var(--height); min-width: var(--width); min-height: var(--height); aspect-ratio: 16/9;}.o-discuss-Call-sidebarToggler{top: 50%; transform: translateY(-50%); right: 0;}.o-discuss-Call-sidebar::-webkit-scrollbar{background: #212529;}.o-discuss-Call-sidebar::-webkit-scrollbar-thumb{background: #495057;}.o-discuss-Call-sidebarToggler{color: white; border-radius: 10px 0px 0px 10px; border-right: 0px; background-color: #1a1a1a;}

/* /mail/static/src/discuss/call/common/call_action_list.scss */
 .o-discuss-CallActionList-bar button:not(.btn-danger):not(.btn-success){background-color: var(--o-discuss-CallActionList-bgColor, #343a40); color: #FFFFFF;}.o-discuss-CallActionList-bar button{aspect-ratio: 1;}

/* /mail/static/src/discuss/call/common/call_invitation.scss */
 .o-discuss-CallInvitation{background-color: #000; color: #fff; border-color: #dee2e6 !important;}.o-discuss-CallInvitation.o-cameraPreview{aspect-ratio: 16 / 9; width: Min(720px, 92vw);}.o-discuss-CallInvitation button{aspect-ratio: 1;}.o-discuss-CallInvitation img{aspect-ratio: 1; width: 75px; border: 3px solid gray;}.o-discuss-CallInvitation.o-cameraPreview img{width: 50px;}

/* /mail/static/src/discuss/call/common/call_invitations.scss */
 .o-discuss-CallInvitations{z-index: 1055;}.o-discuss-CallInvitation-avatar{aspect-ratio: 1;}

/* /mail/static/src/discuss/call/common/call_menu.scss */
 .o-discuss-CallMenu-buttonContent{max-width: 150px; border: 1px solid rgba(113, 99, 158, 0.7) !important; background-color: #f6f6f9 !important;}.o-discuss-CallMenu-buttonContent.o-isOdooCommunity{height: 26px !important; overflow: hidden; color: #71639e !important;}.o-discuss-CallMenu-animation{animation: flash 2s; animation-direction: alternate; animation-iteration-count: 2;}.o-discuss-CallMenu-animation.o-isOdooCommunity{transform: translateY(10px);}

/* /mail/static/src/discuss/call/common/call_participant_card.scss */
 .o-discuss-CallParticipantCard{color: white; aspect-ratio: 16/9;}.o-discuss-CallParticipantCard.o-isTalking{box-shadow: inset 0 0 0 4px var(--discuss-talkingColor, #34ce57);}.o-discuss-CallParticipantCard.o-isTalking.o-inset{box-shadow: inset 0 0 0 2px var(--discuss-talkingColor, #34ce57);}.o-discuss-CallParticipantCard.o-inset{height: 20%; max-height: 125px !important; right: 1vh; bottom: 1vh; position: absolute !important; cursor: move !important;}.o-discuss-CallParticipantCard.o-inset.o-small{width: 30%; left: 0; top: 0;}.o-mail-ChatWindow .o-discuss-CallParticipantCard.o-inset{bottom: 5vh;}.o-discuss-CallParticipantCard.o-inset .o-discuss-CallParticipantCard-avatar img{max-height: min(70%, 70px); max-width: min(70%, 70px);}.o-discuss-CallParticipantCard-avatar:not(.o-minimized){background-color: var(--o-discuss-CallParticipantCard-avatarBgColor, #495057);}.o-discuss-CallParticipantCard-avatar img{max-height: min(100%, 100px); max-width: min(100%, 100px); aspect-ratio: 1; border: none;}.o-discuss-CallParticipantCard-avatar img.o-isTalking{outline: 4px solid var(--discuss-talkingColor, #34ce57); outline-offset: -4px;}.o-discuss-CallParticipantCard-avatar img.o-isInvitation:not(:hover){animation: o-discuss-CallParticipantCard-avatarImag_borderPulse 3s linear infinite;}.o-discuss-CallParticipantCard-avatar img.o-isInvitation:hover{border: solid #dc3545;}@keyframes o-discuss-CallParticipantCard-avatarImag_borderPulse{0%{border: solid white;}20%{border: solid #6c757d;}35%{border: solid #f8f9fa;}50%{border: solid #6c757d;}70%{border: solid #f8f9fa;}85%{border: solid #495057;}}.o-discuss-CallParticipantCard-iconBlackBg{background-color: rgba(0, 0, 0, 0.75); opacity: 75%;}.o-discuss-CallParticipantCard-overlay{margin: Min(5%, 8px);}.o-discuss-CallParticipantCard-overlayBottomName{background-color: rgba(0, 0, 0, 0.75);}.o-discuss-CallParticipantCard-overlay-replayButton{background-color: #212529;}.o-discuss-CallParticipantCard-overlay-replayButton:hover{background-color: #495057;}.o-discuss-CallParticipantCard-overlay-replayButton:active{background-color: #343a40;}

/* /mail/static/src/discuss/call/common/chat_window_patch.scss */
 @media (hover: none){.o-mail-ChatWindow-command .fa-phone, .o-mail-ChatWindow-command .fa-video-camera{color: #28a745 !important;}}@media (hover: hover){.o-mail-ChatWindow-command:hover:has(.fa-phone), .o-mail-ChatWindow-command:hover:has(.fa-video-camera){outline: 1px solid rgba(40, 167, 69, 0.35);}.o-mail-ChatWindow-command:hover:has(.fa-phone) i, .o-mail-ChatWindow-command:hover:has(.fa-video-camera) i{color: #2dbc4e !important;}}

/* /mail/static/src/discuss/call/common/discuss_patch.scss */
 @media (hover: none){.o-mail-Discuss-headerActions button .fa-phone, .o-mail-Discuss-headerActions button .fa-video-camera{color: #28a745 !important;}}@media (hover: hover){.o-mail-Discuss-headerActions button:hover:has(.fa-phone), .o-mail-Discuss-headerActions button:hover:has(.fa-video-camera){background-color: #f8f9fa !important; outline: 1px solid rgba(40, 167, 69, 0.25);}.o-mail-Discuss-headerActions button:hover:has(.fa-phone) i, .o-mail-Discuss-headerActions button:hover:has(.fa-video-camera) i{color: #2dbc4e !important;}}

/* /mail/static/src/discuss/typing/common/typing.scss */
 .o-discuss-Typing-dot{animation: o_mail_Typing_animation 1.5s linear infinite;}.o-discuss-Typing-dot.o-sizeMedium{width: 5px; height: 5px;}.o-discuss-Typing-dot.o-sizeSmall{width: 3px; height: 3px;}.o-discuss-Typing-dot.o-discuss-Typing-dot2{animation-delay: -1.35s;}.o-discuss-Typing-dot.o-discuss-Typing-dot3{animation-delay: -1.2s;}@keyframes o_mail_Typing_animation{0%, 40%, 100%{opacity: initial;}20%{opacity: 25%;}}.o-discuss-Typing:before{content: "\200b";}

/* /im_livechat/static/src/embed/common/chat_window_patch.scss */
 .o-mail-ChatWindow-command:hover, .o-mail-ChatWindow-command.o-active, .o-mail-ChatWindow-command.o-hover{color: inherit !important;}

/* /im_livechat/static/src/embed/common/close_confirmation.scss */
 .o-livechat-CloseConfirmation{z-index: 20;}

/* /im_livechat/static/src/embed/common/emoji_picker.scss */
 .o-EmojiPicker{font-size: 1.2em;}

/* /im_livechat/static/src/embed/common/livechat_button.scss */
 .o-livechat-LivechatButton{min-width: 100px; font-size: 14px; z-index: 5; transition: filter 0.3s;}.o-livechat-LivechatButton:hover{filter: brightness(90%);}.o-livechat-LivechatButton-notification{transform-origin: 100% 100%; bottom: 2.5em; right: 4.5em;}.o-livechat-LivechatButton-animate{animation: o-livechat-LivechatButton-notification-animation 0.6s ease-in-out forwards;}@keyframes o-livechat-LivechatButton-notification-animation{0%{opacity: 0; transform: scale(0);}20%{opacity: 0; transform: scale(0.5);}50%{opacity: 1;}75%{transform: scale(1.05);}100%{transform: scale(1);}}

/* /im_livechat/static/src/embed/common/scss/shadow.scss */
 :host, [data-bs-theme="light"]{--blue: #007bff; --indigo: #6610f2; --purple: #6f42c1; --pink: #e83e8c; --red: #dc3545; --orange: #fd7e14; --yellow: #ffc107; --green: #28a745; --teal: #20c997; --cyan: #17a2b8; --white: #FFFFFF; --gray: #6c757d; --gray-dark: #343a40; --o-cc5-btn-secondary-border: ; --o-cc5-btn-secondary: #F3F2F2; --o-cc5-btn-primary-border: ; --o-cc5-btn-primary: ; --o-cc5-link: ; --o-cc5-h6: ; --o-cc5-h5: ; --o-cc5-h4: ; --o-cc5-h3: ; --o-cc5-h2: ; --o-cc5-headings: #FFFFFF; --o-cc5-text: ; --o-cc5-bg: #111827; --o-cc4-btn-secondary-border: ; --o-cc4-btn-secondary: #F3F2F2; --o-cc4-btn-primary-border: ; --o-cc4-btn-primary: #111827; --o-cc4-link: #111827; --o-cc4-h6: ; --o-cc4-h5: ; --o-cc4-h4: ; --o-cc4-h3: ; --o-cc4-h2: ; --o-cc4-headings: ; --o-cc4-text: ; --o-cc4-bg: #714B67; --o-cc3-btn-secondary-border: ; --o-cc3-btn-secondary: #F3F2F2; --o-cc3-btn-primary-border: ; --o-cc3-btn-primary: ; --o-cc3-link: ; --o-cc3-h6: ; --o-cc3-h5: ; --o-cc3-h4: ; --o-cc3-h3: ; --o-cc3-h2: ; --o-cc3-headings: ; --o-cc3-text: ; --o-cc3-bg: #2D3142; --o-cc2-btn-secondary-border: ; --o-cc2-btn-secondary: ; --o-cc2-btn-primary-border: ; --o-cc2-btn-primary: ; --o-cc2-link: ; --o-cc2-h6: ; --o-cc2-h5: ; --o-cc2-h4: ; --o-cc2-h3: ; --o-cc2-h2: ; --o-cc2-headings: #111827; --o-cc2-text: ; --o-cc2-bg: #F3F2F2; --o-cc1-btn-secondary-border: ; --o-cc1-btn-secondary: ; --o-cc1-btn-primary-border: ; --o-cc1-btn-primary: ; --o-cc1-link: ; --o-cc1-h6: ; --o-cc1-h5: ; --o-cc1-h4: ; --o-cc1-h3: ; --o-cc1-h2: ; --o-cc1-headings: ; --o-cc1-text: ; --o-cc1-bg: #FFFFFF; --copyright-custom: rgba(0, 0, 0, 0.15); --copyright: ; --footer-custom: ; --footer: #111827; --header-sales_four-custom: ; --header-sales_four: #FFFFFF; --header-sales_three-custom: ; --header-sales_three: #F3F2F2; --header-sales_two-custom: ; --header-sales_two: #111827; --header-sales_one-custom: ; --header-sales_one: #F3F2F2; --menu-border-color: ; --menu-custom: ; --menu: #FFFFFF; --input: ; --body: white; --o-color-5: #111827; --o-color-4: #FFFFFF; --o-color-3: #F3F2F2; --o-color-2: #2D3142; --o-color-1: #714B67; --gray-100: #f8f9fa; --gray-200: #e9ecef; --gray-300: #dee2e6; --gray-400: #ced4da; --gray-500: #adb5bd; --gray-600: #6c757d; --gray-700: #495057; --gray-800: #343a40; --gray-900: #212529; --gray-white-85: rgba(255, 255, 255, 0.85); --gray-white-75: rgba(255, 255, 255, 0.75); --gray-white-50: rgba(255, 255, 255, 0.5); --gray-white-25: rgba(255, 255, 255, 0.25); --gray-black-75: rgba(0, 0, 0, 0.75); --gray-black-50: rgba(0, 0, 0, 0.5); --gray-black-25: rgba(0, 0, 0, 0.25); --gray-black-15: rgba(0, 0, 0, 0.15); --primary: #71639e; --secondary: #dee2e6; --success: #28a745; --info: #17a2b8; --warning: #ffac00; --danger: #dc3545; --light: #f8f9fa; --dark: #212529; --primary-rgb: 113, 99, 158; --secondary-rgb: 222, 226, 230; --success-rgb: 40, 167, 69; --info-rgb: 23, 162, 184; --warning-rgb: 255, 172, 0; --danger-rgb: 220, 53, 69; --light-rgb: 248, 249, 250; --dark-rgb: 33, 37, 41; --primary-text-emphasis: #2d283f; --secondary-text-emphasis: #595a5c; --success-text-emphasis: #10431c; --info-text-emphasis: #09414a; --warning-text-emphasis: #664500; --danger-text-emphasis: #58151c; --light-text-emphasis: #495057; --dark-text-emphasis: #495057; --primary-bg-subtle: #e3e0ec; --secondary-bg-subtle: #f8f9fa; --success-bg-subtle: #d4edda; --info-bg-subtle: #d1ecf1; --warning-bg-subtle: #ffeecc; --danger-bg-subtle: #f8d7da; --light-bg-subtle: #fcfcfd; --dark-bg-subtle: #ced4da; --primary-border-subtle: #c6c1d8; --secondary-border-subtle: #f2f3f5; --success-border-subtle: #a9dcb5; --info-border-subtle: #a2dae3; --warning-border-subtle: #ffde99; --danger-border-subtle: #f1aeb5; --light-border-subtle: #e9ecef; --dark-border-subtle: #adb5bd; --white-rgb: 255, 255, 255; --black-rgb: 0, 0, 0; --font-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Ubuntu, "Noto Sans", Arial, "Odoo Unicode Support Noto", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; --font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; --gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0)); --body-font-family: var(--font-sans-serif); --body-font-size: 0.875rem; --body-font-weight: 400; --body-line-height: 1.5; --body-color: #495057; --body-color-rgb: 73, 80, 87; --body-bg: #f8f9fa; --body-bg-rgb: 248, 249, 250; --emphasis-color: #000000; --emphasis-color-rgb: 0, 0, 0; --secondary-color: rgba(73, 80, 87, 0.75); --secondary-color-rgb: 73, 80, 87; --secondary-bg: #e9ecef; --secondary-bg-rgb: 233, 236, 239; --tertiary-color: rgba(73, 80, 87, 0.5); --tertiary-color-rgb: 73, 80, 87; --tertiary-bg: #f8f9fa; --tertiary-bg-rgb: 248, 249, 250; --heading-color: #212529; --link-color: #66598f; --link-color-rgb: 101.56719368, 88.756917, 142.743083; --link-decoration: none; --link-hover-color: #473e64; --link-hover-color-rgb: 71, 62, 100; --link-hover-decoration: none; --code-color: #d2317b; --highlight-color: #495057; --highlight-bg: #fff3cd; --border-width: 1px; --border-style: solid; --border-color: #dee2e6; --border-color-translucent: rgba(0, 0, 0, 0.175); --border-radius: 0.25rem; --border-radius-sm: 0.1875rem; --border-radius-lg: 0.375rem; --border-radius-xl: 1rem; --border-radius-xxl: 2rem; --border-radius-2xl: var(--border-radius-xxl); --border-radius-pill: 50rem; --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15); --box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); --box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175); --box-shadow-inset: inset 0 1px 2px rgba(0, 0, 0, 0.075); --focus-ring-width: 0.25rem; --focus-ring-opacity: 0.25; --focus-ring-color: rgba(113, 99, 158, 0.25); --form-valid-color: #28a745; --form-valid-border-color: #28a745; --form-invalid-color: #dc3545; --form-invalid-border-color: #dc3545;}:host{margin: 0; font-family: var(--body-font-family); font-size: var(--body-font-size); font-weight: var(--body-font-weight); line-height: var(--body-line-height); color: var(--body-color); text-align: var(--body-text-align); background-color: var(--body-bg); -webkit-text-size-adjust: 100%; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);}:host{--o-webclient-color-scheme: bright; font-size: 14px;}:host .o_hidden{display: none !important;}

/* /im_livechat/static/src/embed/common/thread_patch.scss */
 .o-livechat-NoPinMenu [data-oe-type="pin-menu"]{display: none;}

/* /im_livechat/static/src/embed/external/overrides.scss */
 .o-main-components-container, .o_notification_manager{position: fixed !important;}
# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

import logging
from odoo import api, models

_logger = logging.getLogger(__name__)


class ResDeviceLog(models.Model):
    _inherit = 'res.device.log'

    @api.model
    def _update_device(self, request):
        """
        Override để fix lỗi GeoIP AttributeError - đơn giản hóa để tránh lỗi
        """
        try:
            # Gọi parent method với try-catch để bắt lỗi GeoIP
            return super()._update_device(request)
        except AttributeError as e:
            if 'country_name' in str(e) or 'country_code' in str(e):
                _logger.warning("GeoIP AttributeError caught and ignored: %s", e)
                # Không làm gì cả, chỉ log warning
                return
            else:
                # Re-raise nếu không phải lỗi GeoIP
                raise

-- Script SQL để dọn dẹp module jazzy_backend_theme khỏi database
-- Chạy script này trong PostgreSQL để loại bỏ hoàn toàn module

-- 1. Xóa module khỏi bảng ir_module_module
DELETE FROM ir_module_module WHERE name = 'jazzy_backend_theme';

-- 2. Xóa các dependency liên quan
DELETE FROM ir_module_module_dependency WHERE name = 'jazzy_backend_theme';

-- 3. <PERSON><PERSON>a các view liên quan (nếu có)
DELETE FROM ir_ui_view WHERE module = 'jazzy_backend_theme';

-- 4. Xóa các data liên quan
DELETE FROM ir_model_data WHERE module = 'jazzy_backend_theme';

-- 5. Xóa các asset liên quan
DELETE FROM ir_asset WHERE path LIKE '%jazzy_backend_theme%';

-- 6. <PERSON><PERSON><PERSON> các config parameter liên quan
DELETE FROM ir_config_parameter WHERE key LIKE '%jazzy_backend_theme%';

-- 7. Commit changes
COMMIT;

# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

import logging
from odoo import api, models

_logger = logging.getLogger(__name__)


class Http(models.AbstractModel):
    _inherit = 'ir.http'

    @api.model
    def get_frontend_session_info(self):
        """
        Override để fix lỗi GeoIP AttributeError trong website
        """
        try:
            # Gọi parent method với try-catch để bắt lỗi GeoIP
            return super().get_frontend_session_info()
        except AttributeError as e:
            if 'country_code' in str(e) or 'country_name' in str(e):
                _logger.warning("GeoIP AttributeError caught in get_frontend_session_info: %s", e)
                # Trả về session info cơ bản mà không có GeoIP
                session_info = super(Http, self).get_frontend_session_info()
                # Override các field GeoIP với giá trị None
                session_info.update({
                    'geoip_country_code': None,
                    'geoip_phone_code': None,
                })
                return session_info
            else:
                # Re-raise nếu không phải lỗi GeoIP
                raise

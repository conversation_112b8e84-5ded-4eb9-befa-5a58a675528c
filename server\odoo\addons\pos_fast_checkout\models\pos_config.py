# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import models, fields, api


class PosConfig(models.Model):
    _inherit = 'pos.config'
    
    # Tính năng Fast Checkout
    fast_checkout_enabled = fields.<PERSON><PERSON>an(
        string='Kích hoạt Fast Checkout',
        default=True,
        help='Kích hoạt chế độ thanh toán nhanh với các tối ưu hóa workflow'
    )
    
    # Phím tắt Space
    space_key_checkout = fields.Boolean(
        string='Phím Space để thanh toán',
        default=True,
        help='Cho phép sử dụng phím Space để thực hiện thanh toán nhanh'
    )
    
    # In hóa đơn tự động
    auto_print_receipt_fast = fields.Boolean(
        string='In hóa đơn tự động (Fast)',
        default=True,
        help='Tự động in hóa đơn ngay sau khi thanh toán thành công mà không hiển thị màn hình xem trước'
    )
    
    # Phương thức thanh toán mặc định
    default_payment_method_id = fields.Many2one(
        'pos.payment.method',
        string='<PERSON>ư<PERSON>ng thức thanh toán mặc định',
        help='Phương thức thanh toán sẽ được tự động thêm khi vào màn hình thanh toán'
    )
    
    # Khách hàng mặc định
    default_customer_id = fields.Many2one(
        'res.partner',
        string='Khách hàng mặc định',
        domain=[('is_company', '=', False)],
        help='Khách hàng sẽ được tự động chọn cho tất cả đơn hàng mới'
    )
    
    # Tự động áp dụng thanh toán mặc định
    auto_apply_default_payment = fields.Boolean(
        string='Tự động áp dụng thanh toán mặc định',
        default=True,
        help='Tự động thêm phương thức thanh toán mặc định khi vào màn hình thanh toán'
    )
    
    # Tự động áp dụng khách hàng mặc định
    auto_apply_default_customer = fields.Boolean(
        string='Tự động áp dụng khách hàng mặc định',
        default=True,
        help='Tự động chọn khách hàng mặc định cho đơn hàng mới'
    )
    
    # Bỏ qua màn hình xác nhận
    skip_payment_confirmation = fields.Boolean(
        string='Bỏ qua xác nhận thanh toán',
        default=False,
        help='Bỏ qua các bước xác nhận không cần thiết trong quá trình thanh toán'
    )
    
    @api.onchange('fast_checkout_enabled')
    def _onchange_fast_checkout_enabled(self):
        """Khi tắt fast checkout, tắt tất cả các tính năng liên quan"""
        if not self.fast_checkout_enabled:
            self.space_key_checkout = False
            self.auto_print_receipt_fast = False
            self.auto_apply_default_payment = False
            self.auto_apply_default_customer = False
            self.skip_payment_confirmation = False
    
    @api.onchange('default_payment_method_id')
    def _onchange_default_payment_method_id(self):
        """Kiểm tra phương thức thanh toán mặc định có trong danh sách không"""
        if self.default_payment_method_id and self.payment_method_ids:
            if self.default_payment_method_id not in self.payment_method_ids:
                return {
                    'warning': {
                        'title': 'Cảnh báo',
                        'message': 'Phương thức thanh toán mặc định phải có trong danh sách phương thức thanh toán của POS này.'
                    }
                }
    
    def get_default_payment_method(self):
        """Lấy phương thức thanh toán mặc định"""
        if self.default_payment_method_id and self.default_payment_method_id in self.payment_method_ids:
            return self.default_payment_method_id
        return False
    
    def get_default_customer(self):
        """Lấy khách hàng mặc định"""
        return self.default_customer_id if self.default_customer_id else False

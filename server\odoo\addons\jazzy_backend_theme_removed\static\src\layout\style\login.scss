#wrapwrap > main {
    background: #f8f8fb;
}
.navbar {
    background: #fff !important;
}

body {
    font-family: 'Poppins', sans-serif !important;
}
body.bg-100 {
    background-color: #000000 !important;
}
.card.o_database_list {
    align-items: center;
    max-width: 450px !important
}
.card.o_database_list .card-body {
    background-color: #fff !important;
    border-radius: 5px !important;
    -webkit-box-shadow: 0 0.75rem 1.5rem rgba(18,38,63,.03) !important;
    box-shadow: 0 0.75rem 1.5rem rgba(18,38,63, .03) !important;
}
.o_main_navbar .o_menu_sections {
    flex-wrap: w
}
a {
    color: #556ee6;
    text-decoration: none;
}
a:hover {
    color: #4458b8;
    text-decoration: underline;
}
.alert-info {
    color: #306391;
    background-color: #dcedfc;
    border-color: #cbe4fb;
}
.oe_login_form button.btn-link {
    color: #495057;
    font-weight: 500;
    font-size: 14px !important;
}
.oe_login_form button.btn-link:hover {
    color: #171a1c;
}
// Login button starts
.btn-primary {
    color: #fff;
    background-color: #556ee6;
    border-color: #556ee6;
}
.btn-primary:hover {
    color: #fff;
    background-color: #485ec4;
    border-color: #4458b8;
}
.btn-outline-primary {
    border-color: #556ee6;
    color: #556ee6;
}
.btn-outline-primary:hover {
    color: #fff;
    background-color: #556ee6;
    border-color: #556ee6;
}
.btn-check:active+.btn-primary,
.btn-check:checked+.btn-primary,
.btn-primary.active,.btn-primary:active,
.show>.btn-primary.dropdown-toggle {
    color: #fff;
    background-color: #4458b8 !important;
    border-color: #4053ad !important;
}
.btn-check:focus+.btn-primary, .btn-primary:focus {
    color: #fff;
    background-color: #485ec4 !important;
    border-color: #4458b8 !important;
    -webkit-box-shadow: 0 0 0 .15rem rgba(111,132,234,.5) !important;
    box-shadow: 0 0 0 .15rem rgba(111,132,234,.5) !important;
}
.oe_login_form .btn {
    display: inline-block;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    padding: .47rem .75rem;
    border-radius: .25rem;
    -webkit-transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
}
.btn-secondary {
    color: #fff !important;
    background-color: #74788d !important;
    border-color: #74788d !important;
}
.btn-secondary:hover {
    color: #fff !important;
    background-color: #636678 !important;
    border-color: #5d6071 !important;
}
.btn-secondary:active {
    color: #fff;
    background-color: #5d6071 !important;
    border-color: #575a6a !important;
}
.btn-secondary i,.btn-secondary span {
    color: #fff !important;
}
.btn-fill-secondary:focus, .btn-secondary:focus, .btn-fill-secondary.focus, .focus.btn-secondary {
    box-shadow: none !important;
}
// Login button ends

// Input starts
.oe_login_form input {
    display: block;
    width: 100%;
    height: 40px !important;
    padding: 10px 20px;
    font-size: 13px;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da !important;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: .25rem;
    -webkit-transition: border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    transition: border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    box-shadow: none !important;
    margin-bottom:10px !important;
}
form label {
    font-weight: 400 !important;
}
.oe_login_form a.btn.btn-secondary {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.35rem 0.75rem;
}
.oe_login_form a.btn.btn-secondary i.fa.fa-database {
    margin-left: 5px;
}

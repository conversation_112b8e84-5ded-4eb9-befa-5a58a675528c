
/* /web/static/lib/bootstrap/scss/_functions.scss */
 

/* /web/static/lib/bootstrap/scss/_mixins.scss */
 

/* /web/static/src/scss/functions.scss */
 

/* /web/static/src/scss/mixins_forwardport.scss */
 

/* /web/static/src/scss/bs_mixins_overrides.scss */
 

/* /web/static/src/scss/utils.scss */
 

/* /web/static/src/scss/primary_variables.scss */
 

/* /muk_web_chatter/static/src/scss/variables.scss */
 

/* /web/static/src/core/avatar/avatar.variables.scss */
 

/* /web/static/src/core/notifications/notification.variables.scss */
 

/* /web/static/src/search/control_panel/control_panel.variables.scss */
 

/* /web/static/src/search/search_bar/search_bar.variables.scss */
 

/* /web/static/src/search/search_panel/search_panel.variables.scss */
 

/* /web/static/src/views/fields/statusbar/statusbar_field.variables.scss */
 

/* /web/static/src/views/fields/translation_button.variables.scss */
 

/* /web/static/src/views/form/form.variables.scss */
 

/* /web/static/src/views/kanban/kanban.variables.scss */
 

/* /web/static/src/webclient/burger_menu/burger_menu.variables.scss */
 

/* /web/static/src/webclient/navbar/navbar.variables.scss */
 

/* /mail/static/src/core/common/primary_variables.scss */
 

/* /mail/static/src/discuss/typing/common/primary_variables.scss */
 

/* /mail/static/src/scss/variables/primary_variables.scss */
 

/* /onboarding/static/src/scss/onboarding.variables.scss */
 

/* /web_editor/static/src/scss/web_editor.variables.scss */
 

/* /web_editor/static/src/scss/wysiwyg.variables.scss */
 

/* /portal/static/src/scss/primary_variables.scss */
 

/* /account/static/src/scss/variables.scss */
 @keyframes animate-red{0%{color: red;}100%{color: inherit;}}.animate{animation: animate-red 1s ease;}

/* /website/static/src/scss/primary_variables.scss */
 

/* /website/static/src/scss/options/user_values.scss */
 

/* /website/static/src/scss/options/colors/user_color_palette.scss */
 

/* /website/static/src/scss/options/colors/user_gray_color_palette.scss */
 

/* /website/static/src/scss/options/colors/user_theme_color_palette.scss */
 

/* /website_sale/static/src/scss/primary_variables.scss */
 

/* /hr_org_chart/static/src/scss/variables.scss */
 

/* /muk_web_appsbar/static/src/scss/variables.scss */
 

/* /website/static/src/snippets/s_badge/000_variables.scss */
 

/* /website/static/src/snippets/s_product_list/000_variables.scss */
 

/* /website/static/src/scss/secondary_variables.scss */
 

/* /web/static/src/scss/secondary_variables.scss */
 

/* /web_editor/static/src/scss/secondary_variables.scss */
 

/* /web/static/src/scss/bootstrap_overridden.scss */
 .user-select-none{-webkit-user-select: none !important;}

/* /web/static/src/scss/bs_mixins_overrides_backend.scss */
 

/* /web_editor/static/src/scss/bootstrap_overridden_backend.scss */
 

/* /web_editor/static/src/scss/bootstrap_overridden.scss */
 

/* /muk_web_appsbar/static/src/scss/mixins.scss */
 

/* /web/static/src/scss/pre_variables.scss */
 

/* /web/static/lib/bootstrap/scss/_variables.scss */
 

/* /web/static/lib/bootstrap/scss/_variables-dark.scss */
 

/* /web/static/lib/bootstrap/scss/_maps.scss */
 

/* /web/static/src/views/graph/graph_view.scss */
 .o_graph_view{--ControlPanel-border-bottom: none;}.o_graph_view .o_cp_bottom_left{display: block;}:not(.o-dashboard-action) > .o_graph_view .o_graph_canvas_container{min-height: 50vh;}.o_graph_view .o_view_sample_data .o_graph_canvas_container{opacity: 0.06; pointer-events: none; user-select: none;}

/* /web/static/src/views/pivot/pivot_view.scss */
 .o_pivot table{width: var(--PivotView-width, auto);}.o_pivot table.o_enable_linking .o_pivot_cell_value:not(.o_empty){color: #495057;}.o_pivot table.o_enable_linking .o_pivot_cell_value:not(.o_empty):hover, .o_pivot table.o_enable_linking .o_pivot_cell_value:not(.o_empty):focus, .o_pivot table.o_enable_linking .o_pivot_cell_value:not(.o_empty).focus{color: #66598f;}.o_pivot table .o_pivot_measure_row, .o_pivot table .o_pivot_origin_row, .o_pivot table .o_pivot_header_cell_closed, .o_pivot table .o_pivot_header_cell_opened{color: #495057;}.o_pivot table .o_pivot_measure_row:hover, .o_pivot table .o_pivot_measure_row:focus, .o_pivot table .o_pivot_measure_row.focus, .o_pivot table .o_pivot_origin_row:hover, .o_pivot table .o_pivot_origin_row:focus, .o_pivot table .o_pivot_origin_row.focus, .o_pivot table .o_pivot_header_cell_closed:hover, .o_pivot table .o_pivot_header_cell_closed:focus, .o_pivot table .o_pivot_header_cell_closed.focus, .o_pivot table .o_pivot_header_cell_opened:hover, .o_pivot table .o_pivot_header_cell_opened:focus, .o_pivot table .o_pivot_header_cell_opened.focus{color: #212529;}.o_pivot table .o_pivot_measure_row:hover, .o_pivot table .o_pivot_origin_row:hover, .o_pivot table .o_pivot_header_cell_closed:hover, .o_pivot table .o_pivot_header_cell_opened:hover{background-color: #e9ecef !important;}.o_pivot table .o_pivot_cell_value.o_cell_hover{background-color: rgba(var(--emphasis-color-rgb), 0.055);}.o_pivot table .o_pivot_cell_value{text-align: right !important; direction: ltr;}.o_pivot_view .o_cp_bottom_left{display: block;}.o_pivot_view .o_view_sample_data{overflow: hidden !important;}.o_pivot_view .o_view_sample_data .o_pivot{opacity: 0.06; pointer-events: none; user-select: none;}

/* /mail/static/src/views/web/activity/activity_controller.scss */
 .o_activity_view > table thead > tr > th:first-of-type{min-width: 300px;}.o_activity_view .o_activity_summary_cell .o-mail-ActivityCell-counter{color: #212529;}.o_activity_view .o_activity_summary_cell.planned{background-color: #28a745; color: #f8f9fa;}.o_activity_view .o_activity_summary_cell.planned a{color: #f8f9fa;}.o_activity_view .o_activity_summary_cell.planned a:hover{text-decoration: underline;}.o_activity_view .o_activity_summary_cell.overdue{background-color: #dc3545; color: #f8f9fa;}.o_activity_view .o_activity_summary_cell.overdue a{color: #f8f9fa;}.o_activity_view .o_activity_summary_cell.overdue a:hover{text-decoration: underline;}.o_activity_view .o_activity_summary_cell.today{background-color: #ffac00; color: #f8f9fa;}.o_activity_view .o_activity_summary_cell.today a{color: #f8f9fa;}.o_activity_view .o_activity_summary_cell.today a:hover{text-decoration: underline;}.o_activity_view .o_activity_summary_cell.done{background-color: #dee2e6;}.o_activity_view .o_activity_summary_cell.done a{color: #212529;}.o_activity_view .o_activity_summary_cell.done a:hover{text-decoration: underline;}.o_activity_view .o_activity_summary_cell.o_activity_empty_cell > i{display: none;}.o_activity_view .o_activity_summary_cell.o_activity_empty_cell:hover{background-color: #dddbe8;}.o_activity_view .o_activity_summary_cell.o_activity_empty_cell:hover > i{color: gray; display: -webkit-box; display: -webkit-flex; display: flex;}.o_activity_view .o_activity_record > div{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; align-items: center;}.o_activity_view .o_activity_record > div .o_m2o_avatar > img, .o_activity_view .o_activity_record > div > img{object-fit: cover; width: 32px; height: 32px; max-height: 32px;}.o_activity_view .o_activity_record > div .o_m2m_avatar, .o_activity_view .o_activity_record > div .o_m2m_avatar_empty{width: 32px !important; height: 32px !important;}.o_activity_view .o_activity_record > div .o_m2o_avatar{margin-right: 16px;}.o_activity_view .o_activity_record > div .o_m2o_avatar .o_delete{font-size: initial !important;}.o_activity_view .o_activity_record > div .o_m2m_avatar_empty{padding-top: 2px; font-size: large;}.o_activity_view .o_activity_record > div .o_text_bold{font-weight: 500;}.o_activity_view .o_activity_record > div .o_text_block{display: inline-block; max-width: 15rem; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; display: block;}.o_activity_view .o_activity_record:hover{background-color: #dddbe8;}.o_activity_view .o_activity_filter_planned{background-color: #eef5f1;}.o_activity_view .o_activity_filter_today{background-color: #f8f5ee;}.o_activity_view .o_activity_filter_overdue{background-color: #f7eff1;}.o_activity_view .o_activity_type_cell{min-width: 100px;}.o_activity_view .o_activity_type_cell .fa-ellipsis-v{cursor: pointer;}.o_activity_view .o_activity_type_cell .o_template_element{white-space: nowrap; padding: 5px; cursor: pointer;}.o_activity_view .o_activity_type_cell .o_template_element:hover{color: #008818;}.o_activity_view .o_activity_type_cell .o_activity_counter{margin: 5px 0 0 0;}.o_activity_view .o_activity_type_cell .o_activity_counter > .o_column_progress{width: 100%;}.o_activity_view .o_activity_type_cell .o_activity_counter > .o_column_progress > div.active{border: 1px solid;}.o_activity_view_table{height: 1px;}

/* /web_hierarchy/static/src/hierarchy.variables.scss */
 

/* /web_hierarchy/static/src/hierarchy_card.scss */
 .o_hierarchy_node_container{width: 250px; min-height: 130px;}.o_hierarchy_node_container .o_hierarchy_node_button_container{height: 30px;}.o_hierarchy_node_container.o_hierarchy_dragged{margin: 0px;}.o_hierarchy_node_container.o_hierarchy_dragged .o_hierarchy_node{background-color: white;}.o_hierarchy_node_container.o_hierarchy_hover .o_hierarchy_node{background-color: #e6f2f3; border: 3px solid green !important;}.o_hierarchy_node_container .o_hierarchy_node:hover{cursor: pointer;}.o_hierarchy_node_container .o_hierarchy_node .o_hierarchy_node_header{height: 30px;}.o_hierarchy_node_container .o_hierarchy_node .o_hierarchy_node_header div.o_field_background_image{width: 48px; height: 48px; margin-bottom: -24px; transform: translateY(-24px); z-index: 1;}.o_hierarchy_node_container .o_hierarchy_node .o_hierarchy_node_header div.o_field_background_image > img{border-radius: 50%;}.o_hierarchy_node_container .o_hierarchy_node .o_hierarchy_node_body{height: 65px;}.o_hierarchy_node_container .o_hierarchy_node .o_hierarchy_node_body .o_employee_availability{position: absolute; top: 4px; right: 2px;}.o_hierarchy_node_container .o_hierarchy_node .o_hierarchy_node_footer{height: 30px;}.o_hierarchy_node_container .o_hierarchy_node .o_hierarchy_node_color_0{background-color: #e9ecef;}.o_hierarchy_node_container .o_hierarchy_node .o_hierarchy_node_color_1{background-color: #ee2d2d;}.o_hierarchy_node_container .o_hierarchy_node .o_hierarchy_node_color_2{background-color: #dc8534;}.o_hierarchy_node_container .o_hierarchy_node .o_hierarchy_node_color_3{background-color: #e8bb1d;}.o_hierarchy_node_container .o_hierarchy_node .o_hierarchy_node_color_4{background-color: #5794dd;}.o_hierarchy_node_container .o_hierarchy_node .o_hierarchy_node_color_5{background-color: #9f628f;}.o_hierarchy_node_container .o_hierarchy_node .o_hierarchy_node_color_6{background-color: #db8865;}.o_hierarchy_node_container .o_hierarchy_node .o_hierarchy_node_color_7{background-color: #41a9a2;}.o_hierarchy_node_container .o_hierarchy_node .o_hierarchy_node_color_8{background-color: #304be0;}.o_hierarchy_node_container .o_hierarchy_node .o_hierarchy_node_color_9{background-color: #ee2f8a;}.o_hierarchy_node_container .o_hierarchy_node .o_hierarchy_node_color_10{background-color: #61c36e;}.o_hierarchy_node_container .o_hierarchy_node .o_hierarchy_node_color_11{background-color: #9872e6;}.o_hierarchy_node_container .o_hierarchy_node_button{grid-template-columns: 50px 1fr 50px;}

/* /web_hierarchy/static/src/hierarchy_renderer.scss */
 .o_hierarchy_renderer .o_hierarchy_container{gap: 25px;}.o_hierarchy_renderer .o_hierarchy_container .o_hierarchy_row.o_hierarchy_hover{background-color: #e6f2f3; box-shadow: -1px -1px 0px 0px #017e84 inset, 1px 1px 0px 0px #017e84 inset;}.o_hierarchy_renderer .o_hierarchy_container .o_hierarchy_row:has(.o_hierarchy_node_unfolded) .o_hierarchy_node_container:not(.o_hierarchy_node_unfolded){opacity: 50%;}.o_hierarchy_renderer .o_hierarchy_container .o_hierarchy_separator{position: relative; margin-top: 10px; margin-bottom: 10px; width: 100%;}.o_hierarchy_renderer .o_hierarchy_container .o_hierarchy_separator .o_hierarchy_line_left:before{border-top: 2px solid #ababb1; border-left: 2px solid #ababb1; border-top-left-radius: 8px; margin-left: -10%; margin-top: -2px;}.o_hierarchy_renderer .o_hierarchy_container .o_hierarchy_separator .o_hierarchy_line_left:after{border-bottom: 2px solid #ababb1; border-right: 2px solid #ababb1; border-bottom-right-radius: 6px; margin-left: 30%; margin-top: -12px;}.o_hierarchy_renderer .o_hierarchy_container .o_hierarchy_separator .o_hierarchy_line_right:before{border-bottom: 2px solid #ababb1; border-left: 2px solid #ababb1; border-bottom-left-radius: 6px; margin-left: calc(-10% - 1px); margin-top: -12px;}.o_hierarchy_renderer .o_hierarchy_container .o_hierarchy_separator .o_hierarchy_line_right:after{border-top: 2px solid #ababb1; border-right: 2px solid #ababb1; border-top-right-radius: 8px; margin-left: 30%; margin-top: -2px;}.o_hierarchy_renderer .o_hierarchy_container .o_hierarchy_separator .o_hierarchy_line_part{border-top: 2px solid #ababb1; margin: 0 10%; width: 30%;}.o_hierarchy_renderer .o_hierarchy_container .o_hierarchy_separator .o_hierarchy_line_part:before, .o_hierarchy_renderer .o_hierarchy_container .o_hierarchy_separator .o_hierarchy_line_part:after{content: ""; width: calc(10% + 1px); height: 12px; position: absolute; display: block;}

/* /hr_org_chart/static/src/views/hr_employee_hierarchy/hr_employee_hierarchy_renderer.scss */
 .o_hierarchy_renderer .o_hierarchy_parent_node_container .o_avatar > span{width: 100% !important;}
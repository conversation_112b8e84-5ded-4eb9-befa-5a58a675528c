#!/usr/bin/env python3
"""
Script kiểm tra tình trạng hệ thống Odoo
Sử dụng: python check_system_health.py
"""

import requests
import time
import sys

def check_odoo_health():
    """Kiểm tra tình trạng Odoo server"""
    base_url = "http://localhost:8069"
    
    print("=== KIỂM TRA TÌNH TRẠNG HỆ THỐNG ODOO ===\n")
    
    # 1. Kiểm tra server có chạy không
    try:
        response = requests.get(f"{base_url}/web/database/selector", timeout=10)
        if response.status_code == 200:
            print("✅ Odoo server đang chạy")
        else:
            print(f"⚠️  Odoo server trả về status code: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ Không thể kết nối đến Odoo server")
        return False
    except Exception as e:
        print(f"❌ Lỗi khi kiểm tra server: {e}")
        return False
    
    # 2. Ki<PERSON>m tra web client
    try:
        response = requests.post(f"{base_url}/web/webclient/version_info", 
                               json={}, timeout=10)
        if response.status_code == 200:
            print("✅ Web client hoạt động bình thường")
        else:
            print(f"⚠️  Web client trả về status code: {response.status_code}")
    except Exception as e:
        print(f"⚠️  Lỗi khi kiểm tra web client: {e}")
    
    # 3. Kiểm tra database connection
    try:
        response = requests.get(f"{base_url}/web/database/list", timeout=10)
        if response.status_code == 200:
            print("✅ Database connection hoạt động")
        else:
            print(f"⚠️  Database connection có vấn đề: {response.status_code}")
    except Exception as e:
        print(f"⚠️  Lỗi khi kiểm tra database: {e}")
    
    return True

def check_tiktok_modules():
    """Kiểm tra module TikTok Shop integration"""
    print("\n=== KIỂM TRA MODULE TIKTOK SHOP INTEGRATION ===\n")
    
    # Kiểm tra file tồn tại
    import os
    
    modules_to_check = [
        "server/odoo/addons/ket_noi_tmdt",
        "server/odoo/addons/ket_noi_tmdt_sale_order"
    ]
    
    for module_path in modules_to_check:
        if os.path.exists(module_path):
            print(f"✅ Module {os.path.basename(module_path)} tồn tại")
            
            # Kiểm tra manifest file
            manifest_path = os.path.join(module_path, "__manifest__.py")
            if os.path.exists(manifest_path):
                print(f"✅ Manifest file của {os.path.basename(module_path)} OK")
            else:
                print(f"❌ Thiếu manifest file cho {os.path.basename(module_path)}")
        else:
            print(f"❌ Module {os.path.basename(module_path)} không tồn tại")

def main():
    """Hàm chính"""
    print("Bắt đầu kiểm tra tình trạng hệ thống...\n")
    
    # Kiểm tra Odoo health
    if check_odoo_health():
        # Kiểm tra TikTok modules
        check_tiktok_modules()
        
        print("\n=== KẾT LUẬN ===")
        print("Hệ thống đã được kiểm tra. Xem kết quả ở trên.")
        print("Nếu có lỗi, vui lòng thực hiện các bước khắc phục được đề xuất.")
    else:
        print("\n❌ Không thể kiểm tra đầy đủ do server không hoạt động")

if __name__ == "__main__":
    main()

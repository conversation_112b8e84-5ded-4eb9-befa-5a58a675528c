# TikTok Shop Connector cho Odoo

Module Odoo để kết nối và đồng bộ dữ liệu với TikTok Shop.

## 🚀 Tính năng chính

### 🔗 Kết nối API
- Kết nối với TikTok Shop API v2
- Hỗ trợ môi trường sandbox và production
- <PERSON><PERSON><PERSON> thực bảo mật với App Key/Secret và Access Token

### 📦 Quản lý đơn hàng
- Đồng bộ đơn hàng từ TikTok Shop về Odoo
- Tự động tạo Sale Orders trong Odoo
- Cập nhật trạng thái đơn hàng lên TikTok Shop
- Mapping khách hàng và địa chỉ giao hàng

### 🛍️ Quản lý sản phẩm
- Đồng bộ sản phẩm và SKU từ TikTok Shop
- Tự động tạo sản phẩm mới trong Odoo
- Cập nhật tồn kho lên TikTok Shop
- Quản lý biến thể sản phẩm

### ⚡ Đồng bộ tự động
- Cron jobs tự động đồng bộ theo định kỳ
- Cấu hình khoảng thời gian đồng bộ
- Xử lý lỗi và thông báo

## 📋 Cài đặt

### Yêu cầu hệ thống
- Odoo 18.0+
- Python package: `requests`

### Cài đặt module
1. Module đã được tạo trong thư mục `addons/tiktok_shop_connector`
2. Restart Odoo server
3. Vào Apps → Update Apps List
4. Tìm "TikTok Shop Connector" và cài đặt

## ⚙️ Cấu hình

### 1. Tạo App trên TikTok Developer Portal
1. Truy cập [TikTok Developer Portal](https://partner.tiktokshop.com/developer)
2. Tạo App mới và lấy App Key, App Secret
3. Cấu hình callback URL và permissions

### 2. Cấu hình trong Odoo
1. Vào **TikTok Shop → Cấu hình**
2. Tạo cấu hình mới với thông tin:
   - **Tên Shop**: Tên để nhận biết
   - **Shop ID**: ID của shop trên TikTok
   - **App Key**: App Key từ Developer Portal
   - **App Secret**: App Secret từ Developer Portal
   - **Access Token**: Token để truy cập API
   - **Môi trường**: Chọn Sandbox hoặc Production

3. Nhấn **Kiểm tra kết nối** để xác thực

## 🎯 Sử dụng

### Đồng bộ thủ công
1. Vào **TikTok Shop → Đồng bộ**
2. Chọn loại đồng bộ và cấu hình
3. Nhấn **Bắt đầu đồng bộ**

### Quản lý đơn hàng
1. Vào **TikTok Shop → Đơn hàng**
2. Xem và đồng bộ đơn hàng

### Quản lý sản phẩm
1. Vào **TikTok Shop → Sản phẩm**
2. Xem và đồng bộ sản phẩm

## 🔧 Cấu trúc Module

```
tiktok_shop_connector/
├── __manifest__.py
├── __init__.py
├── models/
│   ├── tiktok_shop_config.py
│   ├── tiktok_shop_order.py
│   ├── tiktok_shop_product.py
│   ├── tiktok_shop_api.py
│   ├── sale_order.py
│   └── product_template.py
├── views/
├── wizard/
├── security/
├── data/
└── demo/
```

## 📞 Hỗ trợ

- **Email**: <EMAIL>
- **Documentation**: Module README
- **Version**: 1.0.0

## 📄 License

LGPL-3

---

**Module đã được tạo thành công trong Odoo! 🎉**

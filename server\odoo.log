2025-06-25 17:35:15,257 39552 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-25 17:35:15,503 39552 INFO ? odoo.modules.loading: loading 1 modules... 
2025-06-25 17:35:15,513 39552 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-25 17:35:15,546 39552 WARNING ? odoo.modules.graph: module jazzy_backend_theme: not installable, skipped 
2025-06-25 17:35:15,721 39552 INFO ? odoo.modules.loading: loading 174 modules... 
2025-06-25 17:35:18,958 39552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:35:18] "POST /web/webclient/version_info HTTP/1.1" 500 - 0 0.000 0.274
2025-06-25 17:35:19,368 39552 INFO ? odoo.modules.loading: 174 modules loaded in 3.65s, 0 queries (+0 extra) 
2025-06-25 17:35:19,875 39552 ERROR ? odoo.modules.loading: Some modules are not loaded, some dependencies or manifest may be missing: ['jazzy_backend_theme'] 
2025-06-25 17:35:19,877 39552 INFO ? odoo.modules.loading: Modules loaded. 
2025-06-25 17:35:19,893 39552 INFO ? odoo.modules.registry: Registry loaded in 4.470s 
2025-06-25 17:35:19,899 39552 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-25 17:35:20,011 39552 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:35:20] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 25 0.085 4.506
2025-06-25 17:35:20,024 39552 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:35:20] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 3 0.002 0.007
2025-06-25 17:35:20,477 39552 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-06-25 17:35:27,047 39552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:35:27] "POST /web/action/load HTTP/1.1" 500 - 0 0.000 0.047
2025-06-25 17:35:32,418 39552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:35:32] "POST /web/webclient/version_info HTTP/1.1" 500 - 0 0.000 0.038
2025-06-25 17:35:52,731 39552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:35:52] "POST /web/webclient/version_info HTTP/1.1" 500 - 0 0.000 0.049
2025-06-25 17:36:22,419 39552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:36:22] "POST /web/webclient/version_info HTTP/1.1" 500 - 0 0.000 0.042
2025-06-25 17:37:07,728 39552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:37:07] "POST /web/webclient/version_info HTTP/1.1" 500 - 0 0.000 0.025
2025-06-25 17:38:14,731 39552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:38:14] "POST /web/webclient/version_info HTTP/1.1" 500 - 0 0.000 0.027
2025-06-25 17:39:57,113 32552 INFO ? odoo: Odoo version 18.0-******** 
2025-06-25 17:39:57,113 32552 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-25 17:39:57,113 32552 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-25 17:39:57,113 32552 INFO ? odoo: database: openpg@localhost:5432 
2025-06-25 17:39:57,297 32552 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-25 17:39:57,317 32552 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-25 17:39:57,777 32552 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-25 17:39:58,031 32552 INFO ? odoo.modules.loading: loading 1 modules... 
2025-06-25 17:39:58,038 32552 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-25 17:39:58,062 32552 WARNING ? odoo.modules.graph: module jazzy_backend_theme: not installable, skipped 
2025-06-25 17:39:58,175 32552 INFO ? odoo.modules.loading: loading 174 modules... 
2025-06-25 17:40:01,023 32552 INFO ? odoo.modules.loading: 174 modules loaded in 2.85s, 0 queries (+0 extra) 
2025-06-25 17:40:01,295 32552 ERROR ? odoo.modules.loading: Some modules are not loaded, some dependencies or manifest may be missing: ['jazzy_backend_theme'] 
2025-06-25 17:40:01,296 32552 INFO ? odoo.modules.loading: Modules loaded. 
2025-06-25 17:40:01,306 32552 INFO ? odoo.modules.registry: Registry loaded in 3.336s 
2025-06-25 17:40:01,312 32552 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-25 17:40:01,390 32552 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:40:01] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 25 0.066 3.356
2025-06-25 17:40:01,405 32552 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:40:01] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 3 0.004 0.007
2025-06-25 17:40:01,916 32552 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-06-25 17:40:05,460 32388 INFO ? odoo: Odoo version 18.0-******** 
2025-06-25 17:40:05,461 32388 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-25 17:40:05,461 32388 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-25 17:40:05,461 32388 INFO ? odoo: database: openpg@localhost:5432 
2025-06-25 17:40:05,695 32388 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-25 17:40:05,738 32388 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-25 17:40:06,178 32388 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-25 17:40:58,399 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:40:58] "GET / HTTP/1.1" 303 - 1 0.006 0.086
2025-06-25 17:40:58,582 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:40:58] "GET /odoo HTTP/1.1" 303 - 2 0.015 0.148
2025-06-25 17:41:01,424 32552 INFO ? odoo.sql_db: ConnectionPool(read/write;used=3/count=6/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=demo host=localhost port=5432 application_name=odoo-32552 sslmode=prefer' 
2025-06-25 17:41:01,425 32552 INFO ? odoo.sql_db: ConnectionPool(read/write;used=3/count=5/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=your_database_name host=localhost port=5432 application_name=odoo-32552 sslmode=prefer' 
2025-06-25 17:41:01,631 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:01] "GET /web/database/selector HTTP/1.1" 200 - 8 0.063 2.976
2025-06-25 17:41:02,397 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:02] "GET /web/static/lib/bootstrap/js/dist/dom/manipulator.js HTTP/1.1" 200 - 1 0.006 0.391
2025-06-25 17:41:02,398 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:02] "GET /web/static/src/libs/fontawesome/css/font-awesome.css HTTP/1.1" 200 - 1 0.006 0.405
2025-06-25 17:41:02,400 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:02] "GET /web/static/lib/bootstrap/dist/css/bootstrap.css HTTP/1.1" 200 - 1 0.005 0.405
2025-06-25 17:41:02,403 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:02] "GET /web/static/lib/bootstrap/js/dist/dom/data.js HTTP/1.1" 200 - 1 0.005 0.410
2025-06-25 17:41:02,423 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:02] "GET /web/static/lib/bootstrap/js/dist/dom/event-handler.js HTTP/1.1" 200 - 1 0.005 0.419
2025-06-25 17:41:02,425 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:02] "GET /web/static/lib/bootstrap/js/dist/util/index.js HTTP/1.1" 200 - 1 0.004 0.432
2025-06-25 17:41:03,183 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:03] "GET /web/static/lib/bootstrap/js/dist/util/component-functions.js HTTP/1.1" 200 - 1 0.006 0.461
2025-06-25 17:41:03,185 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:03] "GET /web/static/lib/bootstrap/js/dist/util/config.js HTTP/1.1" 200 - 1 0.005 0.462
2025-06-25 17:41:03,186 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:03] "GET /web/static/lib/bootstrap/js/dist/util/scrollbar.js HTTP/1.1" 200 - 1 0.005 0.435
2025-06-25 17:41:03,187 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:03] "GET /web/static/lib/bootstrap/js/dist/dom/selector-engine.js HTTP/1.1" 200 - 1 0.004 0.468
2025-06-25 17:41:03,188 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:03] "GET /web/static/lib/bootstrap/js/dist/util/focustrap.js HTTP/1.1" 200 - 1 0.007 0.437
2025-06-25 17:41:03,195 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:03] "GET /web/static/lib/bootstrap/js/dist/util/backdrop.js HTTP/1.1" 200 - 1 0.005 0.469
2025-06-25 17:41:03,975 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:03] "GET /web/static/src/public/database_manager.js HTTP/1.1" 200 - 1 0.006 0.466
2025-06-25 17:41:03,985 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:03] "GET /web/static/lib/bootstrap/js/dist/base-component.js HTTP/1.1" 200 - 1 0.008 0.491
2025-06-25 17:41:04,007 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:04] "GET /web/static/lib/bootstrap/js/dist/modal.js HTTP/1.1" 200 - 1 0.005 0.499
2025-06-25 17:41:04,383 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:04] "GET /web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0 HTTP/1.1" 200 - 1 0.005 0.085
2025-06-25 17:41:04,442 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:04] "GET /web/static/img/logo2.png HTTP/1.1" 200 - 1 0.005 0.062
2025-06-25 17:41:04,860 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:04] "GET /web/static/img/favicon.ico HTTP/1.1" 200 - 1 0.007 0.097
2025-06-25 17:41:10,032 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:10] "GET /odoo?db=ecomplus HTTP/1.1" 302 - 1 0.004 0.088
2025-06-25 17:41:10,270 32552 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:10] "GET /odoo?db=ecomplus HTTP/1.1" 303 - 6 0.010 0.014
2025-06-25 17:41:13,977 32552 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:13] "GET /web/login?redirect=/odoo?db%3Decomplus HTTP/1.1" 200 - 170 0.241 3.149
2025-06-25 17:41:14,058 32552 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:14] "GET /web/assets/1/74b47e3/web.assets_frontend.min.css HTTP/1.1" 200 - 7 0.010 0.031
2025-06-25 17:41:14,476 32552 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:14] "GET /web/assets/1/bbc4610/web.assets_frontend_minimal.min.js HTTP/1.1" 200 - 7 0.009 0.088
2025-06-25 17:41:15,816 32552 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:15] "GET /web/image/website/1/logo/My%20Website?unique=d6bf3d7 HTTP/1.1" 200 - 8 0.010 0.054
2025-06-25 17:41:16,116 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:16] "GET /web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff2 HTTP/1.1" 200 - 0 0.000 0.054
2025-06-25 17:41:16,174 32552 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:16] "GET /web/static/img/odoo_logo_tiny.png HTTP/1.1" 200 - 0 0.000 0.004
2025-06-25 17:41:17,901 32552 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:17] "GET /web/image/website/1/favicon?unique=d6bf3d7 HTTP/1.1" 200 - 8 0.008 0.121
2025-06-25 17:41:18,747 32552 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:18] "GET /web/assets/1/8d07b31/web.assets_frontend_lazy.min.js HTTP/1.1" 200 - 7 0.008 0.950
2025-06-25 17:41:19,139 32552 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:19] "GET /website/translations/8d0668eb991f20961a3d2eb1a7d3709f8e39eb75?lang=vi_VN HTTP/1.1" 200 - 4 0.004 0.051
2025-06-25 17:41:28,353 32552 INFO ecomplus odoo.addons.base.models.res_users: Login failed for db:ecomplus login:<EMAIL> from 127.0.0.1 
2025-06-25 17:41:28,833 32552 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:28] "POST /web/login HTTP/1.1" 200 - 17 0.059 0.638
2025-06-25 17:41:36,372 33144 INFO ? odoo: Odoo version 18.0-******** 
2025-06-25 17:41:36,372 33144 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-25 17:41:36,372 33144 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-25 17:41:36,372 33144 INFO ? odoo: database: openpg@localhost:5432 
2025-06-25 17:41:36,628 33144 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-25 17:41:36,679 33144 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-25 17:41:38,199 33144 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-25 17:41:38,726 33144 INFO ? odoo.modules.loading: loading 1 modules... 
2025-06-25 17:41:38,736 33144 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-25 17:41:38,774 33144 WARNING ? odoo.modules.graph: module jazzy_backend_theme: not installable, skipped 
2025-06-25 17:41:38,957 33144 INFO ? odoo.modules.loading: loading 174 modules... 
2025-06-25 17:41:42,942 33144 INFO ? odoo.modules.loading: 174 modules loaded in 3.99s, 0 queries (+0 extra) 
2025-06-25 17:41:43,361 33144 ERROR ? odoo.modules.loading: Some modules are not loaded, some dependencies or manifest may be missing: ['jazzy_backend_theme'] 
2025-06-25 17:41:43,363 33144 INFO ? odoo.modules.loading: Modules loaded. 
2025-06-25 17:41:43,375 33144 INFO ? odoo.modules.registry: Registry loaded in 4.746s 
2025-06-25 17:41:43,381 33144 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-25 17:41:43,576 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:43] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 25 0.094 4.856
2025-06-25 17:41:43,646 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:43] "GET /web/service-worker.js HTTP/1.1" 200 - 15 0.086 1.959
2025-06-25 17:41:43,739 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:43] "GET /odoo/offline HTTP/1.1" 200 - 13 0.041 0.031
2025-06-25 17:41:43,922 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:43] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 3 0.003 0.014
2025-06-25 17:41:44,002 33144 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-06-25 17:41:54,244 33144 INFO ecomplus odoo.addons.base.models.res_users: Login successful for db:ecomplus login:admin from 127.0.0.1 
2025-06-25 17:41:54,279 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:41:54] "POST /web/login HTTP/1.1" 303 - 41 0.104 1.974
2025-06-25 17:41:54,322 33144 INFO ecomplus odoo.addons.base.models.res_device: User 2 inserts device log (U4WZSmSVUMrxcKpHaAv_tkIpkUD3h5xz9B4r8Cl9MW) 
2025-06-25 17:42:02,193 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:42:02] "GET /odoo?db=ecomplus HTTP/1.1" 200 - 108 0.181 7.719
2025-06-25 17:42:03,779 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:42:03] "GET /web/webclient/translations/2b7fe25a728be8ce37e4496c0fd51336747aedcc?lang=vi_VN HTTP/1.1" 200 - 1 0.001 1.218
2025-06-25 17:42:04,094 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:42:04] "GET /web/webclient/load_menus/564b7ececa21d83a67698e6b98c1549635d2941b8e3070e5d0b9fa6ea4454fa7 HTTP/1.1" 200 - 218 0.201 1.335
2025-06-25 17:42:19,193 33144 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/35a4e72/web.assets_web.min.css (id:1538) 
2025-06-25 17:42:19,194 33144 INFO ecomplus odoo.addons.base.models.assetsbundle: Deleting attachments [1273] (matching /web/assets/_______/web.assets_web.min.css) because it was replaced with /web/assets/35a4e72/web.assets_web.min.css 
2025-06-25 17:42:19,348 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:42:19] "GET /web/assets/35a4e72/web.assets_web.min.css HTTP/1.1" 200 - 34 0.231 16.887
2025-06-25 17:42:29,995 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:42:29] "POST /web/webclient/version_info HTTP/1.1" 200 - 2 0.011 0.100
2025-06-25 17:42:30,063 33144 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/2584882/web.assets_web_print.min.css (id:1539) 
2025-06-25 17:42:30,065 33144 INFO ecomplus odoo.addons.base.models.assetsbundle: Deleting attachments [1274] (matching /web/assets/_______/web.assets_web_print.min.css) because it was replaced with /web/assets/2584882/web.assets_web_print.min.css 
2025-06-25 17:42:30,135 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:42:30] "GET /web/assets/2584882/web.assets_web_print.min.css HTTP/1.1" 200 - 27 0.101 10.372
2025-06-25 17:42:58,328 33144 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/9c41a94/web.assets_web.min.js (id:1540) 
2025-06-25 17:42:58,329 33144 INFO ecomplus odoo.addons.base.models.assetsbundle: Deleting attachments [1275] (matching /web/assets/_______/web.assets_web.min.js) because it was replaced with /web/assets/9c41a94/web.assets_web.min.js 
2025-06-25 17:42:59,455 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:42:59] "GET /web/assets/9c41a94/web.assets_web.min.js HTTP/1.1" 200 - 30 0.101 56.812
2025-06-25 17:43:00,295 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:00] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.011 0.008
2025-06-25 17:43:00,334 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:00] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 200 - 4 0.003 0.054
2025-06-25 17:43:00,541 33144 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:00] "GET /web/static/img/odoo-icon-192x192.png HTTP/1.1" 200 - 0 0.000 0.001
2025-06-25 17:43:00,676 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:00] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 1 0.002 0.023
2025-06-25 17:43:00,843 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:00] "POST /mail/data HTTP/1.1" 200 - 43 0.133 0.061
2025-06-25 17:43:01,874 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:01] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.002 0.007
2025-06-25 17:43:02,236 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:02] "GET /odoo/offline HTTP/1.1" 200 - 4 0.005 0.013
2025-06-25 17:43:07,465 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:07] "GET /odoo HTTP/1.1" 200 - 95 0.093 2.148
2025-06-25 17:43:08,287 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:08] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 8 0.008 0.012
2025-06-25 17:43:08,318 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:08] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 9 0.009 0.017
2025-06-25 17:43:08,798 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:08] "POST /web/action/load HTTP/1.1" 200 - 8 0.018 0.011
2025-06-25 17:43:08,870 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:08] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 9 0.028 0.058
2025-06-25 17:43:08,871 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:08] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 10 0.013 0.056
2025-06-25 17:43:08,881 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:08] "POST /mail/data HTTP/1.1" 200 - 26 0.082 0.029
2025-06-25 17:43:08,890 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:08] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.008 0.074
2025-06-25 17:43:08,892 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:08] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 11 0.016 0.070
2025-06-25 17:43:09,246 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:09] "POST /mail/data HTTP/1.1" 200 - 53 0.146 0.067
2025-06-25 17:43:09,382 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:09] "POST /mail/inbox/messages HTTP/1.1" 200 - 7 0.008 0.007
2025-06-25 17:43:11,438 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:11] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.001 0.005
2025-06-25 17:43:11,904 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:11] "POST /web/action/load HTTP/1.1" 200 - 12 0.053 0.013
2025-06-25 17:43:12,542 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:12] "POST /web/dataset/call_kw/pos.config/get_views HTTP/1.1" 200 - 70 0.150 0.151
2025-06-25 17:43:12,647 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:12] "POST /web/dataset/call_kw/pos.config/web_search_read HTTP/1.1" 200 - 21 0.038 0.037
2025-06-25 17:43:12,921 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:12] "POST /web/dataset/call_kw/pos.config/get_pos_kanban_view_state HTTP/1.1" 200 - 13 0.013 0.018
2025-06-25 17:43:13,440 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:13] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 10 0.008 0.024
2025-06-25 17:43:16,346 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:16] "POST /web/action/load HTTP/1.1" 200 - 8 0.009 0.007
2025-06-25 17:43:16,742 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:16] "POST /web/dataset/call_kw/website/get_current_website HTTP/1.1" 200 - 4 0.014 0.026
2025-06-25 17:43:16,743 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:16] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.007 0.030
2025-06-25 17:43:16,745 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:16] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.011 0.030
2025-06-25 17:43:16,747 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:16] "POST /web/dataset/call_kw/website/search_read HTTP/1.1" 200 - 5 0.015 0.029
2025-06-25 17:43:16,753 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:16] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.016 0.028
2025-06-25 17:43:16,968 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:16] "GET /website/force/1?path=/ HTTP/1.1" 303 - 5 0.003 0.007
2025-06-25 17:43:18,732 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:18] "GET /website/iframefallback HTTP/1.1" 200 - 50 0.054 1.566
2025-06-25 17:43:20,309 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:20] "GET / HTTP/1.1" 200 - 507 0.550 2.643
2025-06-25 17:43:21,318 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:21] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 24 0.022 0.336
2025-06-25 17:43:38,319 33144 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) starting 
2025-06-25 17:43:38,334 33144 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) done in 0.015s 
2025-06-25 17:43:38,338 33144 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) processed 0 records, 0 records remaining 
2025-06-25 17:43:38,344 33144 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) completed 
2025-06-25 17:43:50,224 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:50] "POST /web/action/load HTTP/1.1" 200 - 11 0.057 0.023
2025-06-25 17:43:50,567 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:50] "POST /web/dataset/call_kw/website.visitor/get_views HTTP/1.1" 200 - 36 0.095 0.081
2025-06-25 17:43:51,326 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:51] "POST /web/dataset/call_kw/website.visitor/web_search_read HTTP/1.1" 200 - 24 0.158 0.582
2025-06-25 17:43:51,814 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:51] "GET /web/image/res.partner/3/image_128?unique=1750739424000 HTTP/1.1" 200 - 7 0.007 0.018
2025-06-25 17:43:51,814 33144 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:43:51] "GET /base/static/img/avatar_grey.png HTTP/1.1" 200 - 0 0.000 0.042
2025-06-25 17:44:29,969 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:29] "POST /web/dataset/call_button/website.visitor/action_send_chat_request HTTP/1.1" 200 - 49 0.075 0.131
2025-06-25 17:44:30,340 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:30] "POST /discuss/channel/fold HTTP/1.1" 200 - 9 0.013 0.017
2025-06-25 17:44:30,359 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:30] "POST /web/dataset/call_kw/website.visitor/web_search_read HTTP/1.1" 200 - 19 0.033 0.033
2025-06-25 17:44:30,365 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:30] "POST /web/dataset/call_kw/hr.employee/search_read HTTP/1.1" 200 - 5 0.018 0.019
2025-06-25 17:44:30,378 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:30] "POST /discuss/channel/fold HTTP/1.1" 200 - 4 0.007 0.011
2025-06-25 17:44:30,394 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:30] "POST /web/dataset/call_kw/hr.employee/search_read HTTP/1.1" 200 - 3 0.007 0.016
2025-06-25 17:44:30,427 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:30] "POST /discuss/channel/messages HTTP/1.1" 200 - 6 0.015 0.012
2025-06-25 17:44:30,557 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:30] "GET /web/image/res.partner/3/image_128?unique=1750873469000 HTTP/1.1" 200 - 7 0.004 0.014
2025-06-25 17:44:30,760 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:30] "GET /web/image/res.partner/3/avatar_128?unique=1750873469000 HTTP/1.1" 200 - 9 0.011 0.022
2025-06-25 17:44:32,520 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:32] "POST /discuss/channel/fold HTTP/1.1" 200 - 13 0.008 0.067
2025-06-25 17:44:32,792 33144 INFO ecomplus odoo.models.unlink: User #2 deleted discuss.channel records with IDs: [5] 
2025-06-25 17:44:32,875 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:32] "POST /web/dataset/call_kw/discuss.channel/channel_pin HTTP/1.1" 200 - 27 0.037 0.090
2025-06-25 17:44:33,561 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:33] "POST /web/dataset/call_kw/website.visitor/web_read HTTP/1.1" 200 - 22 0.020 0.023
2025-06-25 17:44:43,521 33144 INFO ? odoo.sql_db: ConnectionPool(read/write;used=3/count=10/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=demo host=localhost port=5432 application_name=odoo-33144 sslmode=prefer' 
2025-06-25 17:44:43,522 33144 INFO ? odoo.sql_db: ConnectionPool(read/write;used=3/count=9/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=your_database_name host=localhost port=5432 application_name=odoo-33144 sslmode=prefer' 
2025-06-25 17:44:43,651 33144 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:43] "GET /web/database/selector HTTP/1.1" 200 - 8 0.042 1.866
2025-06-25 17:44:43,904 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:43] "POST /web/action/load HTTP/1.1" 200 - 14 0.023 0.015
2025-06-25 17:44:44,268 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:44] "POST /web/action/run HTTP/1.1" 200 - 13 0.018 0.015
2025-06-25 17:44:44,487 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:44] "POST /website/fetch_dashboard_data HTTP/1.1" 200 - 6 0.003 0.008
2025-06-25 17:44:44,632 33144 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:44] "GET /website/static/src/img/website_dashboard_visit_demo.png HTTP/1.1" 200 - 0 0.000 0.032
2025-06-25 17:44:45,804 33144 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:45] "POST /web/webclient/version_info HTTP/1.1" 200 - 1 0.008 0.105
2025-06-25 17:44:48,053 33144 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:48] "GET /web/database/list HTTP/1.1" 400 - 1 0.005 0.178
2025-06-25 17:44:48,461 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:48] "POST /web/dataset/call_kw/website.visitor/web_search_read HTTP/1.1" 200 - 17 0.018 0.027
2025-06-25 17:44:48,851 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:48] "GET /web/image/res.partner/3/image_128?unique=1750873472000 HTTP/1.1" 200 - 7 0.011 0.021
2025-06-25 17:44:50,600 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:50] "POST /web/action/load HTTP/1.1" 200 - 13 0.007 0.016
2025-06-25 17:44:51,113 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:51] "POST /web/dataset/call_kw/sale.report/get_views HTTP/1.1" 200 - 39 0.084 0.075
2025-06-25 17:44:51,401 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:51] "GET /web/bundle/web.assets_backend_lazy?lang=vi_VN&debug=1 HTTP/1.1" 200 - 18 0.016 0.165
2025-06-25 17:44:52,531 33144 INFO ecomplus odoo.addons.base.models.assetsbundle: Found a similar attachment for /web/assets/1/8849443/web.assets_backend_lazy.min.js, copying from /web/assets/8849443/web.assets_backend_lazy.min.js 
2025-06-25 17:44:52,538 33144 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1/4b7edda/web.assets_backend_lazy.min.css (id:1541) 
2025-06-25 17:44:52,555 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:52] "GET /web/assets/1/4b7edda/web.assets_backend_lazy.min.css HTTP/1.1" 200 - 17 0.015 1.103
2025-06-25 17:44:52,599 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:52] "GET /web/assets/1/8849443/web.assets_backend_lazy.min.js HTTP/1.1" 200 - 12 0.806 0.074
2025-06-25 17:44:52,701 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:52] "POST /web/dataset/call_kw/sale.report/read_group HTTP/1.1" 200 - 11 0.068 0.012
2025-06-25 17:44:59,341 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:59] "POST /web/action/load HTTP/1.1" 200 - 11 0.013 0.024
2025-06-25 17:44:59,718 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:59] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 54 0.079 0.087
2025-06-25 17:44:59,740 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:44:59] "POST /web/dataset/call_kw/ir.module.category/read HTTP/1.1" 200 - 5 0.005 0.007
2025-06-25 17:45:00,135 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:00] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 5 0.005 0.081
2025-06-25 17:45:00,308 33144 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:00] "GET /website_crm/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 17:45:00,617 33144 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:00] "GET /website_membership/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.013
2025-06-25 17:45:00,620 33144 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:00] "GET /website_sale_wishlist/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.013
2025-06-25 17:45:00,624 33144 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:00] "GET /website_forum/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.019
2025-06-25 17:45:00,629 33144 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:00] "GET /website_sale_comparison/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.018
2025-06-25 17:45:00,649 33144 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:00] "GET /website_sale_stock/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.024
2025-06-25 17:45:00,652 33144 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:00] "GET /website_project/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.019
2025-06-25 17:45:00,931 33144 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:00] "GET /website_sale_loyalty/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 17:45:00,952 33144 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:00] "GET /website_sale_collect/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.015
2025-06-25 17:45:00,955 33144 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:00] "GET /website_blog/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.017
2025-06-25 17:45:00,967 33144 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:00] "GET /website_crm_partner_assign/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.020
2025-06-25 17:45:00,981 33144 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:00] "GET /website_customer/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.016
2025-06-25 17:45:00,983 33144 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:00] "GET /website_event_sale/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.012
2025-06-25 17:45:01,252 33144 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:01] "GET /website_google_map/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-06-25 17:45:01,269 33144 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:01] "GET /website_links/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-06-25 17:45:01,272 33144 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:01] "GET /website_mass_mailing/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.004
2025-06-25 17:45:28,321 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:28] "POST /web/action/load HTTP/1.1" 200 - 11 0.011 0.021
2025-06-25 17:45:28,834 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:28] "POST /web/dataset/call_kw/im_livechat.channel/get_views HTTP/1.1" 200 - 81 0.142 0.141
2025-06-25 17:45:28,885 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:28] "POST /web/dataset/call_kw/im_livechat.channel/web_search_read HTTP/1.1" 200 - 14 0.015 0.026
2025-06-25 17:45:29,290 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:29] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 10 0.013 0.028
2025-06-25 17:45:30,568 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:30] "POST /web/action/load HTTP/1.1" 200 - 14 0.032 0.154
2025-06-25 17:45:31,093 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:31] "POST /web/dataset/call_kw/discuss.channel/get_views HTTP/1.1" 200 - 34 0.079 0.084
2025-06-25 17:45:31,213 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:31] "POST /web/dataset/call_kw/discuss.channel/web_search_read HTTP/1.1" 200 - 4 0.003 0.016
2025-06-25 17:45:31,470 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:31] "GET /web/image/res.partner/2/avatar_128 HTTP/1.1" 200 - 8 0.012 0.040
2025-06-25 17:45:31,821 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:31] "GET /web/image/res.partner/3/avatar_128 HTTP/1.1" 200 - 9 0.070 0.031
2025-06-25 17:45:31,834 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:31] "GET /web/image/res.partner/4/avatar_128 HTTP/1.1" 200 - 7 0.018 0.097
2025-06-25 17:45:31,861 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:45:31] "GET /web/image/res.partner/5/avatar_128 HTTP/1.1" 200 - 7 0.099 0.040
2025-06-25 17:46:10,748 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:46:10] "POST /web/action/load HTTP/1.1" 200 - 12 0.007 0.012
2025-06-25 17:46:11,114 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:46:11] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 7 0.002 0.019
2025-06-25 17:46:11,344 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:46:11] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 3 0.002 0.006
2025-06-25 17:46:11,519 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:46:11] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 62 0.048 0.037
2025-06-25 17:46:11,559 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:46:11] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 5 0.007 0.118
2025-06-25 17:46:13,643 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:46:13] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 62 0.044 0.048
2025-06-25 17:46:14,039 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:46:14] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 6 0.010 0.061
2025-06-25 17:46:15,616 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:46:15] "POST /web/action/load HTTP/1.1" 200 - 11 0.007 0.011
2025-06-25 17:46:15,999 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:46:15] "POST /web/dataset/call_kw/base.module.update/get_views HTTP/1.1" 200 - 14 0.008 0.018
2025-06-25 17:46:16,240 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:46:16] "POST /web/dataset/call_kw/base.module.update/onchange HTTP/1.1" 200 - 4 0.002 0.005
2025-06-25 17:46:17,522 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:46:17] "POST /web/dataset/call_kw/base.module.update/web_save HTTP/1.1" 200 - 7 0.007 0.008
2025-06-25 17:46:17,844 33144 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user admin #2 via 127.0.0.1 
2025-06-25 17:46:19,415 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:46:19] "POST /web/dataset/call_button/base.module.update/update_module HTTP/1.1" 200 - 1633 0.945 0.634
2025-06-25 17:46:22,278 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:46:22] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 62 0.057 0.059
2025-06-25 17:46:22,620 33144 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:46:22] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.016 0.011
2025-06-25 17:47:08,779 33144 INFO ? odoo.service.server: Initiating shutdown 
2025-06-25 17:47:08,780 33144 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-06-25 17:47:09,314 33144 ERROR ecomplus odoo.sql_db: bad query: b' UPDATE "bus_presence"\n                    SET "status" = "__tmp"."status"::VARCHAR\n                    FROM (VALUES (2, \'offline\')) AS "__tmp"("id", "status")\n                    WHERE "bus_presence"."id" = "__tmp"."id"\n                '
ERROR: could not serialize access due to concurrent update
 
2025-06-25 17:47:09,361 33144 ERROR ecomplus odoo.addons.bus.websocket: could not serialize access due to concurrent update
 
Traceback (most recent call last):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\bus\websocket.py", line 318, in get_messages
    message = self._process_next_message()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\bus\websocket.py", line 464, in _process_next_message
    self._handle_control_frame(frame)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\bus\websocket.py", line 602, in _handle_control_frame
    self._terminate()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\bus\websocket.py", line 583, in _terminate
    with acquire_cursor(self._db) as cr:
  File "C:\Program Files\Odoo 18.0.********\server\odoo\sql_db.py", line 201, in __exit__
    self.commit()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\sql_db.py", line 479, in commit
    self.flush()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\sql_db.py", line 159, in flush
    self.transaction.flush()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 1022, in flush
    env_to_flush.flush_all()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 849, in flush_all
    self[model_name].flush_model()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\models.py", line 6752, in flush_model
    self._flush(fnames)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\models.py", line 6830, in _flush
    model.browse(some_ids)._write_multi(vals_list)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\models.py", line 4916, in _write_multi
    self.env.execute_query(SQL(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 978, in execute_query
    self.cr.execute(query)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\sql_db.py", line 354, in execute
    res = self._obj.execute(query, params)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
psycopg2.errors.SerializationFailure: could not serialize access due to concurrent update

2025-06-25 17:47:09,852 33144 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=0/max=64): Closed 8 connections  
2025-06-25 17:47:14,536 4204 INFO ? odoo: Odoo version 18.0-******** 
2025-06-25 17:47:14,537 4204 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-25 17:47:14,537 4204 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-25 17:47:14,537 4204 INFO ? odoo: database: openpg@localhost:5432 
2025-06-25 17:47:14,842 4204 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-25 17:47:14,869 4204 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-25 17:47:15,466 4204 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-25 17:47:16,845 4204 INFO ? odoo.modules.loading: loading 1 modules... 
2025-06-25 17:47:16,854 4204 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-25 17:47:16,878 4204 WARNING ? odoo.modules.graph: module jazzy_backend_theme: not installable, skipped 
2025-06-25 17:47:16,900 4204 INFO ? odoo.modules.loading: loading 174 modules... 
2025-06-25 17:47:20,820 4204 INFO ? odoo.modules.loading: 174 modules loaded in 3.92s, 0 queries (+0 extra) 
2025-06-25 17:47:21,313 4204 ERROR ? odoo.modules.loading: Some modules are not loaded, some dependencies or manifest may be missing: ['jazzy_backend_theme'] 
2025-06-25 17:47:21,314 4204 INFO ? odoo.modules.loading: Modules loaded. 
2025-06-25 17:47:21,329 4204 INFO ? odoo.modules.registry: Registry loaded in 4.572s 
2025-06-25 17:47:21,336 4204 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-25 17:47:21,436 4204 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-25 17:47:21,702 4204 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:47:21] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 8 0.079 3.645
2025-06-25 17:47:21,704 4204 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:47:21] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 7 0.073 3.732
2025-06-25 17:47:21,797 4204 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:47:21] "POST /web/dataset/call_kw/base.module.update/onchange HTTP/1.1" 200 - 28 0.117 5.668
2025-06-25 17:47:21,895 4204 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:47:21] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 75 0.220 2.458
2025-06-25 17:47:22,030 4204 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:47:22] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 3 0.004 0.009
2025-06-25 17:47:22,049 4204 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:47:22] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 2 0.002 0.007
2025-06-25 17:47:22,118 4204 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-06-25 17:47:22,286 4204 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:47:22] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 6 0.010 0.060
2025-06-25 17:47:23,644 4204 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:47:23] "POST /web/dataset/call_kw/base.module.update/web_save HTTP/1.1" 200 - 8 0.008 0.008
2025-06-25 17:47:23,912 4204 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user admin #2 via 127.0.0.1 
2025-06-25 17:47:25,758 4204 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:47:25] "POST /web/dataset/call_button/base.module.update/update_module HTTP/1.1" 200 - 1652 1.101 0.763
2025-06-25 17:47:28,807 4204 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:47:28] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 62 0.040 0.046
2025-06-25 17:47:29,145 4204 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:47:29] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 5 0.014 0.013
2025-06-25 17:47:30,845 4204 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['TikTok Shop Connector'] to user admin #2 via 127.0.0.1 
2025-06-25 17:47:30,845 4204 INFO ecomplus odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-06-25 17:47:30,848 4204 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['TikTok Shop Connector'] to user admin #2 via 127.0.0.1 
2025-06-25 17:47:31,815 4204 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-25 17:47:31,825 4204 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-25 17:47:31,858 4204 INFO ecomplus odoo.modules.loading: updating modules list 
2025-06-25 17:47:31,859 4204 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-06-25 17:47:33,305 4204 WARNING ecomplus odoo.modules.graph: module jazzy_backend_theme: not installable, skipped 
2025-06-25 17:47:33,331 4204 INFO ecomplus odoo.modules.loading: loading 174 modules... 
2025-06-25 17:47:33,490 4204 INFO ecomplus odoo.modules.loading: 174 modules loaded in 0.16s, 0 queries (+0 extra) 
2025-06-25 17:47:33,495 4204 INFO ecomplus odoo.modules.loading: loading 175 modules... 
2025-06-25 17:47:33,496 4204 INFO ecomplus odoo.modules.loading: Loading module tiktok_shop_connector (141/175) 
2025-06-25 17:47:33,949 4204 INFO ecomplus odoo.modules.registry: module tiktok_shop_connector: creating or updating database tables 
2025-06-25 17:47:34,430 4204 INFO ecomplus odoo.modules.loading: loading tiktok_shop_connector/security/ir.model.access.csv 
2025-06-25 17:47:34,451 4204 INFO ecomplus odoo.modules.loading: loading tiktok_shop_connector/views/tiktok_shop_menus.xml 
2025-06-25 17:47:34,499 4204 WARNING ecomplus odoo.modules.loading: Transient module states were reset 
2025-06-25 17:47:34,501 4204 ERROR ecomplus odoo.modules.registry: Failed to load registry 
2025-06-25 17:47:34,508 4204 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-25 17:47:34,520 4204 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-25 17:47:34,528 4204 WARNING ecomplus odoo.modules.graph: module jazzy_backend_theme: not installable, skipped 
2025-06-25 17:47:34,554 4204 INFO ecomplus odoo.modules.loading: loading 174 modules... 
2025-06-25 17:47:34,795 4204 INFO ecomplus odoo.modules.loading: 174 modules loaded in 0.24s, 0 queries (+0 extra) 
2025-06-25 17:47:35,213 4204 ERROR ecomplus odoo.modules.loading: Some modules are not loaded, some dependencies or manifest may be missing: ['jazzy_backend_theme'] 
2025-06-25 17:47:35,215 4204 INFO ecomplus odoo.modules.loading: Modules loaded. 
2025-06-25 17:47:35,225 4204 INFO ecomplus odoo.modules.registry: Registry loaded in 0.724s 
2025-06-25 17:47:35,388 4204 ERROR ecomplus odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.model.data', <function IrModelData._xmlid_lookup at 0x000001DC12B90360>, 'tiktok_shop_connector.action_tiktok_shop_config')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\convert.py", line 537, in _tag_root
    f(rec)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\convert.py", line 293, in _tag_menuitem
    act = self.env.ref(a_action).sudo()
          ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 658, in ref
    res_model, res_id = self['ir.model.data']._xmlid_to_res_model_res_id(
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\base\models\ir_model.py", line 2253, in _xmlid_to_res_model_res_id
    return self._xmlid_lookup(xmlid)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\cache.py", line 110, in lookup
    value = d[key] = self.method(*args, **kwargs)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\base\models\ir_model.py", line 2246, in _xmlid_lookup
    raise ValueError('External ID not found in the system: %s' % xmlid)
ValueError: External ID not found in the system: tiktok_shop_connector.action_tiktok_shop_config

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 2390, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1917, in _serve_db
    return self._transactioning(
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1980, in _transactioning
    return service_model.retrying(func, env=self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\model.py", line 156, in retrying
    result = func()
             ^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1947, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 2195, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\base\models\ir_http.py", line 333, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 744, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\web\controllers\dataset.py", line 42, in call_button
    action = call_kw(request.env[model], method, args, kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 533, in call_kw
    result = getattr(recs, name)(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\base\models\ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\base\models\ir_module.py", line 480, in button_immediate_install
    return self._button_immediate_function(self.env.registry[self._name].button_install)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\base\models\ir_module.py", line 604, in _button_immediate_function
    registry = modules.registry.Registry.new(self._cr.dbname, update_module=True)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 484, in load_modules
    processed_modules += load_marked_modules(env, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 228, in load_module_graph
    load_data(env, idref, mode, kind='data', package=package)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(env, package.name, filename, idref, mode, noupdate, kind)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\convert.py", line 608, in convert_file
    convert_xml_import(env, module, fp, idref, mode, noupdate)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\convert.py", line 679, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\convert.py", line 594, in parse
    self._tag_root(de)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\convert.py", line 550, in _tag_root
    raise ParseError('while parsing %s:%s, somewhere inside\n%s' % (
odoo.tools.convert.ParseError: while parsing file:/c:/program%20files/odoo%2018.0.********/server/odoo/addons/tiktok_shop_connector/views/tiktok_shop_menus.xml:13, somewhere inside
<menuitem id="menu_tiktok_shop_config" name="C&#x1EA5;u h&#xEC;nh" parent="menu_tiktok_shop_root" sequence="10" action="action_tiktok_shop_config"/>
2025-06-25 17:47:35,394 4204 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:47:35] "POST /web/dataset/call_button/ir.module.module/button_immediate_install HTTP/1.1" 200 - 1739 1.252 3.307
2025-06-25 17:48:10,758 4204 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-25 17:48:11,184 4204 INFO ? werkzeug: 127.0.0.1 - - [25/Jun/2025 17:48:11] "GET /web/static/img/default_icon_app.png HTTP/1.1" 200 - 0 0.000 0.031
2025-06-25 17:48:11,203 4204 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:48:11] "POST /web/action/load HTTP/1.1" 200 - 10 0.064 0.037
2025-06-25 17:48:11,257 4204 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:48:11] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 200 - 10 0.048 0.037
2025-06-25 17:48:11,738 4204 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:48:11] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 200 - 30 0.084 0.907
2025-06-25 17:48:11,743 4204 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:48:11] "GET /web/image/res.partner/3/avatar_128?unique=************* HTTP/1.1" 200 - 14 0.569 0.055
2025-06-25 17:48:12,392 4204 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:48:12] "POST /mail/data HTTP/1.1" 200 - 60 0.122 0.070
2025-06-25 17:48:12,543 4204 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:48:12] "POST /mail/inbox/messages HTTP/1.1" 200 - 5 0.008 0.007
2025-06-25 17:48:12,892 4204 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:48:12] "GET /web/image/res.partner/2/avatar_128?unique=1750739344000 HTTP/1.1" 200 - 7 0.019 0.031
2025-06-25 17:48:12,906 4204 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:48:12] "GET /web/image/discuss.channel/2/avatar_128?unique=f28f517b0f5ce61d27fb5fee4d1b97a28d6b2bed03374e1bba8f9355129bd753d50595934507b331fd5b1139f54cb7c2ec4af6f7d600d7a721d18eceff8de0d3 HTTP/1.1" 200 - 9 0.034 0.029
2025-06-25 17:48:12,911 4204 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:48:12] "GET /web/image/res.partner/3/avatar_128?unique=1750739344000 HTTP/1.1" 200 - 8 0.030 0.036
2025-06-25 17:48:12,912 4204 INFO ecomplus werkzeug: 127.0.0.1 - - [25/Jun/2025 17:48:12] "GET /web/image/discuss.channel/1/avatar_128?unique=e9084beedba8c1427377a511725e81a45ecf3524a658a8eb529de70deafdb436229a6c2da6a30d68afc8658735dc1358e81f7e60ace81340637558766b180438 HTTP/1.1" 200 - 9 0.038 0.030

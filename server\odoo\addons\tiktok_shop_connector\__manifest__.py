# -*- coding: utf-8 -*-
{
    'name': 'TikTok Shop Connector',
    'version': '1.0.0',
    'category': 'Sales/Sales',
    'summary': 'Connect and sync orders with TikTok Shop',
    'description': """
TikTok Shop Connector
=====================

This module allows:
* Connect with TikTok Shop API
* Sync orders from TikTok Shop to Odoo
* Update order status to TikTok Shop
* Manage products and inventory
* Auto sync periodically

Main features:
- Configure TikTok Shop API connection
- Auto import orders
- Map products between TikTok Shop and Odoo
- Update order status
- Reports and statistics
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'depends': [
        'base',
        'sale',
        'stock',
        'product',
        'account',
        'contacts',
        'sale_management',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/tiktok_shop_actions.xml',  # Load actions first
        'views/tiktok_shop_menus.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': True,
    'license': 'LGPL-3',
    'external_dependencies': {
        'python': ['requests'],
    },
}

# Hướng dẫn Test và Khắc phục Lỗi Odoo

## Tóm tắt các lỗi đã phát hiện và khắc phục

### 1. Lỗi Module jazzy_backend_theme ✅ ĐÃ KHẮC PHỤC
- **Vấn đề**: Module không tồn tại nhưng hệ thống vẫn cố gắng load
- **Giải pháp**: Đã đổi tên thư mục thành `jazzy_backend_theme_removed`

### 2. Lỗi HTTP 500 ⚠️ CẦN THEO DÕI
- **Vấn đề**: Các request web client trả về lỗi 500
- **Tình trạng**: Đã giảm thiểu sau khi khắc phục lỗi module

### 3. Module TikTok Shop Integration ✅ HOẠT ĐỘNG BÌNH THƯỜNG
- **Tình trạng**: Các module `ket_noi_tmdt` và `ket_noi_tmdt_sale_order` hoạt động tốt
- **Chức năng**: Đ<PERSON><PERSON> đủ các tính năng quản lý shop, sản phẩm, đơn hàng

### 4. Lỗi TikTok Shop Connector Module ✅ ĐÃ KHẮC PHỤC
- **Vấn đề**: External ID not found error cho 'tiktok_shop_connector.action_tiktok_shop_config'
- **Nguyên nhân**: Thứ tự load XML files không đúng, action được tham chiếu trước khi được định nghĩa
- **Giải pháp**: Tách action definitions ra file riêng và load trước menu definitions

### 5. Lỗi View Type 'tree' trong Odoo 18 ✅ ĐÃ KHẮC PHỤC
- **Vấn đề**: ValueError: Wrong value for ir.ui.view.type: 'tree'
- **Nguyên nhân**: Trong Odoo 18, view type 'tree' đã bị deprecated và phải thay bằng 'list'
- **Giải pháp**: Thêm `<field name="type">list</field>` cho tree views và `<field name="type">form</field>` cho form views

### 6. Lỗi Arch Element trong List View Odoo 18 ✅ ĐÃ KHẮC PHỤC
- **Vấn đề**: ParseError: Nút gốc của một chế độ xem list nên là <list>, không phải là <tree>
- **Nguyên nhân**: Khi view có type="list", arch element phải sử dụng `<list>` thay vì `<tree>`
- **Giải pháp**: Thay thế `<tree>` và `</tree>` bằng `<list>` và `</list>` trong arch element

## Các bước test để đảm bảo hệ thống hoạt động ổn định

### Bước 1: Dọn dẹp database (Khuyến nghị)
```bash
# Kết nối PostgreSQL và chạy script
psql -U openpg -d ecomplus -f cleanup_jazzy_module.sql
```

### Bước 2: Khởi động lại Odoo server
```bash
# Dừng tất cả process Python
taskkill /F /IM python.exe

# Khởi động lại server
cd "C:\Program Files\Odoo 18.0.20250519"
python server\odoo-bin -c server\odoo.conf
```

### Bước 3: Kiểm tra log sau khi khởi động
```bash
# Xem log mới nhất
tail -f server\odoo.log

# Tìm kiếm lỗi
findstr /i "error\|warning\|500" server\odoo.log
```

### Bước 4: Test chức năng web interface
1. Truy cập http://localhost:8069
2. Đăng nhập vào hệ thống
3. Kiểm tra các menu chính hoạt động
4. Kiểm tra không có lỗi 500 trong browser console

### Bước 5: Test module TikTok Shop Integration
1. Vào menu **Kết nối TMĐT > Quản lý > Danh sách Shop**
2. Tạo một shop mới
3. Vào menu **Kết nối TMĐT > Quản lý > Liên kết sản phẩm**
4. Tạo liên kết sản phẩm mới
5. Test chức năng tìm kiếm bằng Shopee Model ID trong Sale Order

### Bước 6: Test module TikTok Shop Connector
1. Vào menu **TikTok Shop > Cấu hình**
2. Tạo cấu hình TikTok Shop mới
3. Nhập thông tin API (App Key, App Secret, Access Token)
4. Test kết nối với TikTok Shop API
5. Kiểm tra chức năng đồng bộ đơn hàng

### Bước 7: Chạy script kiểm tra tự động
```bash
python check_system_health.py
```

## Các lỗi có thể gặp và cách khắc phục

### Lỗi: "module jazzy_backend_theme: not installable, skipped"
**Giải pháp**: 
1. Chạy script SQL cleanup_jazzy_module.sql
2. Khởi động lại server

### Lỗi: HTTP 500 trên web client
**Giải pháp**:
1. Kiểm tra log chi tiết: `grep -A 5 -B 5 "500" server/odoo.log`
2. Kiểm tra database connection
3. Restart server với mode debug: `python server\odoo-bin -c server\odoo.conf --log-level=debug`

### Lỗi: Module TikTok Shop không hoạt động
**Giải pháp**:
1. Kiểm tra module đã được install: Apps > Installed > tìm "Kết Nối TMĐT"
2. Nếu chưa install: Apps > Apps > tìm và install module
3. Kiểm tra dependencies: base, product, stock, sale_management

### Lỗi: "External ID not found: tiktok_shop_connector.action_tiktok_shop_config"
**Giải pháp**:
1. Kiểm tra thứ tự load file trong __manifest__.py
2. Đảm bảo action definitions được load trước menu definitions
3. Tách action definitions ra file riêng nếu cần thiết
4. Update module: `python server\odoo-bin -c server\odoo.conf -u tiktok_shop_connector`

### Lỗi: "ValueError: Wrong value for ir.ui.view.type: 'tree'"
**Giải pháp**:
1. Thêm field `type` vào view record definitions
2. Sử dụng `<field name="type">list</field>` cho tree views
3. Sử dụng `<field name="type">form</field>` cho form views
4. ⚠️ **QUAN TRỌNG**: Cũng phải thay đổi arch element (xem lỗi tiếp theo)
5. Update module sau khi sửa: `python server\odoo-bin -c server\odoo.conf -u tiktok_shop_connector`

### Lỗi: "ParseError: Nút gốc của một chế độ xem list nên là <list>, không phải là <tree>"
**Giải pháp**:
1. Khi view có `type="list"`, arch element phải sử dụng `<list>` thay vì `<tree>`
2. Thay thế `<tree string="...">` bằng `<list string="...">`
3. Thay thế `</tree>` bằng `</list>`
4. Giữ nguyên tất cả field definitions và attributes bên trong
5. Update module: `python server\odoo-bin -c server\odoo.conf -u tiktok_shop_connector`

## Monitoring và bảo trì định kỳ

### Hàng ngày:
- Kiểm tra log có lỗi mới: `findstr /i "error" server\odoo.log`
- Kiểm tra dung lượng database
- Backup database

### Hàng tuần:
- Chạy script check_system_health.py
- Kiểm tra performance web interface
- Update module nếu có phiên bản mới

### Hàng tháng:
- Dọn dẹp log cũ
- Kiểm tra và optimize database
- Review và update security settings

## Liên hệ hỗ trợ
Nếu gặp vấn đề không thể tự khắc phục:
1. Thu thập log chi tiết
2. Ghi lại các bước tái tạo lỗi
3. Liên hệ team technical support

# Phân tích và <PERSON>hắc phục Lỗi TikTok Shop Connector Module

## Tổng quan về các lỗi đã khắc phục

### Lỗi 1: External ID not found
```
ValueError: External ID not found in the system: tiktok_shop_connector.action_tiktok_shop_config
```

**Vị trí lỗi**:
- **File**: `/server/odoo/addons/tiktok_shop_connector/views/tiktok_shop_menus.xml`
- **Dòng**: 13 (trong menu item definition)
- **Nguyên nhân**: Menu item tham chiếu đến action trước khi action được định nghĩa

### Lỗi 2: Wrong value for view type
```
ValueError: Wrong value for ir.ui.view.type: 'tree'
```

**Vị trí lỗi**:
- **File**: `/server/odoo/addons/tiktok_shop_connector/views/tiktok_shop_menus.xml`
- **Dòng**: 62 (trong view record definition)
- **<PERSON>uy<PERSON><PERSON> nhân**: Trong Odoo 18, view type 'tree' đã bị deprecated, phải sử dụng 'list'

## Phân tích chi tiết

### Cấu trúc module TikTok Shop Connector
```
tiktok_shop_connector/
├── __manifest__.py
├── models/
│   ├── __init__.py
│   └── tiktok_shop_config.py
├── security/
│   └── ir.model.access.csv
└── views/
    ├── tiktok_shop_actions.xml (mới tạo)
    └── tiktok_shop_menus.xml
```

### Vấn đề về thứ tự load XML
**Trước khi sửa**:
1. Menu item được định nghĩa trước (dòng 13)
2. Action được định nghĩa sau (dòng 16-28)
3. Odoo không thể tìm thấy action khi parse menu item

**Sau khi sửa**:
1. Action được định nghĩa trong file riêng (`tiktok_shop_actions.xml`)
2. File actions được load trước trong manifest
3. Menu item có thể tham chiếu đến action đã tồn tại

## Giải pháp đã thực hiện

### Bước 1: Tạo file actions riêng
**File**: `views/tiktok_shop_actions.xml`
```xml
<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="action_tiktok_shop_config" model="ir.actions.act_window">
        <field name="name">Cấu hình TikTok Shop</field>
        <field name="res_model">tiktok.shop.config</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Tạo cấu hình TikTok Shop đầu tiên!
            </p>
            <p>
                Cấu hình kết nối với TikTok Shop để đồng bộ đơn hàng và sản phẩm.
            </p>
        </field>
    </record>
</odoo>
```

### Bước 2: Cập nhật manifest
**File**: `__manifest__.py`
```python
'data': [
    'security/ir.model.access.csv',
    'views/tiktok_shop_actions.xml',  # Load actions first
    'views/tiktok_shop_menus.xml',
],
```

### Bước 3: Cập nhật file menus
**File**: `views/tiktok_shop_menus.xml`
- Xóa action definition (đã chuyển sang file riêng)
- Giữ lại menu definitions và view definitions

### Bước 4: Sửa view type cho Odoo 18
**File**: `views/tiktok_shop_menus.xml`
```xml
<!-- Form view -->
<record id="view_tiktok_shop_config_form" model="ir.ui.view">
    <field name="name">tiktok.shop.config.form</field>
    <field name="model">tiktok.shop.config</field>
    <field name="type">form</field>  <!-- Thêm field type -->
    <field name="arch" type="xml">
        <!-- form content -->
    </field>
</record>

<!-- List view -->
<record id="view_tiktok_shop_config_tree" model="ir.ui.view">
    <field name="name">tiktok.shop.config.list</field>
    <field name="model">tiktok.shop.config</field>
    <field name="type">list</field>  <!-- Sử dụng 'list' thay vì 'tree' -->
    <field name="arch" type="xml">
        <tree string="Cấu hình TikTok Shop">
            <!-- tree content -->
        </tree>
    </field>
</record>
```

### Bước 5: Thêm dependency
**File**: `__manifest__.py`
```python
'depends': [
    'base',
    'sale',
    'stock',
    'product',
    'account',
    'contacts',
    'sale_management',  # Thêm dependency này
],
```

## Kết quả sau khi khắc phục

### ✅ Thành công
- Module TikTok Shop Connector load thành công
- Không còn lỗi "External ID not found"
- Không còn lỗi "Wrong value for ir.ui.view.type: 'tree'"
- Menu "TikTok Shop" xuất hiện trong interface
- Action "Cấu hình TikTok Shop" hoạt động bình thường
- Tree/List view render đúng theo chuẩn Odoo 18

### 📊 Log kiểm tra
```
2025-06-25 17:58:23,914 INFO odoo.modules.loading: Modules loaded.
2025-06-25 17:58:23,935 INFO odoo.modules.registry: Registry loaded in 7.241s
```
- Không có lỗi TikTok Shop Connector trong log mới
- Server khởi động thành công

## Chức năng của module TikTok Shop Connector

### Model chính: `tiktok.shop.config`
**Các trường chính**:
- `shop_name`: Tên shop TikTok
- `shop_id`: ID shop trên TikTok
- `app_key`: API Key của ứng dụng
- `app_secret`: API Secret của ứng dụng  
- `access_token`: Token truy cập API
- `is_sandbox`: Chế độ sandbox/production
- `is_active`: Trạng thái hoạt động
- `auto_sync_orders`: Tự động đồng bộ đơn hàng
- `connection_status`: Trạng thái kết nối
- `total_orders_synced`: Tổng số đơn hàng đã đồng bộ

### Giao diện người dùng
**Menu structure**:
```
TikTok Shop
└── Cấu hình
```

**Views**:
- **Tree view**: Danh sách các cấu hình shop
- **Form view**: Chi tiết cấu hình với các nhóm:
  - Thông tin cơ bản
  - Cấu hình API
  - Cấu hình đồng bộ
  - Thống kê

**Chức năng**:
- Button "Kiểm tra kết nối" để test API
- Status bar hiển thị trạng thái kết nối
- Toggle để bật/tắt shop

## Khuyến nghị cho tương lai

### 1. Best practices cho module development
- **Luôn định nghĩa actions trước khi sử dụng**
- **Tách actions và menus ra các file riêng**
- **Sử dụng thứ tự load hợp lý trong manifest**

### 2. Testing checklist
- [ ] Module install thành công
- [ ] Không có lỗi External ID not found
- [ ] Menu xuất hiện đúng vị trí
- [ ] Actions hoạt động bình thường
- [ ] Views render đúng

### 3. Monitoring
- Theo dõi log khi install/update module
- Kiểm tra performance khi load nhiều records
- Monitor API calls đến TikTok Shop

## Tài liệu tham khảo
- [Odoo Module Development Guide](https://www.odoo.com/documentation/18.0/developer/reference/backend/module.html)
- [TikTok Shop API Documentation](https://partner.tiktokshop.com/docv2/page/6507ead7b99d5302be949ba9)
- [XML Data Files in Odoo](https://www.odoo.com/documentation/18.0/developer/reference/backend/data.html)
